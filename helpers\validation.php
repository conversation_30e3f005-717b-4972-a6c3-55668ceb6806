<?php
/**
 * Validation Helper Functions
 * Human Resources Center System
 */

class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data = []) {
        $this->data = $data;
    }
    
    /**
     * Validate required field
     */
    public function required($field, $message = null) {
        $message = $message ?: "Field {$field} is required";
        
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field][] = $message;
        }
        
        return $this;
    }
    
    /**
     * Validate email
     */
    public function email($field, $message = null) {
        $message = $message ?: "Field {$field} must be a valid email address";
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate minimum length
     */
    public function minLength($field, $length, $message = null) {
        $message = $message ?: "Field {$field} must be at least {$length} characters long";
        
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $length) {
            $this->errors[$field][] = $message;
        }
        
        return $this;
    }
    
    /**
     * Validate maximum length
     */
    public function maxLength($field, $length, $message = null) {
        $message = $message ?: "Field {$field} must not exceed {$length} characters";
        
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $length) {
            $this->errors[$field][] = $message;
        }
        
        return $this;
    }
    
    /**
     * Validate numeric
     */
    public function numeric($field, $message = null) {
        $message = $message ?: "Field {$field} must be numeric";
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!is_numeric($this->data[$field])) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate integer
     */
    public function integer($field, $message = null) {
        $message = $message ?: "Field {$field} must be an integer";
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_INT)) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate minimum value
     */
    public function min($field, $min, $message = null) {
        $message = $message ?: "Field {$field} must be at least {$min}";
        
        if (isset($this->data[$field]) && is_numeric($this->data[$field])) {
            if ($this->data[$field] < $min) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate maximum value
     */
    public function max($field, $max, $message = null) {
        $message = $message ?: "Field {$field} must not exceed {$max}";
        
        if (isset($this->data[$field]) && is_numeric($this->data[$field])) {
            if ($this->data[$field] > $max) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate date
     */
    public function date($field, $format = 'Y-m-d', $message = null) {
        $message = $message ?: "Field {$field} must be a valid date";
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $date = DateTime::createFromFormat($format, $this->data[$field]);
            if (!$date || $date->format($format) !== $this->data[$field]) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate phone number (Thai format)
     */
    public function phone($field, $message = null) {
        $message = $message ?: "Field {$field} must be a valid phone number";
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $phone = preg_replace('/[^0-9]/', '', $this->data[$field]);
            if (!preg_match('/^(0[689]{1}[0-9]{8}|0[2-7]{1}[0-9]{7})$/', $phone)) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate Thai ID card number
     */
    public function thaiId($field, $message = null) {
        $message = $message ?: "Field {$field} must be a valid Thai ID card number";
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $id = preg_replace('/[^0-9]/', '', $this->data[$field]);
            
            if (strlen($id) !== 13) {
                $this->errors[$field][] = $message;
                return $this;
            }
            
            // Check digit validation
            $sum = 0;
            for ($i = 0; $i < 12; $i++) {
                $sum += (int)$id[$i] * (13 - $i);
            }
            
            $checkDigit = (11 - ($sum % 11)) % 10;
            if ($checkDigit != (int)$id[12]) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate in array
     */
    public function in($field, $array, $message = null) {
        $message = $message ?: "Field {$field} must be one of: " . implode(', ', $array);
        
        if (isset($this->data[$field]) && !in_array($this->data[$field], $array)) {
            $this->errors[$field][] = $message;
        }
        
        return $this;
    }
    
    /**
     * Validate regex pattern
     */
    public function regex($field, $pattern, $message = null) {
        $message = $message ?: "Field {$field} format is invalid";
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!preg_match($pattern, $this->data[$field])) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Validate unique value in database
     */
    public function unique($field, $table, $column = null, $excludeId = null, $message = null) {
        $column = $column ?: $field;
        $message = $message ?: "Field {$field} already exists";
        
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            try {
                $db = new Database();
                $pdo = $db->getConnection();
                
                $sql = "SELECT COUNT(*) FROM {$table} WHERE {$column} = ?";
                $params = [$this->data[$field]];
                
                if ($excludeId) {
                    $sql .= " AND id != ?";
                    $params[] = $excludeId;
                }
                
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                
                if ($stmt->fetchColumn() > 0) {
                    $this->errors[$field][] = $message;
                }
            } catch (Exception $e) {
                error_log("Validation error: " . $e->getMessage());
            }
        }
        
        return $this;
    }
    
    /**
     * Validate file upload
     */
    public function file($field, $maxSize = null, $allowedTypes = null, $message = null) {
        $maxSize = $maxSize ?: MAX_FILE_SIZE;
        $allowedTypes = $allowedTypes ?: ALLOWED_FILE_TYPES;
        
        if (isset($_FILES[$field]) && $_FILES[$field]['error'] !== UPLOAD_ERR_NO_FILE) {
            $file = $_FILES[$field];
            
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $this->errors[$field][] = "File upload error";
                return $this;
            }
            
            if ($file['size'] > $maxSize) {
                $this->errors[$field][] = "File size exceeds maximum allowed size";
            }
            
            $extension = getFileExtension($file['name']);
            if (!in_array($extension, $allowedTypes)) {
                $this->errors[$field][] = "File type not allowed";
            }
        }
        
        return $this;
    }
    
    /**
     * Custom validation
     */
    public function custom($field, $callback, $message = null) {
        $message = $message ?: "Field {$field} is invalid";
        
        if (isset($this->data[$field])) {
            if (!call_user_func($callback, $this->data[$field])) {
                $this->errors[$field][] = $message;
            }
        }
        
        return $this;
    }
    
    /**
     * Check if validation passed
     */
    public function passes() {
        return empty($this->errors);
    }
    
    /**
     * Check if validation failed
     */
    public function fails() {
        return !$this->passes();
    }
    
    /**
     * Get all errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get errors for specific field
     */
    public function getError($field) {
        return $this->errors[$field] ?? [];
    }
    
    /**
     * Get first error for specific field
     */
    public function getFirstError($field) {
        $errors = $this->getError($field);
        return !empty($errors) ? $errors[0] : null;
    }
    
    /**
     * Get all errors as flat array
     */
    public function getAllErrors() {
        $allErrors = [];
        foreach ($this->errors as $fieldErrors) {
            $allErrors = array_merge($allErrors, $fieldErrors);
        }
        return $allErrors;
    }
}

/**
 * Quick validation functions
 */

function validateRequired($value, $fieldName = 'Field') {
    return !empty(trim($value)) ? true : "{$fieldName} is required";
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) ? true : "Invalid email format";
}

function validatePhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^(0[689]{1}[0-9]{8}|0[2-7]{1}[0-9]{7})$/', $phone) ? true : "Invalid phone number";
}

function validateThaiId($id) {
    $id = preg_replace('/[^0-9]/', '', $id);
    
    if (strlen($id) !== 13) {
        return "Thai ID must be 13 digits";
    }
    
    $sum = 0;
    for ($i = 0; $i < 12; $i++) {
        $sum += (int)$id[$i] * (13 - $i);
    }
    
    $checkDigit = (11 - ($sum % 11)) % 10;
    return $checkDigit == (int)$id[12] ? true : "Invalid Thai ID number";
}

function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date ? true : "Invalid date format";
}

function validatePassword($password) {
    $errors = checkPasswordStrength($password);
    return empty($errors) ? true : implode(', ', $errors);
}
