<?php
require_once '../config/config.php';

header('Content-Type: application/json');

$auth = new Auth();
if (!$auth->isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));
$benefitId = isset($pathParts[2]) ? (int)$pathParts[2] : null;

$benefitController = new BenefitController();

try {
    switch ($method) {
        case 'GET':
            if ($benefitId || isset($_GET['id'])) {
                // Get single benefit
                requirePermission('benefits.view');
                $benefitController->getData();
            } else {
                // Get all benefits (for AJAX table)
                requirePermission('benefits.view');
                $benefitModel = new Benefit();
                
                $page = $_GET['page'] ?? 1;
                $search = $_GET['search'] ?? null;
                $type = $_GET['type'] ?? null;
                
                $limit = RECORDS_PER_PAGE;
                $offset = ($page - 1) * $limit;
                
                $benefits = $benefitModel->getAll($limit, $offset, $search, $type);
                $totalRecords = $benefitModel->count($search, $type);
                $pagination = paginate($totalRecords, $page, $limit);
                
                echo json_encode([
                    'success' => true,
                    'data' => $benefits,
                    'pagination' => $pagination
                ]);
            }
            break;
            
        case 'POST':
            requirePermission('benefits.create');
            $_POST['ajax'] = true;
            $benefitController->store();
            break;
            
        case 'PUT':
            requirePermission('benefits.edit');
            
            // Parse PUT data
            $putData = [];
            parse_str(file_get_contents('php://input'), $putData);
            $_POST = array_merge($_POST, $putData);
            $_POST['ajax'] = true;
            
            if ($benefitId || isset($_POST['benefit_id'])) {
                $id = $benefitId ?: $_POST['benefit_id'];
                $benefitController->update($id);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Benefit ID required']);
            }
            break;
            
        case 'DELETE':
            requirePermission('benefits.delete');
            
            $input = json_decode(file_get_contents('php://input'), true);
            $_POST = $input;
            
            $benefitController->delete();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'วิธีการเรียกใช้ไม่ได้รับอนุญาต']);
            break;
    }
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์']);
}
?>
