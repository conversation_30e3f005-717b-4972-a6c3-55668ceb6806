<?php
// Robust employees API v2
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Start output buffering
ob_start();

// Set content type immediately
header('Content-Type: application/json');

// API v2 specific response functions
function apiV2ErrorResponse($message, $code = 500, $debug = null) {
    while (ob_get_level()) {
        ob_end_clean();
    }
    http_response_code($code);
    $response = ['success' => false, 'message' => $message];
    if ($debug) {
        $response['debug_info'] = $debug;
    }
    echo json_encode($response);
    exit;
}

function apiV2SuccessResponse($data, $message = 'Success') {
    while (ob_get_level()) {
        ob_end_clean();
    }
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

try {
    // Basic request info
    $method = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
    $employeeId = $_GET['id'] ?? null;
    
    // Log basic info
    error_log("API v2: Method=$method, ID=$employeeId");
    
    // Check if this is a PUT request
    if ($method !== 'PUT') {
        apiV2ErrorResponse('API v2 รองรับเฉพาะ PUT method เท่านั้น', 405);
    }

    if (!$employeeId) {
        apiV2ErrorResponse('ต้องระบุรหัสพนักงาน', 400);
    }
    
    // Try to load config
    $configPaths = [
        '../config/config.php',
        dirname(__DIR__) . '/config/config.php',
        __DIR__ . '/../config/config.php'
    ];
    
    $configLoaded = false;
    foreach ($configPaths as $path) {
        if (file_exists($path)) {
            try {
                require_once $path;
                $configLoaded = true;
                error_log("API v2: Config loaded from $path");
                break;
            } catch (Exception $e) {
                error_log("API v2: Config error from $path: " . $e->getMessage());
            }
        }
    }
    
    if (!$configLoaded) {
        apiV2ErrorResponse('ไม่สามารถโหลดการตั้งค่าระบบได้', 500, [
            'error' => 'ไม่พบไฟล์การตั้งค่า',
            'paths_checked' => $configPaths
        ]);
    }

    // Check required classes
    if (!class_exists('Auth')) {
        apiV2ErrorResponse('ไม่พบคลาส Auth', 500, ['missing_class' => 'Auth']);
    }

    if (!class_exists('Employee')) {
        apiV2ErrorResponse('ไม่พบคลาส Employee', 500, ['missing_class' => 'Employee']);
    }
    
    // Check authentication
    try {
        $auth = new Auth();
        if (!$auth->isAuthenticated()) {
            apiV2ErrorResponse('ต้องเข้าสู่ระบบก่อน', 401);
        }
        error_log("API v2: Authentication OK");
    } catch (Exception $e) {
        apiV2ErrorResponse('เกิดข้อผิดพลาดในการตรวจสอบสิทธิ์', 500, [
            'error' => $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine()
        ]);
    }
    
    // Parse PUT data
    $putData = [];
    try {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        error_log("API v2: Content-Type: $contentType");
        
        if (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
            $rawInput = file_get_contents('php://input');
            parse_str($rawInput, $putData);
            error_log("API v2: PUT data parsed, fields: " . count($putData));
        } else {
            apiV2ErrorResponse('ประเภทข้อมูลไม่รองรับ', 400, ['content_type' => $contentType]);
        }
    } catch (Exception $e) {
        apiV2ErrorResponse('ไม่สามารถประมวลผลข้อมูล PUT ได้', 400, [
            'error' => $e->getMessage()
        ]);
    }

    // Validate required fields
    if (empty($putData['first_name']) || empty($putData['last_name'])) {
        apiV2ErrorResponse('ต้องระบุชื่อและนามสกุล', 400, [
            'received_fields' => array_keys($putData)
        ]);
    }
    
    // Try to update employee
    try {
        $employeeModel = new Employee();
        error_log("API v2: Employee model created");
        
        $result = $employeeModel->update($employeeId, $putData);
        error_log("API v2: Update result: " . json_encode($result));
        
        if ($result['success']) {
            apiV2SuccessResponse([], $result['message']);
        } else {
            apiV2ErrorResponse($result['message'], 400, [
                'employee_id' => $employeeId,
                'update_error' => true
            ]);
        }

    } catch (Exception $e) {
        apiV2ErrorResponse('ไม่สามารถอัปเดตข้อมูลพนักงานได้', 500, [
            'error' => $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'employee_id' => $employeeId
        ]);
    }
    
} catch (Exception $e) {
    error_log("API v2 Exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    apiV2ErrorResponse('เกิดข้อผิดพลาดที่ไม่คาดคิด', 500, [
        'error' => $e->getMessage(),
        'file' => basename($e->getFile()),
        'line' => $e->getLine(),
        'trace' => explode("\n", $e->getTraceAsString())
    ]);
} catch (Error $e) {
    error_log("API v2 Fatal Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    apiV2ErrorResponse('เกิดข้อผิดพลาดร้ายแรง', 500, [
        'error' => $e->getMessage(),
        'file' => basename($e->getFile()),
        'line' => $e->getLine()
    ]);
}
?>
