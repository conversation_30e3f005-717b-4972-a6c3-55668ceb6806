<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile API - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .debug-log {
            background: #000;
            color: #0f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .error-log { color: #f00; }
        .success-log { color: #0f0; }
        .info-log { color: #ff0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">🔍 Test Profile API</h1>
            <p class="text-white-50">ทดสอบ Profile API หลัง Registration</p>
        </div>
        
        <!-- Current Status -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title text-success">✅ Registration สำเร็จแล้ว</h5>
                <div class="alert alert-success">
                    <strong>ขั้นตอนที่เสร็จแล้ว:</strong><br>
                    ✅ LIFF Integration<br>
                    ✅ Registration Flow<br>
                    ✅ Employee Registration<br>
                    ✅ LINE ID Linking<br><br>
                    <strong>ขั้นตอนต่อไป:</strong> ทดสอบ Profile API
                </div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 Profile API Tests</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>ข้อมูล LINE User:</h6>
                        <div id="userInfo" class="alert alert-info">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">กำลังโหลด...</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>การทดสอบ:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="testProfileAPI()">
                                👤 ทดสอบ Profile API
                            </button>
                            <button class="btn btn-success" onclick="testMainApp()">
                                🏠 ทดสอบ Main App
                            </button>
                            <button class="btn btn-info" onclick="checkRegistrationStatus()">
                                🔍 ตรวจสอบสถานะลงทะเบียน
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Logs -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📋 Debug Logs</h5>
                <div id="debugLogs" class="debug-log">
                    [Ready] Profile API debug console...
                </div>
                <div class="mt-2">
                    <button class="btn btn-secondary btn-sm" onclick="clearLogs()">
                        🗑️ Clear Logs
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Solutions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🔧 วิธีแก้ไขปัญหา</h5>
                
                <div class="alert alert-warning">
                    <h6>หากยังมี "Unexpected token" error:</h6>
                    <ol>
                        <li>ตรวจสอบ PHP error logs</li>
                        <li>ตรวจสอบ database connection</li>
                        <li>ตรวจสอบ Employee model methods</li>
                        <li>ตรวจสอบ include/require paths</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        let currentUser = null;
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsDiv = document.getElementById('debugLogs');
            const colorClass = type === 'error' ? 'error-log' : type === 'success' ? 'success-log' : 'info-log';
            logsDiv.innerHTML += `<div class="${colorClass}">${logEntry}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('debugLogs').innerHTML = '[Ready] Profile API debug console cleared...';
        }
        
        // Initialize LIFF
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                log('Initializing LIFF...', 'info');
                await liff.init({ liffId: LIFF_ID });
                
                if (liff.isLoggedIn()) {
                    currentUser = await liff.getProfile();
                    log(`LIFF initialized successfully. User: ${currentUser.displayName}`, 'success');
                    
                    // Display user info
                    const userInfoHtml = `
                        <div class="text-center">
                            <img src="${currentUser.pictureUrl}" alt="Profile" style="width: 60px; height: 60px; border-radius: 50%; margin-bottom: 10px;">
                            <h6>${currentUser.displayName}</h6>
                            <small>LINE ID: ${currentUser.userId}</small>
                        </div>
                    `;
                    document.getElementById('userInfo').innerHTML = userInfoHtml;
                    
                } else {
                    log('User not logged in', 'error');
                }
            } catch (error) {
                log(`LIFF initialization failed: ${error.message}`, 'error');
            }
        });
        
        async function testProfileAPI() {
            log('Testing Profile API...', 'info');
            
            try {
                if (!currentUser) {
                    log('No LINE user profile available', 'error');
                    return;
                }
                
                const accessToken = await liff.getAccessToken();
                log(`Access Token: ${accessToken ? 'Available' : 'Not available'}`, accessToken ? 'success' : 'error');
                
                log('Calling Profile API...', 'info');
                const response = await fetch(`${API_BASE_URL}/employee/profile.php`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`Response Status: ${response.status}`, response.ok ? 'success' : 'error');
                log(`Response Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`, 'info');
                
                const responseText = await response.text();
                log(`Response Text: ${responseText}`, 'info');
                
                try {
                    const data = JSON.parse(responseText);
                    log(`Parsed JSON: ${JSON.stringify(data, null, 2)}`, data.success ? 'success' : 'error');
                    
                    if (data.success) {
                        log(`✅ Profile loaded: ${data.data.first_name} ${data.data.last_name}`, 'success');
                    } else {
                        log(`❌ Profile error: ${data.message}`, 'error');
                    }
                } catch (jsonError) {
                    log(`❌ Failed to parse JSON: ${jsonError.message}`, 'error');
                    log(`Raw response (first 300 chars): ${responseText.substring(0, 300)}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Profile API test error: ${error.message}`, 'error');
            }
        }
        
        async function testMainApp() {
            log('Opening main app for testing...', 'info');
            window.open('index.html', '_blank');
        }
        
        async function checkRegistrationStatus() {
            log('Checking registration status...', 'info');
            
            try {
                // First check if we can call profile API
                await testProfileAPI();
                
                // Then check database directly if possible
                log('Registration status check completed', 'info');
                
            } catch (error) {
                log(`Registration status check error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
