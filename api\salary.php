<?php
require_once '../config/config.php';

header('Content-Type: application/json');

$auth = new Auth();
if (!$auth->isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$salaryModel = new Salary();

try {
    switch ($method) {
        case 'GET':
            requirePermission('salary.view');
            
            $employeeId = $_GET['employee_id'] ?? null;
            if (!$employeeId) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ต้องระบุรหัสพนักงาน']);
                exit;
            }
            
            $salary = $salaryModel->getByEmployeeId($employeeId);
            
            echo json_encode([
                'success' => true,
                'data' => $salary
            ]);
            break;
            
        case 'POST':
            requirePermission('salary.edit');
            
            if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'โทเค็น CSRF ไม่ถูกต้อง']);
                exit;
            }
            
            // Validate input
            $validator = new Validator($_POST);
            $validator->required('employee_id', 'Employee ID is required')
                     ->required('basic_salary', 'Basic salary is required')
                     ->required('effective_date', 'Effective date is required')
                     ->numeric('basic_salary', 'Basic salary must be a number')
                     ->date('effective_date', 'Y-m-d', 'Invalid effective date format');
            
            if ($validator->fails()) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ถูกต้อง']);
                exit;
            }
            
            // Prepare data
            $data = [
                'employee_id' => $_POST['employee_id'],
                'basic_salary' => floatval($_POST['basic_salary']),
                'allowances' => floatval($_POST['allowances'] ?? 0),
                'overtime_rate' => floatval($_POST['overtime_rate'] ?? 0),
                'effective_date' => $_POST['effective_date'],
                'is_active' => isset($_POST['is_active']) ? 1 : 0
            ];
            
            // Check if salary record exists
            $existingSalary = $salaryModel->getByEmployeeId($data['employee_id']);
            
            if ($existingSalary) {
                // Update existing salary
                $result = $salaryModel->update($existingSalary['id'], $data);
            } else {
                // Create new salary
                $result = $salaryModel->create($data);
            }
            
            if ($result['success']) {
                echo json_encode(['success' => true, 'message' => $result['message']]);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $result['message']]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'วิธีการเรียกใช้ไม่ได้รับอนุญาต']);
            break;
    }
} catch (Exception $e) {
    error_log("Salary API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์']);
}
?>
