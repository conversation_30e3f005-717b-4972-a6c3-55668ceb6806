<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detailed Debug - Registration Issue</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .debug-log {
            background: #000;
            color: #0f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            margin: 10px 0;
            font-size: 12px;
        }
        
        .error-log { color: #f00; }
        .success-log { color: #0f0; }
        .info-log { color: #ff0; }
        .warning-log { color: #ffa500; }
        
        .test-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">🔍 Detailed Debug</h1>
            <p class="text-white-50">แก้ไขปัญหา "เกิดข้อผิดพลาดในการลงทะเบียน"</p>
        </div>
        
        <!-- Problem Analysis -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title text-danger">🚨 ปัญหาปัจจุบัน</h5>
                <div class="alert alert-warning">
                    <strong>ปัญหา:</strong> "เกิดข้อผิดพลาดในการลงทะเบียน กรุณาลองใหม่อีกครั้ง"<br>
                    <strong>สาเหตุที่เป็นไปได้:</strong>
                    <ul class="mb-0">
                        <li>ไม่พบข้อมูลพนักงานในฐานข้อมูล</li>
                        <li>รหัสพนักงานหรือเบอร์โทรไม่ตรงกัน</li>
                        <li>Database connection error</li>
                        <li>PHP class/method ไม่พบ</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Test Form -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 ทดสอบการลงทะเบียน</h5>
                
                <div class="test-form">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>ข้อมูลทดสอบ:</h6>
                            <div class="mb-3">
                                <label class="form-label">รหัสพนักงาน:</label>
                                <input type="text" class="form-control" id="employeeCode" value="EMP001">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">เบอร์โทร:</label>
                                <input type="text" class="form-control" id="phone" value="0812345678">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>LINE User Info:</h6>
                            <div id="lineUserInfo" class="alert alert-info">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm"></div>
                                    <span class="ms-2">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <button class="btn btn-primary me-md-2" onclick="testDetailedRegistration()">
                            🔍 ทดสอบการลงทะเบียนแบบละเอียด
                        </button>
                        <button class="btn btn-info me-md-2" onclick="checkEmployeeData()">
                            👤 ตรวจสอบข้อมูลพนักงาน
                        </button>
                        <button class="btn btn-success" onclick="testDatabaseConnection()">
                            🗄️ ทดสอบฐานข้อมูล
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Logs -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📋 Detailed Debug Logs</h5>
                <div id="debugLogs" class="debug-log">
                    [Ready] Detailed debug console initialized...
                </div>
                <div class="mt-2">
                    <button class="btn btn-secondary btn-sm" onclick="clearLogs()">
                        🗑️ Clear Logs
                    </button>
                    <button class="btn btn-info btn-sm" onclick="exportLogs()">
                        📤 Export Logs
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Solutions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">💡 วิธีแก้ไขที่เป็นไปได้</h5>
                
                <div class="accordion" id="solutionsAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#solution1">
                                1. เพิ่มข้อมูลพนักงานในฐานข้อมูล
                            </button>
                        </h2>
                        <div id="solution1" class="accordion-collapse collapse show" data-bs-parent="#solutionsAccordion">
                            <div class="accordion-body">
                                <p>หากไม่พบข้อมูลพนักงาน ให้เพิ่มข้อมูลทดสอบ:</p>
                                <pre class="bg-dark text-light p-3 rounded">
INSERT INTO employees (employee_code, first_name, last_name, nickname, phone, position, department, email, hire_date, employment_status, created_at) 
VALUES ('EMP001', 'John', 'Doe', 'John', '0812345678', 'Software Developer', 'IT Department', '<EMAIL>', '2024-01-01', 'active', NOW());
                                </pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution2">
                                2. ตรวจสอบ Database Schema
                            </button>
                        </h2>
                        <div id="solution2" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                            <div class="accordion-body">
                                <p>ตรวจสอบว่า table employees มี columns ที่จำเป็น:</p>
                                <pre class="bg-dark text-light p-3 rounded">
-- ตรวจสอบ table structure
DESCRIBE employees;

-- ตรวจสอบข้อมูลที่มีอยู่
SELECT employee_code, phone, line_id FROM employees;
                                </pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution3">
                                3. ทดสอบด้วยข้อมูลอื่น
                            </button>
                        </h2>
                        <div id="solution3" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                            <div class="accordion-body">
                                <p>ลองใช้ข้อมูลพนักงานอื่นที่มีในฐานข้อมูล:</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="testWithEMP002()">
                                            Test EMP002
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="testWithEMP003()">
                                            Test EMP003
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        let currentUser = null;
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsDiv = document.getElementById('debugLogs');
            const colorClass = type === 'error' ? 'error-log' : type === 'success' ? 'success-log' : type === 'warning' ? 'warning-log' : 'info-log';
            logsDiv.innerHTML += `<div class="${colorClass}">${logEntry}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('debugLogs').innerHTML = '[Ready] Detailed debug console cleared...';
        }
        
        function exportLogs() {
            const logText = logs.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'registration-detailed-debug.txt';
            a.click();
        }
        
        // Initialize LIFF
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                log('Initializing LIFF for detailed debug...', 'info');
                await liff.init({ liffId: LIFF_ID });
                
                if (liff.isLoggedIn()) {
                    currentUser = await liff.getProfile();
                    log(`LIFF initialized successfully. User: ${currentUser.displayName}`, 'success');
                    
                    // Display LINE user info
                    const userInfoHtml = `
                        <div class="text-center">
                            <img src="${currentUser.pictureUrl}" alt="Profile" style="width: 50px; height: 50px; border-radius: 50%; margin-bottom: 10px;">
                            <h6>${currentUser.displayName}</h6>
                            <small>ID: ${currentUser.userId}</small>
                        </div>
                    `;
                    document.getElementById('lineUserInfo').innerHTML = userInfoHtml;
                    
                } else {
                    log('User not logged in', 'error');
                }
            } catch (error) {
                log(`LIFF initialization failed: ${error.message}`, 'error');
            }
        });
        
        async function testDetailedRegistration() {
            const employeeCode = document.getElementById('employeeCode').value;
            const phone = document.getElementById('phone').value;
            
            log(`🧪 Starting detailed registration test...`, 'info');
            log(`Employee Code: ${employeeCode}`, 'info');
            log(`Phone: ${phone}`, 'info');
            log(`LINE User: ${currentUser?.displayName}`, 'info');
            log(`LINE ID: ${currentUser?.userId}`, 'info');
            
            try {
                if (!currentUser) {
                    log('❌ No LINE user profile available', 'error');
                    return;
                }
                
                const accessToken = await liff.getAccessToken();
                log(`Access Token: ${accessToken ? 'Available (' + accessToken.substring(0, 20) + '...)' : 'Not available'}`, accessToken ? 'success' : 'error');
                
                log('📡 Sending registration request...', 'info');
                
                const requestBody = {
                    employee_code: employeeCode,
                    phone: phone
                };
                
                log(`Request Body: ${JSON.stringify(requestBody)}`, 'info');
                
                const response = await fetch(`${API_BASE_URL}/employee/register.php`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                log(`Response Status: ${response.status}`, response.ok ? 'success' : 'error');
                log(`Response Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`, 'info');
                
                const responseText = await response.text();
                log(`Raw Response: ${responseText}`, 'info');
                
                try {
                    const data = JSON.parse(responseText);
                    log(`Parsed JSON: ${JSON.stringify(data, null, 2)}`, data.success ? 'success' : 'error');
                    
                    if (data.success) {
                        log(`✅ Registration successful!`, 'success');
                        log(`Employee: ${data.data.first_name} ${data.data.last_name}`, 'success');
                        
                        Swal.fire({
                            icon: 'success',
                            title: 'ลงทะเบียนสำเร็จ!',
                            text: `ยินดีต้อนรับ ${data.data.first_name} ${data.data.last_name}`,
                            confirmButtonText: 'เยี่ยม!'
                        });
                    } else {
                        log(`❌ Registration failed: ${data.message}`, 'error');
                        if (data.debug) {
                            log(`Debug Info: ${JSON.stringify(data.debug, null, 2)}`, 'warning');
                        }
                    }
                } catch (jsonError) {
                    log(`❌ Failed to parse JSON: ${jsonError.message}`, 'error');
                    log(`This means the server returned HTML instead of JSON`, 'error');
                    log(`Check PHP error logs on the server`, 'warning');
                }
                
            } catch (error) {
                log(`❌ Registration test error: ${error.message}`, 'error');
            }
        }
        
        async function checkEmployeeData() {
            log('🔍 Checking employee data in database...', 'info');
            
            // This would require a separate API endpoint to check database
            log('💡 To check employee data, run this SQL on the server:', 'warning');
            log('SELECT * FROM employees WHERE employee_code = "EMP001" OR phone = "0812345678";', 'warning');
        }
        
        async function testDatabaseConnection() {
            log('🗄️ Testing database connection...', 'info');
            
            try {
                // Test with a simple API call
                const response = await fetch(`${API_BASE_URL}/test.php`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Database connection works - PHP ${data.php_version}`, 'success');
                } else {
                    log(`❌ Database connection failed`, 'error');
                }
            } catch (error) {
                log(`❌ Database test error: ${error.message}`, 'error');
            }
        }
        
        function testWithEMP002() {
            document.getElementById('employeeCode').value = 'EMP002';
            document.getElementById('phone').value = '0823456789';
            log('📝 Changed test data to EMP002', 'info');
        }
        
        function testWithEMP003() {
            document.getElementById('employeeCode').value = 'EMP003';
            document.getElementById('phone').value = '0834567890';
            log('📝 Changed test data to EMP003', 'info');
        }
    </script>
</body>
</html>
