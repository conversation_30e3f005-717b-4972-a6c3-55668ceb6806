<?php
/**
 * Benefit Model
 * Human Resources Center System
 */

class Benefit {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new Database();
        $this->logger = new Logger();
    }
    
    /**
     * Get all benefits
     */
    public function getAll($limit = null, $offset = 0, $search = null, $type = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT * FROM benefits WHERE 1=1";
            $params = [];
            
            if ($search) {
                $sql .= " AND (name LIKE ? OR description LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if ($type) {
                $sql .= " AND benefit_type = ?";
                $params[] = $type;
            }
            
            $sql .= " ORDER BY name";
            
            if ($limit) {
                $sql .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get benefits error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get benefit by ID
     */
    public function getById($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT * FROM benefits WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get benefit by ID error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Create benefit
     */
    public function create($data) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "INSERT INTO benefits (name, benefit_type, description, amount, is_active)
                    VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['name'],
                $data['type'],
                $data['description'] ?? null,
                $data['amount'] ?? 0,
                $data['is_active'] ?? 1
            ]);
            
            if ($result) {
                $benefitId = $pdo->lastInsertId();
                
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'benefit_created',
                    "Benefit created: {$data['name']}",
                    ['benefit_id' => $benefitId]
                );
                
                return ['success' => true, 'id' => $benefitId, 'message' => 'สร้างข้อมูลสวัสดิการเรียบร้อยแล้ว'];
            }
            
            return ['success' => false, 'message' => 'ไม่สามารถสร้างข้อมูลสวัสดิการได้'];
            
        } catch (Exception $e) {
            error_log("Create benefit error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'create_benefit_error', $e->getMessage(), $data);
            return ['success' => false, 'message' => 'ไม่สามารถสร้างข้อมูลสวัสดิการได้: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update benefit
     */
    public function update($id, $data) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE benefits SET
                        name = ?, benefit_type = ?, description = ?, amount = ?, is_active = ?
                    WHERE id = ?";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['name'],
                $data['type'],
                $data['description'] ?? null,
                $data['amount'] ?? 0,
                $data['is_active'] ?? 1,
                $id
            ]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'benefit_updated',
                    "Benefit updated: {$data['name']}",
                    ['benefit_id' => $id]
                );
                
                return ['success' => true, 'message' => 'อัปเดตข้อมูลสวัสดิการเรียบร้อยแล้ว'];
            }
            
            return ['success' => false, 'message' => 'ไม่สามารถอัปเดตข้อมูลสวัสดิการได้'];
            
        } catch (Exception $e) {
            error_log("Update benefit error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'update_benefit_error', $e->getMessage(), ['benefit_id' => $id]);
            return ['success' => false, 'message' => 'Failed to update benefit: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete benefit
     */
    public function delete($id) {
        try {
            $pdo = $this->db->getConnection();
            
            // Get benefit info before deletion
            $benefit = $this->getById($id);
            if (!$benefit) {
                return ['success' => false, 'message' => 'Benefit not found'];
            }
            
            $sql = "DELETE FROM benefits WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'benefit_deleted',
                    "Benefit deleted: {$benefit['name']}",
                    ['benefit_id' => $id]
                );
                
                return ['success' => true, 'message' => 'ลบข้อมูลสวัสดิการเรียบร้อยแล้ว'];
            }
            
            return ['success' => false, 'message' => 'ไม่สามารถลบข้อมูลสวัสดิการได้'];
            
        } catch (Exception $e) {
            error_log("Delete benefit error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'delete_benefit_error', $e->getMessage(), ['benefit_id' => $id]);
            return ['success' => false, 'message' => 'Failed to delete benefit: ' . $e->getMessage()];
        }
    }
    
    /**
     * Count benefits
     */
    public function count($search = null, $type = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT COUNT(*) FROM benefits WHERE 1=1";
            $params = [];
            
            if ($search) {
                $sql .= " AND (name LIKE ? OR description LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if ($type) {
                $sql .= " AND benefit_type = ?";
                $params[] = $type;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log("Count benefits error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get employee benefits
     */
    public function getEmployeeBenefits($employeeId) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT eb.*, b.name, b.benefit_type as type, b.description, b.amount
                    FROM employee_benefits eb
                    JOIN benefits b ON eb.benefit_id = b.id
                    WHERE eb.employee_id = ? AND eb.is_active = 1
                    ORDER BY b.name";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$employeeId]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get employee benefits error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Assign benefit to employee
     */
    public function assignToEmployee($employeeId, $benefitId, $startDate = null, $endDate = null) {
        try {
            $pdo = $this->db->getConnection();
            
            // Check if already assigned
            $sql = "SELECT id FROM employee_benefits WHERE employee_id = ? AND benefit_id = ? AND is_active = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$employeeId, $benefitId]);
            
            if ($stmt->fetch()) {
                return ['success' => false, 'message' => 'Benefit already assigned to this employee'];
            }
            
            $sql = "INSERT INTO employee_benefits (employee_id, benefit_id, start_date, end_date, is_active) 
                    VALUES (?, ?, ?, ?, 1)";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $employeeId,
                $benefitId,
                $startDate ?: date('Y-m-d'),
                $endDate
            ]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'benefit_assigned',
                    "Benefit assigned to employee",
                    ['employee_id' => $employeeId, 'benefit_id' => $benefitId]
                );
                
                return ['success' => true, 'message' => 'Benefit assigned successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to assign benefit'];
            
        } catch (Exception $e) {
            error_log("Assign benefit error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to assign benefit: ' . $e->getMessage()];
        }
    }
    
    /**
     * Remove benefit from employee
     */
    public function removeFromEmployee($employeeId, $benefitId) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE employee_benefits SET is_active = 0, end_date = CURDATE() 
                    WHERE employee_id = ? AND benefit_id = ? AND is_active = 1";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$employeeId, $benefitId]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'benefit_removed',
                    "Benefit removed from employee",
                    ['employee_id' => $employeeId, 'benefit_id' => $benefitId]
                );
                
                return ['success' => true, 'message' => 'Benefit removed successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to remove benefit'];
            
        } catch (Exception $e) {
            error_log("Remove benefit error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to remove benefit: ' . $e->getMessage()];
        }
    }

    /**
     * Get benefit types
     */
    public function getBenefitTypes() {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT DISTINCT benefit_type FROM benefits WHERE benefit_type IS NOT NULL ORDER BY benefit_type";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_COLUMN);

        } catch (Exception $e) {
            error_log("Get benefit types error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get active benefits count
     */
    public function getActiveBenefitsCount() {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT COUNT(*) FROM benefits WHERE is_active = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();

            return $stmt->fetchColumn();

        } catch (Exception $e) {
            error_log("Get active benefits count error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get total benefit value
     */
    public function getTotalBenefitValue() {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT SUM(amount) FROM benefits WHERE is_active = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();

            return $stmt->fetchColumn() ?? 0;

        } catch (Exception $e) {
            error_log("Get total benefit value error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get monthly benefit cost
     */
    public function getMonthlyBenefitCost() {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT SUM(amount) FROM benefits WHERE is_active = 1 AND benefit_type = 'monthly'";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();

            return $stmt->fetchColumn() ?? 0;

        } catch (Exception $e) {
            error_log("Get monthly benefit cost error: " . $e->getMessage());
            return 0;
        }
    }
}
