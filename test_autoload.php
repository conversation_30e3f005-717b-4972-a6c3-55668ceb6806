<?php
/**
 * Test Autoload Script
 * ทดสอบการโหลด Classes
 */

require_once 'config/config.php';

echo "<h2>Testing Class Autoloading</h2>";

// Test Database class
echo "<h3>Testing Database Class:</h3>";
try {
    $db = new Database();
    echo "✅ Database class loaded successfully<br>";
    
    $connection = $db->getConnection();
    echo "✅ Database connection successful<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test Auth class
echo "<h3>Testing Auth Class:</h3>";
try {
    $auth = new Auth();
    echo "✅ Auth class loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Auth error: " . $e->getMessage() . "<br>";
}

// Test Logger class
echo "<h3>Testing Logger Class:</h3>";
try {
    $logger = new Logger();
    echo "✅ Logger class loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Logger error: " . $e->getMessage() . "<br>";
}

// Test Employee class
echo "<h3>Testing Employee Class:</h3>";
try {
    $employee = new Employee();
    echo "✅ Employee class loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Employee error: " . $e->getMessage() . "<br>";
}

echo "<h3>Autoload Directories:</h3>";
echo "<ul>";
echo "<li>Config: " . __DIR__ . "/config/</li>";
echo "<li>Models: " . __DIR__ . "/models/</li>";
echo "<li>Controllers: " . __DIR__ . "/controllers/</li>";
echo "<li>Core: " . __DIR__ . "/core/</li>";
echo "<li>Helpers: " . __DIR__ . "/helpers/</li>";
echo "</ul>";

echo "<h3>File Existence Check:</h3>";
$files = [
    'config/database.php' => 'Database',
    'core/Auth.php' => 'Auth',
    'core/Logger.php' => 'Logger',
    'models/Employee.php' => 'Employee'
];

foreach ($files as $file => $class) {
    $exists = file_exists(__DIR__ . '/' . $file);
    echo ($exists ? "✅" : "❌") . " {$file} ({$class})<br>";
}
?>
