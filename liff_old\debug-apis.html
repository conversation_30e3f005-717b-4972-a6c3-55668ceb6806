<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug APIs - Leave & Payroll</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .debug-log {
            background: #000;
            color: #0f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            margin: 10px 0;
            font-size: 12px;
        }
        
        .error-log { color: #f00; }
        .success-log { color: #0f0; }
        .info-log { color: #ff0; }
        .warning-log { color: #ffa500; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">🔧 Debug Leave & Payroll APIs</h1>
            <p class="text-white-50">แก้ไขปัญหา "Unexpected token" ใน APIs</p>
        </div>
        
        <!-- Problem Analysis -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title text-danger">🚨 ปัญหาที่พบ</h5>
                <div class="alert alert-warning">
                    <strong>ปัญหา:</strong> "Unexpected token '<'" ใน APIs ทั้งหมด<br>
                    <strong>สาเหตุ:</strong>
                    <ul class="mb-0">
                        <li>❌ User not logged in - ไม่มี access token</li>
                        <li>❌ PHP errors/warnings ปนเปื้อนใน JSON response</li>
                        <li>❌ Missing dependencies (models, config files)</li>
                        <li>❌ Database connection issues</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Debug Tests -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 Debug Tests (No Authentication)</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>📝 Leave Debug APIs:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="debugLeaveTypes()">
                                📋 Debug Leave Types
                            </button>
                            <button class="btn btn-success" onclick="debugLeaveHistory()">
                                📊 Debug Leave History
                            </button>
                            <button class="btn btn-warning" onclick="debugLeaveSubmit()">
                                ✉️ Debug Leave Submit
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>💰 Payroll Debug APIs:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-info" onclick="debugPayslip()">
                                💳 Debug Latest Payslip
                            </button>
                            <button class="btn btn-secondary" onclick="debugPayrollHistory()">
                                📈 Debug Payroll History
                            </button>
                            <button class="btn btn-dark" onclick="debugSpecificPayslip()">
                                🗓️ Debug Specific Month
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Raw Response Viewer -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📋 Raw Response Viewer</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>🔍 Direct API Calls:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="viewRawResponse('/api/debug/test-leave.php?action=types')">
                                📋 Raw Leave Types
                            </button>
                            <button class="btn btn-outline-success" onclick="viewRawResponse('/api/debug/test-payroll.php?action=latest')">
                                💳 Raw Payslip
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>🗄️ Database Tests:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-info" onclick="testDatabaseConnection()">
                                🔗 Test DB Connection
                            </button>
                            <button class="btn btn-outline-warning" onclick="checkModels()">
                                📦 Check Models
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Console -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📋 Debug Console</h5>
                <div id="debugLogs" class="debug-log">
                    [Ready] Debug APIs console initialized...
                </div>
                <div class="mt-2">
                    <button class="btn btn-secondary btn-sm" onclick="clearLogs()">
                        🗑️ Clear Logs
                    </button>
                    <button class="btn btn-info btn-sm" onclick="exportLogs()">
                        📤 Export Logs
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Solutions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">💡 Solutions</h5>
                
                <div class="alert alert-success">
                    <h6>✅ Debug APIs Created:</h6>
                    <ul>
                        <li>✅ <code>/api/debug/test-leave.php</code> - ทดสอบ Leave APIs โดยไม่ต้อง auth</li>
                        <li>✅ <code>/api/debug/test-payroll.php</code> - ทดสอบ Payroll APIs โดยไม่ต้อง auth</li>
                        <li>✅ Auto-create tables และ sample data</li>
                        <li>✅ Enhanced error reporting</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6>🔧 Next Steps:</h6>
                    <ol>
                        <li>ทดสอบ debug APIs เพื่อหา root cause</li>
                        <li>แก้ไข PHP errors/warnings</li>
                        <li>ตรวจสอบ database tables</li>
                        <li>ทดสอบ main APIs อีกครั้ง</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsDiv = document.getElementById('debugLogs');
            const colorClass = type === 'error' ? 'error-log' : type === 'success' ? 'success-log' : type === 'warning' ? 'warning-log' : 'info-log';
            logsDiv.innerHTML += `<div class="${colorClass}">${logEntry}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('debugLogs').innerHTML = '[Ready] Debug APIs console cleared...';
        }
        
        function exportLogs() {
            const logText = logs.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'leave-payroll-debug.txt';
            a.click();
        }
        
        // Debug API Tests
        async function debugLeaveTypes() {
            log('🧪 Testing debug leave types API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/test-leave.php?action=types`);
                log(`Response Status: ${response.status}`, response.ok ? 'success' : 'error');
                log(`Content-Type: ${response.headers.get('content-type')}`, 'info');
                
                const responseText = await response.text();
                log(`Response Length: ${responseText.length} characters`, 'info');
                
                if (responseText.trim().startsWith('<') || responseText.includes('Warning:')) {
                    log(`❌ Response contains HTML/PHP warnings:`, 'error');
                    log(`First 200 chars: ${responseText.substring(0, 200)}`, 'error');
                    return;
                }
                
                const data = JSON.parse(responseText);
                log(`✅ JSON parsed successfully!`, 'success');
                
                if (data.success) {
                    log(`✅ Leave Types: Found ${data.data.length} types`, 'success');
                    log(`📋 Types: ${data.data.map(t => t.type_name).join(', ')}`, 'info');
                } else {
                    log(`❌ Leave Types failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Debug Leave Types error: ${error.message}`, 'error');
            }
        }
        
        async function debugLeaveHistory() {
            log('🧪 Testing debug leave history API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/test-leave.php?action=history`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Leave History: Found ${data.data.length} requests`, 'success');
                    log(`👤 Test Employee: ${data.test_employee.name}`, 'info');
                } else {
                    log(`❌ Leave History failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Debug Leave History error: ${error.message}`, 'error');
            }
        }
        
        async function debugLeaveSubmit() {
            log('🧪 Testing debug leave submit API...', 'info');
            
            const testData = {
                leave_type_id: 1,
                start_date: '2024-08-25',
                end_date: '2024-08-26',
                reason: 'ทดสอบระบบการลาผ่าน Debug API'
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/test-leave.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Leave Submit: Request ID ${data.data.request_id}`, 'success');
                    log(`📝 Total Days: ${data.data.total_days} days`, 'info');
                } else {
                    log(`❌ Leave Submit failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Debug Leave Submit error: ${error.message}`, 'error');
            }
        }
        
        async function debugPayslip() {
            log('🧪 Testing debug payslip API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/test-payroll.php?action=latest`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Latest Payslip: ${data.data.pay_period_start} to ${data.data.pay_period_end}`, 'success');
                    log(`💰 Net Pay: ฿${Number(data.data.net_pay).toLocaleString()}`, 'info');
                    log(`👤 Test Employee: ${data.test_employee.name} (${data.test_employee.position})`, 'info');
                } else {
                    log(`❌ Payslip failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Debug Payslip error: ${error.message}`, 'error');
            }
        }
        
        async function debugPayrollHistory() {
            log('🧪 Testing debug payroll history API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/test-payroll.php?action=history&limit=6`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Payroll History: Found ${data.data.length} records`, 'success');
                    if (data.data.length > 0) {
                        log(`📈 Latest: ฿${Number(data.data[0].net_pay).toLocaleString()} (${data.data[0].pay_period_start})`, 'info');
                    }
                } else {
                    log(`❌ Payroll History failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Debug Payroll History error: ${error.message}`, 'error');
            }
        }
        
        async function debugSpecificPayslip() {
            log('🧪 Testing debug specific payslip API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/test-payroll.php?action=specific&year=2024&month=8`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ August 2024 Payslip: ฿${Number(data.data.net_pay).toLocaleString()}`, 'success');
                } else {
                    log(`❌ Specific Payslip failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Debug Specific Payslip error: ${error.message}`, 'error');
            }
        }
        
        async function viewRawResponse(endpoint) {
            log(`🔍 Viewing raw response for ${endpoint}...`, 'info');
            
            try {
                const response = await fetch(`https://smartapplytech.com/hrc${endpoint}`);
                const responseText = await response.text();
                
                log(`Status: ${response.status}`, response.ok ? 'success' : 'error');
                log(`Content-Type: ${response.headers.get('content-type')}`, 'info');
                log(`Response Length: ${responseText.length} characters`, 'info');
                
                if (responseText.length > 0) {
                    log(`First 500 chars: ${responseText.substring(0, 500)}`, 'info');
                    
                    if (responseText.includes('Warning:') || responseText.includes('Error:')) {
                        log(`⚠️ PHP warnings/errors detected in response`, 'warning');
                    }
                    
                    if (responseText.trim().startsWith('{')) {
                        log(`✅ Response looks like valid JSON`, 'success');
                    } else {
                        log(`❌ Response does not start with JSON`, 'error');
                    }
                }
                
            } catch (error) {
                log(`❌ Raw response error: ${error.message}`, 'error');
            }
        }
        
        async function testDatabaseConnection() {
            log('🗄️ Testing database connection...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/test.php`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Database connection works - PHP ${data.php_version}`, 'success');
                } else {
                    log(`❌ Database connection failed`, 'error');
                }
            } catch (error) {
                log(`❌ Database test error: ${error.message}`, 'error');
            }
        }
        
        function checkModels() {
            log('📦 Checking models...', 'info');
            log('💡 Models should be auto-loaded by config.php', 'info');
            log('📋 Required: Leave.php, Payroll.php, Employee.php', 'info');
            log('🔧 Check if models directory exists and files are uploaded', 'warning');
        }
    </script>
</body>
</html>
