{"version": 3, "sources": ["customizer.scss", "customizer/_reboot.scss", "customizer/_root.scss", "customizer/utilities/_utilities.scss", "customizer/components/accordion/_accordion.scss", "customizer/components/alert/_alert.scss", "customizer/components/avatar/_avatar.scss", "customizer/components/dropdown/_dropdown.scss", "customizer/components/button/_button.scss", "customizer/components/button/_border-btn.scss", "customizer/components/list-group/_list-group.scss", "customizer/components/forms/_form-control.scss", "customizer/components/forms/_form-check.scss", "customizer/components/forms/_form-wizard.scss", "customizer/components/nav/_nav.scss", "customizer/components/nav/_navbar.scss", "customizer/components/pagination/_pagination.scss", "customizer/components/table/_table.scss", "customizer/components/pages/pricing/_pricing.scss", "customizer/components/circleprogressbar/_circle-progress.scss", "customizer/components/calender/_calender.scss", "customizer/components/pricing/_pricing.scss"], "names": [], "mappings": "AAAA;;;;;;;EAAA,ECCI,uBAAA,CAEJ,QACI,uBAAA,CAGJ,0CACI,gCAAA,CAGJ,KACI,kCAAA,CCZJ,MAGQ,4BACA,sBACA,+BA<PERSON>,+BA<PERSON>,+BACA,+BA<PERSON>,8BACA,8BACA,8BACA,8BACA,8BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,0BAAA,CAOA,wBAlBA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,4BAXA,4BACA,kBACA,2BACA,2BACA,2BACA,2BACA,0BACA,0BACA,0BACA,0BACA,0BACA,0BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,4BACA,4BACA,4BACA,4BACA,4BACA,4BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,yBAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,0BACA,0BACA,0BACA,0BACA,0BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,6BAXA,4BACA,wBACA,iCACA,iCACA,iCACA,iCACA,gCACA,gCACA,gCACA,gCACA,gCACA,+BAXA,4BACA,sBACA,+BACA,+BACA,+BACA,+BACA,8BACA,8BACA,8BACA,8BACA,8BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,0BAAA,CAOA,wBAlBA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,4BAXA,4BACA,kBACA,2BACA,2BACA,2BACA,2BACA,0BACA,0BACA,0BACA,0BACA,0BACA,0BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,4BACA,4BACA,4BACA,4BACA,4BACA,4BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,yBAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,0BACA,0BACA,0BACA,0BACA,0BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,6BAXA,4BACA,wBACA,iCACA,iCACA,iCACA,iCACA,gCACA,gCACA,gCACA,gCACA,gCACA,+BAXA,4BACA,sBACA,+BACA,+BACA,+BACA,+BACA,8BACA,8BACA,8BACA,8BACA,8BACA,gCAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,0BAAA,CAOA,uBAlBA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,4BAXA,4BACA,kBACA,2BACA,2BACA,2BACA,2BACA,0BACA,0BACA,0BACA,0BACA,0BACA,0BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,4BACA,4BACA,4BACA,4BACA,4BACA,4BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,yBAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,0BACA,0BACA,0BACA,0BACA,0BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,6BAXA,4BACA,wBACA,iCACA,iCACA,iCACA,iCACA,gCACA,gCACA,gCACA,gCACA,gCACA,+BAXA,4BACA,sBACA,+BACA,+BACA,+BACA,+BACA,8BACA,8BACA,8BACA,8BACA,8BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAAA,CAOA,0BAlBA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,4BAXA,4BACA,kBACA,2BACA,2BACA,2BACA,2BACA,0BACA,0BACA,0BACA,0BACA,0BACA,0BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,4BACA,4BACA,4BACA,4BACA,4BACA,4BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,yBAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,0BACA,0BACA,0BACA,0BACA,0BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,6BAXA,4BACA,wBACA,iCACA,iCACA,iCACA,iCACA,gCACA,gCACA,gCACA,gCACA,gCACA,+BAXA,4BACA,sBACA,+BACA,+BACA,+BACA,+BACA,8BACA,8BACA,8BACA,8BACA,8BACA,+BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAAA,CAOA,wBAlBA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,4BAXA,4BACA,kBACA,2BACA,2BACA,2BACA,2BACA,0BACA,0BACA,0BACA,0BACA,0BACA,0BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,qBACA,8BACA,8BACA,8BACA,8BACA,6BACA,6BACA,6BACA,6BACA,6BACA,8BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,4BACA,4BACA,4BACA,4BACA,4BACA,4BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,yBAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAXA,4BACA,oBACA,6BACA,6BACA,6BACA,6BACA,0BACA,0BACA,0BACA,0BACA,0BACA,8BAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,6BAXA,4BACA,wBACA,iCACA,iCACA,iCACA,iCACA,gCACA,gCACA,gCACA,gCACA,gCACA,+BAXA,4BACA,sBACA,+BACA,+BACA,+BACA,+BACA,8BACA,8BACA,8BACA,8BACA,8BACA,gCAXA,4BACA,mBACA,4BACA,4BACA,4BACA,4BACA,2BACA,2BACA,2BACA,2BACA,2BACA,2BAAA,CCbJ,cACI,kCAAA,CAEJ,YACI,6CAAA,CAEJ,iBACI,wBACA,4DAAA,CARJ,WACI,+BAAA,CAEJ,SACI,0CAAA,CAEJ,cACI,qBACA,yDAAA,CCRJ,kCACI,iCACA,2CACA,kEAAA,yDAAA,CAEJ,wBACI,uCACA,2EAAA,kEAAA,CCRR,eACI,wBACA,qCACA,8BAAA,CACA,2BACI,gCAAA,CAEJ,2BACI,sBACA,6BACA,8BAAA,CAEJ,0BACI,4CACA,8BAAA,CCbJ,+BACI,8BAAA,CCDJ,0CACI,gCAAA,CAEJ,4CACI,sBACA,kCAAA,CCJA,iBACI,sBACA,mCACA,8BAAA,CAEA,uBACI,sBACA,4CACA,uCAAA,CAEJ,uBACI,sBACA,4CACA,uCAAA,CAEJ,gDACI,sBACA,4CACA,uCAAA,CAGR,yBACI,iCACA,uCAAA,CACA,+BACI,sBACA,mCACA,8BAAA,CAEJ,gEACI,sBACA,4CACA,uCAAA,CAGR,sBACI,wBACA,kDACA,0BAAA,CACA,qFACI,gCACA,kDAEA,0BAAA,CA3CR,cACI,sBACA,gCACA,2BAAA,CAEA,oBACI,sBACA,yCACA,oCAAA,CAEJ,oBACI,sBACA,yCACA,oCAAA,CAEJ,0CACI,sBACA,yCACA,oCAAA,CAGR,sBACI,8BACA,oCAAA,CACA,4BACI,sBACA,gCACA,2BAAA,CAEJ,0DACI,sBACA,yCACA,oCAAA,CAGR,mBACI,qBACA,+CACA,0BAAA,CACA,4EACI,6BACA,+CAEA,0BAAA,CAOhB,UACI,uBAAA,CAGA,kBACI,wBACA,0CAAA,CAEJ,qBACI,wBACA,0CAAA,CAEJ,qBACI,wBACA,2CACA,kEAAA,yDAAA,CAEJ,kBACI,0CAAA,CCrEJ,mBACI,8BAAA,CCFR,yBACI,iCACA,0CAAA,CAEI,4GACI,iCACA,0CAAA,CAKR,wBACI,sBACA,mCACA,8BAAA,CAKJ,sFACI,+BAAA,CAEJ,4CACI,0CAAA,CCvBR,cACI,+BAAA,CACA,oBACI,sCAAA,CAIJ,mBACI,sCAAA,CCRR,kBACI,uCAAA,CACA,0BACI,wCACA,6CAAA,CAEJ,wBACI,mDACA,6CAAA,CAMR,gBACI,yCAAA,CCbI,mBACI,wBACA,oCAAA,CAEA,4BACI,4BAAA,CAIJ,0BACI,4BAAA,CAEA,mCACI,uBAAA,CCRZ,2BACI,sBACA,mCACA,+BAEI,qEAAA,4DAAA,CAMJ,sCAEQ,mEAAA,0DAAA,CASZ,4BACI,sBACA,kCAAA,CAIZ,YACI,iBAAA,CAEI,6BACI,8BAAA,CAGR,sBACI,SAAA,CAEJ,8BACI,qBACA,WACA,wBACA,4BAAA,CC7CQ,wHACI,sCAAA,CAKhB,qBACI,4BAAA,CAKA,+CACI,kCAAA,CAUQ,gEACI,oCAAA,CC1BhB,6BACI,sBACA,mCACA,8BAAA,CAGR,sBACI,uCACA,uBAAA,CCVR,eACI,yCACA,sCAAA,CCFJ,MACC,4BAAA,CAED,SACC,oCAAA,CAGD,gBACC,4BAAA,CACA,sBACC,oCAAA,CCRM,oDACI,wBAAA,CAEJ,mDACI,sBAAA,CAOJ,iDACI,qBAAA,CAEJ,gDACI,mBAAA,CCjBZ,mBACI,sBACA,8CACA,yCAAA,CCHJ,mBACC,wBACG,wBAAA,ClBDA,oBACI,kCAAA,CAEJ,kBACI,6CAAA,CAEJ,uBACI,wBACA,4DAAA,CARJ,iBACI,+BAAA,CAEJ,eACI,0CAAA,CAEJ,oBACI,qBACA,yDAAA,CCRJ,wCACI,iCACA,2CACA,kEAAA,yDAAA,CAEJ,8BACI,uCACA,2EAAA,kEAAA,CCRR,qBACI,wBACA,qCACA,8BAAA,CACA,iCACI,gCAAA,CAEJ,iCACI,sBACA,6BACA,8BAAA,CAEJ,gCACI,4CACA,8BAAA,CCbJ,qCACI,8BAAA,CCDJ,sDACI,gCAAA,CAEJ,wDACI,sBACA,kCAAA,CCJA,uBACI,sBACA,mCACA,8BAAA,CAEA,6BACI,sBACA,4CACA,uCAAA,CAEJ,6BACI,sBACA,4CACA,uCAAA,CAEJ,4DACI,sBACA,4CACA,uCAAA,CAGR,+BACI,iCACA,uCAAA,CACA,qCACI,sBACA,mCACA,8BAAA,CAEJ,4EACI,sBACA,4CACA,uCAAA,CAGR,4BACI,wBACA,kDACA,0BAAA,CACA,uGACI,gCACA,kDAEA,0BAAA,CA3CR,oBACI,sBACA,gCACA,2BAAA,CAEA,0BACI,sBACA,yCACA,oCAAA,CAEJ,0BACI,sBACA,yCACA,oCAAA,CAEJ,sDACI,sBACA,yCACA,oCAAA,CAGR,4BACI,8BACA,oCAAA,CACA,kCACI,sBACA,gCACA,2BAAA,CAEJ,sEACI,sBACA,yCACA,oCAAA,CAGR,yBACI,qBACA,+CACA,0BAAA,CACA,8FACI,6BACA,+CAEA,0BAAA,CAOhB,gBACI,uBAAA,CAGA,wBACI,wBACA,0CAAA,CAEJ,2BACI,wBACA,0CAAA,CAEJ,2BACI,wBACA,2CACA,kEAAA,yDAAA,CAEJ,wBACI,0CAAA,CCrEJ,yBACI,8BAAA,CCFR,+BACI,iCACA,0CAAA,CAEI,wHACI,iCACA,0CAAA,CAKR,8BACI,sBACA,mCACA,8BAAA,CAKJ,kGACI,+BAAA,CAEJ,kDACI,0CAAA,CCvBR,oBACI,+BAAA,CACA,0BACI,sCAAA,CAIJ,yBACI,sCAAA,CCRR,wBACI,uCAAA,CACA,gCACI,wCACA,6CAAA,CAEJ,8BACI,mDACA,6CAAA,CAMR,sBACI,yCAAA,CCbI,yBACI,wBACA,oCAAA,CAEA,kCACI,4BAAA,CAIJ,gCACI,4BAAA,CAEA,yCACI,uBAAA,CCRZ,iCACI,sBACA,mCACA,+BAEI,qEAAA,4DAAA,CAMJ,4CAEQ,mEAAA,0DAAA,CASZ,kCACI,sBACA,kCAAA,CAIZ,kBACI,iBAAA,CAEI,mCACI,8BAAA,CAGR,4BACI,SAAA,CAEJ,oCACI,qBACA,WACA,wBACA,4BAAA,CC7CQ,oIACI,sCAAA,CAKhB,2BACI,4BAAA,CAKA,qDACI,kCAAA,CAUQ,sEACI,oCAAA,CC1BhB,mCACI,sBACA,mCACA,8BAAA,CAGR,4BACI,uCACA,uBAAA,CCVR,qBACI,yCACA,sCAAA,CCFJ,YACC,4BAAA,CAED,eACC,oCAAA,CAGD,sBACC,4BAAA,CACA,4BACC,oCAAA,CCRM,0DACI,wBAAA,CAEJ,yDACI,sBAAA,CAOJ,uDACI,qBAAA,CAEJ,sDACI,mBAAA,CCjBZ,yBACI,sBACA,8CACA,yCAAA,CjBFA,cACI,kCAAA,CAEJ,YACI,6CAAA,CAEJ,iBACI,wBACA,4DAAA,CARJ,WACI,+BAAA,CAEJ,SACI,0CAAA,CAEJ,cACI,qBACA,yDAAA,CAAA", "file": "../customizer.min.css", "sourcesContent": ["/*!\r\n*\r\n* Template: Hope-Ui - Responsive Bootstrap 5 Admin Dashboard Template\r\n* Author: iqonic.design\r\n* Design and Developed by: iqonic.design\r\n* NOTE: This file contains the styling for color variable.\r\n*\r\n*/\r\n\r\n\r\n// Configuration\r\n@import \"./bootstrap/functions\";\r\n// Variables\r\n@import \"./hope-ui-design-system/variable\";\r\n@import \"./hope-ui-design-system/variables/index\";\r\n@import \"./bootstrap/variables\";\r\n@import \"./bootstrap/mixins\";\r\n\r\n@import \"./customizer/variables\";\r\n@import \"./customizer/reboot\";\r\n@import \"./customizer/root\";\r\n\r\n// Components\r\n@import \"./customizer/components\";\r\n@import \"./customizer/dark\";\r\n@import \"./customizer/utilities/utilities\";\r\n", "a{\r\n    color: var(--#{$variable-prefix}primary);\r\n}\r\na:hover{\r\n    color: var(--#{$variable-prefix}primary);\r\n}\r\n\r\nh1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6{\r\n    color: var(--#{$variable-prefix}primary-shade-80);\r\n}\r\n\r\nbody{\r\n    background-color: var(--bs-body-bg);\r\n}", ":root {\r\n\r\n    @mixin custom-color-variables($color-name: 'primary',$color-value: $primary) {\r\n        --#{$variable-prefix}heading-color: #{shade-color($color-value, 80%)};\r\n        --#{$variable-prefix}#{$color-name}: #{$color-value};\r\n        --#{$variable-prefix}#{$color-name}-shade-80: #{shade-color($color-value, 80%)};\r\n        --#{$variable-prefix}#{$color-name}-shade-60: #{shade-color($color-value, 60%)};\r\n        --#{$variable-prefix}#{$color-name}-shade-40: #{shade-color($color-value, 40%)};\r\n        --#{$variable-prefix}#{$color-name}-shade-20: #{shade-color($color-value, 20%)};\r\n        --#{$variable-prefix}#{$color-name}-tint-90: #{tint-color($color-value, 90%)};\r\n        --#{$variable-prefix}#{$color-name}-tint-80: #{tint-color($color-value, 80%)};\r\n        --#{$variable-prefix}#{$color-name}-tint-60: #{tint-color($color-value, 60%)};\r\n        --#{$variable-prefix}#{$color-name}-tint-40: #{tint-color($color-value, 40%)};\r\n        --#{$variable-prefix}#{$color-name}-tint-20: #{tint-color($color-value, 20%)};\r\n        --#{$variable-prefix}#{$color-name}-rgb: #{to-rgb($color-value)};\r\n    }\r\n\r\n    @include custom-color-variables('primary',$primary);\r\n    @include custom-color-variables('info',$info);\r\n\r\n    @each $color, $value in $custom-colors {\r\n        .#{$color} {\r\n            @each $name, $hax-value in $value {\r\n                @include custom-color-variables($name, $hax-value);\r\n            }\r\n        }\r\n    }\r\n\r\n}", "@each $color in $colors-name {\r\n    .text-#{$color}{\r\n        color: var(--#{$variable-prefix}#{$color}) !important;\r\n    }\r\n    .bg-#{$color}{\r\n        background-color: var(--#{$variable-prefix}#{$color}) !important;\r\n    }\r\n    .bg-soft-#{$color}{\r\n        color: var(--#{$variable-prefix}#{$color});\r\n        background-color: rgba(var(--#{$variable-prefix}#{$color}-rgb), .1) !important;\r\n    }\r\n}", ".accordion-button{\r\n    &:not(.collapsed){\r\n        color: var(--#{$variable-prefix}primary-shade-20);\r\n        background-color: var(--#{$variable-prefix}primary-tint-90);\r\n        box-shadow: inset 0 ($accordion-border-width * -1) 0, rgba(var(--#{$variable-prefix}black-rgb),0.13);\r\n    }\r\n    &:focus{\r\n        border-color: var(--#{$variable-prefix}primary-tint-40);\r\n        box-shadow: 0 .125rem $input-btn-focus-blur $input-btn-focus-width, rgba(var(--#{$variable-prefix}primary-rgb),.15);\r\n    }\r\n}", ".alert-primary {\r\n    color: var(--#{$variable-prefix}primary);\r\n    background: var(--#{$variable-prefix}primary-tint-80);\r\n    border-color: var(--#{$variable-prefix}primary);\r\n    .alert-link {\r\n        color: var(--#{$variable-prefix}primary-shade-40);\r\n    }\r\n    &.alert-solid {\r\n        color: var(--#{$variable-prefix}white);\r\n        background: var(--#{$variable-prefix}primary);\r\n        border-color: var(--#{$variable-prefix}primary);\r\n    }\r\n    &.alert-left {\r\n        background: rgba(var(--#{$variable-prefix}primary-rgb), .2);\r\n        border-color: var(--#{$variable-prefix}primary);\r\n    }\r\n}\r\n", ".iq-media-group{\r\n    .iq-icon-box-3 {\r\n        border-color: var(--#{$variable-prefix}primary);\r\n    }\r\n}", ".dropdown-item{\r\n    &:hover, &:focus{\r\n        color: var(--#{$variable-prefix}primary-shade-20);\r\n    }\r\n    &.active, &:active{\r\n        color: var(--#{$variable-prefix}white);\r\n        background-color: var(--#{$variable-prefix}primary);\r\n    }\r\n}", "@each $color in $colors-name {\r\n    .btn{\r\n        &.btn-#{$color}{\r\n            color: var(--#{$variable-prefix}white);\r\n            background-color: var(--#{$variable-prefix}#{$color});\r\n            border-color: var(--#{$variable-prefix}#{$color});\r\n            \r\n            &:hover{\r\n                color:  var(--#{$variable-prefix}white);\r\n                background-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n                border-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n            }\r\n            &:focus{\r\n                color: var(--#{$variable-prefix}white);\r\n                background-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n                border-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n            }\r\n            &:active, &.active{\r\n                color: var(--#{$variable-prefix}white);\r\n                background-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n                border-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n            }\r\n        }\r\n        &.btn-outline-#{$color}{\r\n            color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n            border-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n            &:hover{\r\n                color:  var(--#{$variable-prefix}white);\r\n                background-color: var(--#{$variable-prefix}#{$color});\r\n                border-color: var(--#{$variable-prefix}#{$color});\r\n            }\r\n            &:active, &.active{\r\n                color: var(--#{$variable-prefix}white);\r\n                background-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n                border-color: var(--#{$variable-prefix}#{$color}-shade-20);\r\n            }\r\n        }\r\n        &.btn-soft-#{$color}{\r\n            color: var(--#{$variable-prefix}#{$color});\r\n            background-color: rgba(var(--#{$variable-prefix}#{$color}-rgb), .1);\r\n            border-color: transparent;\r\n            &:hover, &:focus, &:active{\r\n                color: var(--#{$variable-prefix}#{$color}-tint-20);\r\n                background-color: rgba(var(--#{$variable-prefix}#{$color}-rgb), .2);\r\n                // border-color: rgba(var(--bs-primary-rgb), .2);\r\n                border-color: transparent;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.btn-link{\r\n    color:var(--#{$variable-prefix}primary);\r\n}\r\n.bd-aside{\r\n    .active{\r\n        color: var(--#{$variable-prefix}primary);\r\n        background-color: var(--#{$variable-prefix}primary-tint-90);\r\n    }\r\n    .btn:hover{\r\n        color: var(--#{$variable-prefix}primary);\r\n        background-color: var(--#{$variable-prefix}primary-tint-90);\r\n    }\r\n    .btn:focus{\r\n        color: var(--#{$variable-prefix}primary);\r\n        background-color: var(--#{$variable-prefix}primary-tint-90);\r\n        box-shadow: 0 0 0 0.063rem rgba(var(--#{$variable-prefix}primary-rgb),0.7);\r\n    }\r\n    a:hover{\r\n        background-color: var(--#{$variable-prefix}primary-tint-90) ;\r\n    }\r\n}", ".btn-border{\r\n    &.active{\r\n        border-color: var(--#{$variable-prefix}primary);\r\n    }\r\n}", ".list-group-item-primary{\r\n    color: var(--#{$variable-prefix}primary-shade-40);\r\n    background-color: var(--#{$variable-prefix}primary-tint-80); // 70 pe accha laag raha hai\r\n    &.list-group-item-action{\r\n        &:hover, &:focus{\r\n            color: var(--#{$variable-prefix}primary-shade-40);\r\n            background-color: var(--#{$variable-prefix}primary-tint-60);\r\n        }\r\n    }\r\n}\r\n.list-group-item{\r\n    &.active{\r\n        color: var(--#{$variable-prefix}white);\r\n        background-color: var(--#{$variable-prefix}primary);\r\n        border-color: var(--#{$variable-prefix}primary);\r\n    }\r\n}\r\n\r\n.list-group-item-action:not(.active){\r\n    &:hover, &:focus{\r\n        background-color: var(--bg-body);// to check this\r\n    }\r\n    &:active{\r\n        background-color: var(--#{$variable-prefix}primary-tint-80);\r\n    }\r\n}", ".form-control{\r\n    font-size: var(--body-font-size);\r\n    &:focus{\r\n        border-color: var(--#{$variable-prefix}primary-tint-40);\r\n    }\r\n}\r\n.form-select{\r\n    &:focus{\r\n        border-color: var(--#{$variable-prefix}primary-tint-40);\r\n    }\r\n}", ".form-check-input{\r\n    border-color: var(--#{$variable-prefix}primary-shade-20);\r\n    &:checked{\r\n        border-color: var(--#{$variable-prefix}primary-shade-20);\r\n        background-color: var(--#{$variable-prefix}primary) !important;\r\n    }\r\n    &:focus{\r\n        border-color: var(--#{$variable-prefix}primary-shade-20) !important;\r\n        background-color: var(--#{$variable-prefix}primary) !important;\r\n    }\r\n}\r\n\r\n\r\n\r\n.border-primary{\r\n    border-color: var(--#{$variable-prefix}primary) !important;\r\n}\r\n", "#top-tab-list {\r\n    li {\r\n        a {\r\n            color: var(--#{$variable-prefix}primary);\r\n            background: var(--#{$variable-prefix}primary-tint-90);\r\n            \r\n            .iq-icon {\r\n                background: var(--#{$variable-prefix}primary);\r\n            }\r\n        }\r\n        &.active {\r\n            a {\r\n                background: var(--#{$variable-prefix}primary);\r\n\r\n                .iq-icon {\r\n                    color: var(--#{$variable-prefix}primary); \r\n                }\r\n            }\r\n        }\r\n    }\r\n}", ".nav-link{\r\n    &:hover, &:focus {\r\n        // color: var(--#{$variable-prefix}primary);\r\n    }\r\n}\r\n.nav-tabs{\r\n    .nav-link{\r\n        &.active{\r\n            color:var(--#{$variable-prefix}white);\r\n            background-color: var(--#{$variable-prefix}primary);\r\n            border-color: var(--#{$variable-prefix}primary);\r\n            @if($nav-tabs-shadow){\r\n                box-shadow: 0 0.125rem 0.25rem rgba(var(--#{$variable-prefix}primary-rgb), 0.3);\r\n            }\r\n        }\r\n    }\r\n    &.nav-slider{\r\n        .nav-link{\r\n            &.active{\r\n                @if($nav-tabs-shadow){\r\n                    box-shadow: 0 0.125rem 0.25rem rgba(var(--#{$variable-prefix}primary-rgb), 0);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.nav-pills {\r\n    .nav-link{\r\n        &.active{\r\n            color: var(--#{$variable-prefix}white);\r\n            background-color: var(--#{$variable-prefix}primary);\r\n        }\r\n    }\r\n}\r\n.nav-slider{\r\n    position: relative;\r\n    .nav-link{\r\n        &.active {\r\n            background-color: transparent;\r\n        }\r\n    }\r\n    .nav-item {\r\n        z-index: 3;\r\n    }\r\n    .nav-slider-thumb {\r\n        z-index: 1 !important;\r\n        width: 100%;\r\n        color: var(--#{$variable-prefix}primary);\r\n        background: var(--#{$variable-prefix}primary);\r\n    }\r\n}\r\n", ".nav{\r\n    .search-input{\r\n        &.input-group{\r\n            &:focus-within{\r\n                .input-group-text, .form-control{\r\n                    border-color: var(--#{$variable-prefix}primary-tint-40);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .sidebar-toggle{\r\n        background: var(--#{$variable-prefix}primary);\r\n    }\r\n}\r\n.iq-navbar-header{\r\n    &.navs-bg-color{\r\n        .iq-header-img{\r\n            background-color: var(--#{$variable-prefix}primary);\r\n        }\r\n    }\r\n}\r\n\r\n.iq-navbar{\r\n     .dropdown{\r\n        .dropdown-menu {\r\n            &.sub-drop {\r\n                  .iq-sub-card{\r\n                    &:hover{\r\n                        background: var(--bs-primary-tint-90);\r\n                    }\r\n                  }\r\n                }\r\n            }\r\n        }\r\n    }\r\n", ".page-item {\r\n    &.active{\r\n        .page-link{\r\n            color:var(--#{$variable-prefix}white);\r\n            background-color: var(--#{$variable-prefix}primary);\r\n            border-color: var(--#{$variable-prefix}primary);\r\n        }\r\n    }\r\n    .page-link{\r\n        border-color: var(--bs-primary-tint-80);\r\n        color: var(--bs-primary);\r\n    }\r\n}", ".table-primary{\r\n    --bs-table-bg: var(--#{$variable-prefix}primary-tint-80);\r\n    border-color: var(--#{$variable-prefix}primary-tint-80);\r\n}", ".type {\r\n\tbackground: var(--#{$variable-prefix}primary);\r\n}\r\n.prc-box {\r\n\tbackground: var(--#{$variable-prefix}primary-tint-80);\r\n}\r\n\r\n.prc-box.active {\r\n\tbackground: var(--#{$variable-prefix}primary);\r\n\t.type {\r\n\t\tbackground: var(--#{$variable-prefix}primary-tint-80);\r\n    }\r\n}", ".circle-progress-primary{\r\n    svg{\r\n        .circle-progress-value{\r\n            stroke: var(--#{$variable-prefix}primary);\r\n        }\r\n        .circle-progress-text {\r\n            fill: var(--#{$variable-prefix}primary);\r\n        }\r\n    }\r\n}  \r\n\r\n.circle-progress-info{\r\n    svg{\r\n        .circle-progress-value{\r\n            stroke: var(--bs-info);\r\n        }\r\n        .circle-progress-text {\r\n            fill: var(--bs-info);\r\n        }\r\n    }\r\n}  ", ".fc-button-primary {\r\n    color: #fff !important;\r\n    background-color: var(--bs-primary) !important;\r\n    border-color:var(--bs-primary) !important ;\r\n   \r\n  }", ".child-cell.active {\r\n\tcolor: var(--bs-primary);\r\n    stroke: var(--bs-primary);\r\n}"]}