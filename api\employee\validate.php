<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
    exit();
}

$phone = $input['phone'] ?? '';
$employee_code = $input['employee_code'] ?? '';

// Validate required fields
if (empty($phone) || empty($employee_code)) {
    echo json_encode([
        'success' => false, 
        'message' => 'กรุณากรอกเบอร์โทรและรหัสพนักงาน'
    ]);
    exit();
}

// Clean phone number (remove spaces, dashes)
$phone = preg_replace('/[^0-9]/', '', $phone);

// Log validation attempt
error_log("Employee validation attempt - Phone: $phone, Code: $employee_code");

try {
    // Database connection
    $host = 'localhost';
    $dbname = 'hrc_system';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if employee exists with matching phone and employee_code
    $stmt = $pdo->prepare("
        SELECT id, first_name, last_name, employee_code, phone, position, department, hire_date
        FROM employees 
        WHERE phone = ? AND employee_code = ? AND line_user_id IS NULL
    ");
    
    $stmt->execute([$phone, $employee_code]);
    $employee = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($employee) {
        echo json_encode([
            'success' => true,
            'message' => 'พบข้อมูลพนักงานในระบบ สามารถลงทะเบียนได้',
            'data' => [
                'id' => $employee['id'],
                'first_name' => $employee['first_name'],
                'last_name' => $employee['last_name'],
                'employee_code' => $employee['employee_code'],
                'position' => $employee['position'],
                'department' => $employee['department'],
                'phone' => $employee['phone']
            ]
        ]);
    } else {
        // Check if employee_code exists but with different phone
        $stmt = $pdo->prepare("SELECT id FROM employees WHERE employee_code = ?");
        $stmt->execute([$employee_code]);
        $codeExists = $stmt->fetch();
        
        // Check if phone exists but with different employee_code
        $stmt = $pdo->prepare("SELECT id FROM employees WHERE phone = ?");
        $stmt->execute([$phone]);
        $phoneExists = $stmt->fetch();
        
        $errorMessage = 'ไม่พบข้อมูลที่ตรงกัน';
        
        if ($codeExists && $phoneExists) {
            $errorMessage = 'รหัสพนักงานและเบอร์โทรไม่ตรงกัน';
        } elseif ($codeExists) {
            $errorMessage = 'พบรหัสพนักงาน แต่เบอร์โทรไม่ตรงกัน';
        } elseif ($phoneExists) {
            $errorMessage = 'พบเบอร์โทร แต่รหัสพนักงานไม่ตรงกัน';
        } else {
            $errorMessage = 'ไม่พบรหัสพนักงานและเบอร์โทรในระบบ';
        }
        
        echo json_encode([
            'success' => false,
            'message' => $errorMessage
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in validate.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในการเชื่อมต่อฐานข้อมูล'
    ]);
} catch (Exception $e) {
    error_log("General error in validate.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในระบบ'
    ]);
}
?>
