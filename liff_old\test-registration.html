<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Registration Flow - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 20px;
            text-align: center;
            border: none;
        }
        
        .test-step {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .btn-test {
            border-radius: 15px;
            padding: 12px 25px;
            font-weight: 600;
            margin: 5px;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-success {
            background: #d1edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <!-- Header -->
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-flask me-2"></i>
                        ทดสอบ Registration Flow
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">ทดสอบระบบลงทะเบียนพนักงานครั้งแรก</p>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>วัตถุประสงค์:</strong> ทดสอบ flow การลงทะเบียนพนักงานใหม่ที่เข้าใช้งาน LIFF ครั้งแรก
                    </div>
                </div>
            </div>
            
            <!-- Test Steps -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">🧪 ขั้นตอนการทดสอบ</h5>
                    
                    <!-- Step 1 -->
                    <div class="test-step">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6><i class="fas fa-play-circle me-2"></i>1. ทดสอบ LIFF Initialization</h6>
                                <p class="mb-0">ตรวจสอบการเชื่อมต่อ LINE และ OAuth</p>
                            </div>
                            <div>
                                <span id="step1Status" class="status-badge status-pending">รอทดสอบ</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-test" onclick="testLiffInit()">
                                <i class="fas fa-rocket me-2"></i>ทดสอบ LIFF
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 2 -->
                    <div class="test-step">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6><i class="fas fa-user-check me-2"></i>2. ทดสอบ Registration Page</h6>
                                <p class="mb-0">เปิดหน้าลงทะเบียนและทดสอบ UI</p>
                            </div>
                            <div>
                                <span id="step2Status" class="status-badge status-pending">รอทดสอบ</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-success btn-test" onclick="openRegistrationPage()">
                                <i class="fas fa-external-link-alt me-2"></i>เปิดหน้าลงทะเบียน
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 3 -->
                    <div class="test-step">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6><i class="fas fa-database me-2"></i>3. ทดสอบ Registration API</h6>
                                <p class="mb-0">ทดสอบการเรียก API ลงทะเบียน</p>
                            </div>
                            <div>
                                <span id="step3Status" class="status-badge status-pending">รอทดสอบ</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-info btn-test" onclick="testRegistrationAPI()">
                                <i class="fas fa-code me-2"></i>ทดสอบ API
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 4 -->
                    <div class="test-step">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6><i class="fas fa-route me-2"></i>4. ทดสอบ Complete Flow</h6>
                                <p class="mb-0">ทดสอบ flow ทั้งหมดจากต้นจนจบ</p>
                            </div>
                            <div>
                                <span id="step4Status" class="status-badge status-pending">รอทดสอบ</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-warning btn-test" onclick="testCompleteFlow()">
                                <i class="fas fa-play me-2"></i>ทดสอบ Complete Flow
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Test Results -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">📊 ผลการทดสอบ</h5>
                    <div id="testResults" class="mt-3">
                        <p class="text-muted">ยังไม่มีผลการทดสอบ กรุณาเริ่มทดสอบ</p>
                    </div>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">🔗 Quick Links</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <a href="index.html" class="btn btn-outline-primary w-100 mb-2">
                                🏠 Main LIFF App
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="register.html" class="btn btn-outline-success w-100 mb-2">
                                👤 Registration Page
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="debug.html" class="btn btn-outline-info w-100 mb-2">
                                🔍 Debug Tool
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="deployment-guide.html" class="btn btn-outline-warning w-100 mb-2">
                                📤 Deploy Guide
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        let testResults = [];
        
        function updateStatus(step, status, message = '') {
            const statusElement = document.getElementById(`step${step}Status`);
            statusElement.className = `status-badge status-${status}`;
            
            switch(status) {
                case 'success':
                    statusElement.textContent = '✅ สำเร็จ';
                    break;
                case 'error':
                    statusElement.textContent = '❌ ล้มเหลว';
                    break;
                case 'pending':
                    statusElement.textContent = '⏳ กำลังทดสอบ';
                    break;
                default:
                    statusElement.textContent = 'รอทดสอบ';
            }
            
            if (message) {
                addTestResult(`Step ${step}: ${message}`);
            }
        }
        
        function addTestResult(message) {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push(`[${timestamp}] ${message}`);
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="alert alert-light py-2">${result}</div>`
            ).join('');
        }
        
        async function testLiffInit() {
            updateStatus(1, 'pending');
            
            try {
                await liff.init({ liffId: LIFF_ID });
                
                if (liff.isLoggedIn()) {
                    const profile = await liff.getProfile();
                    updateStatus(1, 'success', `LIFF เชื่อมต่อสำเร็จ - User: ${profile.displayName}`);
                } else {
                    updateStatus(1, 'error', 'ผู้ใช้ยังไม่ได้ login');
                }
                
            } catch (error) {
                updateStatus(1, 'error', `LIFF initialization ล้มเหลว: ${error.message}`);
            }
        }
        
        function openRegistrationPage() {
            updateStatus(2, 'pending');
            
            // Open registration page in new tab
            const registrationWindow = window.open('register.html', '_blank');
            
            if (registrationWindow) {
                updateStatus(2, 'success', 'เปิดหน้าลงทะเบียนสำเร็จ');
            } else {
                updateStatus(2, 'error', 'ไม่สามารถเปิดหน้าลงทะเบียนได้');
            }
        }
        
        async function testRegistrationAPI() {
            updateStatus(3, 'pending');
            
            try {
                const response = await fetch(`${API_BASE_URL}/employee/register`, {
                    method: 'OPTIONS'
                });
                
                if (response.ok) {
                    updateStatus(3, 'success', 'API endpoint พร้อมใช้งาน');
                } else {
                    updateStatus(3, 'error', `API endpoint ไม่พร้อม: ${response.status}`);
                }
                
            } catch (error) {
                updateStatus(3, 'error', `ไม่สามารถเชื่อมต่อ API: ${error.message}`);
            }
        }
        
        async function testCompleteFlow() {
            updateStatus(4, 'pending');
            
            try {
                // Test complete registration flow
                await testLiffInit();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                openRegistrationPage();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testRegistrationAPI();
                
                updateStatus(4, 'success', 'Complete flow ทดสอบเสร็จสิ้น');
                
                Swal.fire({
                    icon: 'success',
                    title: 'ทดสอบเสร็จสิ้น!',
                    text: 'Registration flow พร้อมใช้งาน',
                    confirmButtonText: 'ตกลง'
                });
                
            } catch (error) {
                updateStatus(4, 'error', `Complete flow ล้มเหลว: ${error.message}`);
            }
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('เริ่มต้นการทดสอบ Registration Flow');
        });
    </script>
</body>
</html>
