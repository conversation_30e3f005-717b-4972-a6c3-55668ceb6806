<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LIFF Debug - Direct Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            padding: 20px;
        }
        .debug-info {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .error {
            color: #f00;
        }
        .success {
            color: #0f0;
        }
        .warning {
            color: #ff0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LIFF Debug - Direct Test</h1>
        
        <div class="alert alert-info">
            <strong>วัตถุประสงค์:</strong> ทดสอบ LIFF โดยตรงโดยไม่ผ่าน cache หรือ external files
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>Configuration (Hard-coded)</h5>
            </div>
            <div class="card-body">
                <div id="configInfo" class="debug-info">Loading...</div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>LIFF Test Results</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary me-2" onclick="testLiffDirect()">Test LIFF Direct</button>
                <button class="btn btn-danger me-2" onclick="clearLogs()">Clear Logs</button>
                
                <div id="testLogs" class="debug-info mt-3">Ready for testing...</div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Instructions</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>กดปุ่ม "Test LIFF Direct" เพื่อทดสอบ</li>
                    <li>ดูผลลัพธ์ใน Test Results</li>
                    <li>ถ้ายังเกิด error ให้ตรวจสอบ LIFF settings ใน LINE Developers Console</li>
                    <li>ตรวจสอบว่า Endpoint URL ตั้งเป็น production URL (HTTPS)</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <script>
        // Hard-coded configuration to avoid any cache issues
        const LIFF_ID_HARDCODED = '2007905561-Bzx2VrZZ';
        const API_BASE_URL_HARDCODED = 'https://smartapplytech.com/hrc/api';
        
        let logMessages = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logMessages.push(`<span class="${colorClass}">[${timestamp}] ${message}</span>`);
            updateLogs();
        }
        
        function updateLogs() {
            document.getElementById('testLogs').innerHTML = logMessages.join('\n');
        }
        
        function clearLogs() {
            logMessages = [];
            updateLogs();
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const configInfo = `
LIFF_ID: ${LIFF_ID_HARDCODED}
API_BASE_URL: ${API_BASE_URL_HARDCODED}

Environment:
- User Agent: ${navigator.userAgent}
- Current URL: ${window.location.href}
- LIFF SDK Available: ${window.liff ? 'Yes' : 'No'}
- LIFF SDK Version: ${window.liff ? liff.getVersion() : 'N/A'}

Browser Info:
- Language: ${navigator.language}
- Platform: ${navigator.platform}
- Cookies Enabled: ${navigator.cookieEnabled}
            `;
            
            document.getElementById('configInfo').textContent = configInfo;
            log('Debug page loaded successfully', 'success');
        });
        
        async function testLiffDirect() {
            log('=== Starting LIFF Direct Test ===', 'warning');
            log(`Using LIFF_ID: ${LIFF_ID_HARDCODED}`, 'info');
            
            try {
                // Check LIFF SDK
                if (!window.liff) {
                    throw new Error('LIFF SDK not available');
                }
                log('✓ LIFF SDK is available', 'success');
                log(`LIFF SDK Version: ${liff.getVersion()}`, 'info');
                
                // Test LIFF initialization
                log('Attempting LIFF initialization...', 'info');
                await liff.init({ liffId: LIFF_ID_HARDCODED });
                log('✓ LIFF initialized successfully!', 'success');
                
                // Get context
                const context = liff.getContext();
                log(`LIFF Context: ${JSON.stringify(context, null, 2)}`, 'info');
                
                // Check login status
                if (liff.isLoggedIn()) {
                    log('✓ User is logged in', 'success');
                    
                    try {
                        const profile = await liff.getProfile();
                        log(`User Profile:`, 'success');
                        log(`- Display Name: ${profile.displayName}`, 'success');
                        log(`- User ID: ${profile.userId}`, 'success');
                        log(`- Picture URL: ${profile.pictureUrl}`, 'success');
                        
                        // Test API call
                        log('Testing API call...', 'info');
                        const accessToken = await liff.getAccessToken();
                        log(`Access Token: ${accessToken ? 'Available' : 'Not available'}`, accessToken ? 'success' : 'warning');
                        
                        const response = await fetch(`${API_BASE_URL_HARDCODED}/employee/profile`, {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${accessToken}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        log(`API Response Status: ${response.status}`, response.ok ? 'success' : 'warning');
                        
                        const data = await response.json();
                        log(`API Response: ${JSON.stringify(data, null, 2)}`, 'info');
                        
                    } catch (profileError) {
                        log(`✗ Failed to get profile: ${profileError.message}`, 'error');
                    }
                } else {
                    log('User is not logged in', 'warning');
                    log('You can call liff.login() to login', 'info');
                }
                
                log('=== LIFF Test Completed Successfully ===', 'success');
                
            } catch (error) {
                log(`✗ LIFF Test Failed: ${error.message}`, 'error');
                log(`Error Details: ${JSON.stringify({
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                }, null, 2)}`, 'error');
                
                // Additional debugging
                log('=== Debug Information ===', 'warning');
                log(`LIFF_ID used: ${LIFF_ID_HARDCODED}`, 'info');
                log(`LIFF_ID type: ${typeof LIFF_ID_HARDCODED}`, 'info');
                log(`LIFF_ID length: ${LIFF_ID_HARDCODED.length}`, 'info');
                
                if (error.message.includes('channel not found')) {
                    log('=== Possible Solutions ===', 'warning');
                    log('1. Check LIFF ID in LINE Developers Console', 'info');
                    log('2. Verify Channel is active and not suspended', 'info');
                    log('3. Check Endpoint URL is set to HTTPS production URL', 'info');
                    log('4. Ensure LIFF app is published/activated', 'info');
                }
            }
        }
    </script>
</body>
</html>
