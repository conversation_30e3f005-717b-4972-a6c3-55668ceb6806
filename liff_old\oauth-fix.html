<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Error Fix Guide</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .step-card {
            border-left: 4px solid #007bff;
        }
        .success-card {
            border-left: 4px solid #28a745;
        }
        .warning-card {
            border-left: 4px solid #ffc107;
        }
        .error-card {
            border-left: 4px solid #dc3545;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">🔄 OAuth Error Fix Guide</h1>
            <p class="text-white-50">แก้ไขปัญหา "code_verifier does not match"</p>
        </div>
        
        <!-- Current Status -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">✅ ความคืบหน้า</h5>
                <p class="card-text">
                    <strong>ดีมาก!</strong> LIFF ID ใช้งานได้แล้ว Error เปลี่ยนจาก "channel not found" เป็น "code_verifier does not match"<br>
                    ซึ่งหมายความว่าระบบเชื่อมต่อกับ LINE ได้แล้ว แต่มีปัญหาในขั้นตอน OAuth authentication
                </p>
            </div>
        </div>
        
        <!-- Problem Analysis -->
        <div class="card error-card">
            <div class="card-body">
                <h5 class="card-title text-danger">🚨 ปัญหาปัจจุบัน</h5>
                <div class="code-block">
Error: code_verifier does not match
POST https://api.line.me/oauth2/v2.1/token 400 (Bad Request)
                </div>
                <p><strong>สาเหตุ:</strong> OAuth PKCE (Proof Key for Code Exchange) verification ล้มเหลว</p>
            </div>
        </div>
        
        <!-- Solution Steps -->
        <div class="card step-card">
            <div class="card-body">
                <h5 class="card-title text-primary">🔧 วิธีแก้ไข (ลำดับความสำคัญ)</h5>
                
                <div class="accordion" id="solutionAccordion">
                    <!-- Solution 1 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#solution1">
                                1. ล้าง Browser Cache และ Cookies (แนะนำมากที่สุด)
                            </button>
                        </h2>
                        <div id="solution1" class="accordion-collapse collapse show" data-bs-parent="#solutionAccordion">
                            <div class="accordion-body">
                                <h6>วิธีที่ 1: ใช้ปุ่มในแอป</h6>
                                <p>หากเห็นปุ่ม "ล้าง Cache และลองใหม่" ให้กดปุ่มนั้น</p>
                                
                                <h6>วิธีที่ 2: Manual Clear (Chrome)</h6>
                                <ol>
                                    <li>กด <kbd>Ctrl+Shift+Delete</kbd> (Windows) หรือ <kbd>Cmd+Shift+Delete</kbd> (Mac)</li>
                                    <li>เลือก "All time" หรือ "ตลอดเวลา"</li>
                                    <li>เลือก "Cookies and other site data" และ "Cached images and files"</li>
                                    <li>กด "Clear data"</li>
                                    <li>รีโหลดหน้าเว็บ</li>
                                </ol>
                                
                                <h6>วิธีที่ 3: Developer Tools</h6>
                                <ol>
                                    <li>กด <kbd>F12</kbd> เปิด Developer Tools</li>
                                    <li>ไปที่แท็บ "Application" หรือ "Storage"</li>
                                    <li>ล้าง Local Storage, Session Storage, และ Cookies</li>
                                    <li>รีโหลดหน้าเว็บ</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Solution 2 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution2">
                                2. ตรวจสอบ Endpoint URL
                            </button>
                        </h2>
                        <div id="solution2" class="accordion-collapse collapse" data-bs-parent="#solutionAccordion">
                            <div class="accordion-body">
                                <p><strong>ปัญหา:</strong> URL ที่เข้าถึงไม่ตรงกับ Endpoint URL ที่ตั้งใน LIFF</p>
                                
                                <h6>ขั้นตอนการตรวจสอบ:</h6>
                                <ol>
                                    <li>เข้า <a href="https://developers.line.biz/console/" target="_blank">LINE Developers Console</a></li>
                                    <li>เลือก Provider และ Channel</li>
                                    <li>ไปที่แท็บ "LIFF"</li>
                                    <li>ตรวจสอบ Endpoint URL ของ LIFF app</li>
                                </ol>
                                
                                <div class="alert alert-warning">
                                    <strong>⚠️ สำคัญ:</strong><br>
                                    - URL ต้องตรงกันทุกตัวอักษร<br>
                                    - รวมถึง https:// และ trailing slash (/)<br>
                                    - ตัวอย่าง: <code>https://smartapplytech.com/hrc/liff/</code>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Solution 3 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution3">
                                3. ใช้ Incognito/Private Mode
                            </button>
                        </h2>
                        <div id="solution3" class="accordion-collapse collapse" data-bs-parent="#solutionAccordion">
                            <div class="accordion-body">
                                <p>ทดสอบใน Private browsing mode เพื่อหลีกเลี่ยง cache issues</p>
                                
                                <h6>วิธีเปิด:</h6>
                                <ul>
                                    <li><strong>Chrome:</strong> <kbd>Ctrl+Shift+N</kbd></li>
                                    <li><strong>Firefox:</strong> <kbd>Ctrl+Shift+P</kbd></li>
                                    <li><strong>Safari:</strong> <kbd>Cmd+Shift+N</kbd></li>
                                    <li><strong>Edge:</strong> <kbd>Ctrl+Shift+N</kbd></li>
                                </ul>
                                
                                <p>จากนั้นเข้า URL เดิมใน Incognito window</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Solution 4 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution4">
                                4. ตรวจสอบ LIFF Settings
                            </button>
                        </h2>
                        <div id="solution4" class="accordion-collapse collapse" data-bs-parent="#solutionAccordion">
                            <div class="accordion-body">
                                <p>ตรวจสอบการตั้งค่า LIFF ใน LINE Developers Console</p>
                                
                                <h6>การตั้งค่าที่แนะนำ:</h6>
                                <div class="code-block">
LIFF app name: HR Center Employee Portal
Size: Full
Endpoint URL: https://smartapplytech.com/hrc/liff/
Scope: profile openid
Bot link feature: On (แนะนำ)
                                </div>
                                
                                <div class="alert alert-info">
                                    <strong>💡 เคล็ดลับ:</strong><br>
                                    หากยังมีปัญหา ลองสร้าง LIFF app ใหม่ด้วยการตั้งค่าเดียวกัน
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Solution 5 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution5">
                                5. ทดสอบใน LINE App
                            </button>
                        </h2>
                        <div id="solution5" class="accordion-collapse collapse" data-bs-parent="#solutionAccordion">
                            <div class="accordion-body">
                                <p>LIFF ทำงานได้ดีที่สุดใน LINE app</p>
                                
                                <h6>วิธีทดสอบ:</h6>
                                <ol>
                                    <li>เปิด LINE app บนมือถือ</li>
                                    <li>ส่ง URL ให้ตัวเองใน chat</li>
                                    <li>กด URL ใน LINE chat</li>
                                    <li>จะเปิดใน LINE's internal browser</li>
                                </ol>
                                
                                <div class="alert alert-success">
                                    <strong>✅ ข้อดี:</strong><br>
                                    - ไม่มี CORS issues<br>
                                    - OAuth flow ทำงานได้ดีกว่า<br>
                                    - Access token ได้โดยตรง
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card warning-card">
            <div class="card-body">
                <h5 class="card-title text-warning">⚡ Quick Actions</h5>
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-primary w-100 mb-2" onclick="clearCacheManual()">
                            🗑️ Clear Cache
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-info w-100 mb-2" onclick="openIncognito()">
                            🕵️ Incognito Guide
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="https://developers.line.biz/console/" target="_blank" class="btn btn-success w-100 mb-2">
                            🔗 LINE Console
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="debug.html" class="btn btn-secondary w-100 mb-2">
                            🔍 Debug Tool
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Expected Result -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">🎯 ผลลัพธ์ที่คาดหวัง</h5>
                <p>หลังจากทำตามขั้นตอนแล้ว ควรเห็น:</p>
                <ul>
                    <li>✅ LIFF initialized successfully</li>
                    <li>✅ User is logged in</li>
                    <li>✅ Got user profile</li>
                    <li>✅ Employee data loaded successfully</li>
                    <li>✅ แสดงหน้า main content</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function clearCacheManual() {
            const instructions = `
🗑️ วิธีล้าง Cache Manual:

Chrome/Edge:
1. กด Ctrl+Shift+Delete
2. เลือก "All time"
3. เลือก "Cookies" และ "Cached images"
4. กด "Clear data"

Firefox:
1. กด Ctrl+Shift+Delete
2. เลือก "Everything"
3. เลือก "Cookies" และ "Cache"
4. กด "Clear Now"

Safari:
1. กด Cmd+Option+E
2. หรือ Safari > Clear History...
3. เลือก "All History"

หลังจากล้างแล้ว:
- ปิด browser ทั้งหมด
- เปิดใหม่
- เข้า URL อีกครั้ง
            `;
            
            alert(instructions);
        }
        
        function openIncognito() {
            const instructions = `
🕵️ วิธีเปิด Incognito/Private Mode:

Chrome: Ctrl+Shift+N
Firefox: Ctrl+Shift+P  
Safari: Cmd+Shift+N
Edge: Ctrl+Shift+N

จากนั้น:
1. Copy URL นี้: ${window.location.href}
2. Paste ใน Incognito window
3. ทดสอบ LIFF app

ข้อดีของ Incognito:
- ไม่มี cache เก่า
- ไม่มี cookies เก่า
- เริ่มต้นใหม่ทั้งหมด
            `;
            
            alert(instructions);
        }
    </script>
</body>
</html>
