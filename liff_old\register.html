<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ลงทะเบียนพนักงาน - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        
        .registration-container {
            max-width: 500px;
            margin: 0 auto;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 30px;
            text-align: center;
            border: none;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .loading-spinner {
            display: none;
        }
        
        .user-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
            border: 4px solid white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: 15px 0 0 15px;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 15px 15px 0;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="registration-container">
            <!-- Loading Screen -->
            <div id="loadingScreen" class="text-center text-white">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">กำลังเชื่อมต่อกับ LINE...</p>
            </div>
            
            <!-- Registration Form -->
            <div id="registrationForm" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">
                                    <i class="fas fa-user-plus me-2"></i>
                                    ลงทะเบียนพนักงาน
                                </h3>
                                <p class="mb-0 mt-2 opacity-75">ครั้งแรกที่เข้าใช้งานระบบ</p>
                            </div>
                            <button class="btn btn-outline-light btn-sm" onclick="logout()" title="ออกจากระบบ">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- User Info from LINE -->
                        <div id="userInfo" class="user-info">
                            <img id="userAvatar" src="" alt="Profile" class="user-avatar">
                            <h5 id="userName" class="mb-1"></h5>
                            <small class="text-muted">ข้อมูลจาก LINE</small>
                        </div>
                        
                        <!-- Registration Form -->
                        <form id="employeeRegistrationForm">
                            <div class="mb-4">
                                <label for="employeeCode" class="form-label">
                                    <i class="fas fa-id-badge me-2"></i>รหัสพนักงาน
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-hashtag"></i>
                                    </span>
                                    <input type="text" class="form-control" id="employeeCode" 
                                           placeholder="ระบุรหัสพนักงาน เช่น EMP001" required>
                                </div>
                                <div class="form-text">รหัสพนักงานที่ได้รับจากฝ่ายบุคคล</div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-2"></i>เบอร์โทรศัพท์
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-mobile-alt"></i>
                                    </span>
                                    <input type="tel" class="form-control" id="phone" 
                                           placeholder="************" pattern="0[6-9][0-9]{8}" required>
                                </div>
                                <div class="form-text">เบอร์โทรศัพท์ที่ลงทะเบียนกับบริษัท</div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>หมายเหตุ:</strong> ระบบจะตรวจสอบข้อมูลกับฐานข้อมูลพนักงาน 
                                หากข้อมูลไม่ตรงกัน กรุณาติดต่อฝ่ายบุคคล
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <span class="btn-text">
                                        <i class="fas fa-check me-2"></i>ลงทะเบียน
                                    </span>
                                    <span class="loading-spinner">
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                        กำลังลงทะเบียน...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Error Display -->
            <div id="errorAlert" class="alert alert-danger mt-3" style="display: none;">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span id="errorMessage"></span>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        let currentUser = null;
        
        // Initialize LIFF
        document.addEventListener('DOMContentLoaded', function() {
            initializeLiff();
        });
        
        async function initializeLiff() {
            try {
                console.log('Initializing LIFF for registration...');
                
                await liff.init({ liffId: LIFF_ID });
                
                if (!liff.isLoggedIn()) {
                    liff.login();
                    return;
                }
                
                // Get user profile
                const profile = await liff.getProfile();
                currentUser = profile;
                
                console.log('User profile:', profile);
                
                // Display user info
                displayUserInfo(profile);
                
                // Show registration form
                document.getElementById('loadingScreen').style.display = 'none';
                document.getElementById('registrationForm').style.display = 'block';
                
            } catch (error) {
                console.error('LIFF initialization failed:', error);
                showError('ไม่สามารถเชื่อมต่อกับ LINE ได้ กรุณาลองใหม่อีกครั้ง');
            }
        }
        
        function displayUserInfo(profile) {
            document.getElementById('userAvatar').src = profile.pictureUrl || 'https://via.placeholder.com/80/667eea/ffffff?text=👤';
            document.getElementById('userName').textContent = profile.displayName || 'ผู้ใช้ LINE';
        }
        
        // Handle form submission
        document.getElementById('employeeRegistrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const employeeCode = document.getElementById('employeeCode').value.trim();
            const phone = document.getElementById('phone').value.trim();
            
            if (!employeeCode || !phone) {
                showError('กรุณากรอกข้อมูลให้ครบถ้วน');
                return;
            }
            
            // Validate phone format
            const phonePattern = /^0[6-9]\d{8}$/;
            if (!phonePattern.test(phone)) {
                showError('รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง (ตัวอย่าง: ************)');
                return;
            }
            
            await registerEmployee(employeeCode, phone);
        });
        
        async function registerEmployee(employeeCode, phone) {
            try {
                showLoading(true);
                hideError();
                
                const accessToken = await liff.getAccessToken();
                
                const response = await fetch(`${API_BASE_URL}/employee/register.php`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        employee_code: employeeCode,
                        phone: phone
                    })
                });
                
                console.log('Registration response status:', response.status);
                console.log('Registration response headers:', Object.fromEntries(response.headers.entries()));

                let data;
                try {
                    data = await response.json();
                    console.log('Registration response data:', data);
                } catch (jsonError) {
                    console.error('Failed to parse JSON response:', jsonError);
                    const responseText = await response.clone().text();
                    console.log('Response text:', responseText.substring(0, 500));
                    showError('Server ตอบกลับด้วยข้อมูลที่ไม่ใช่ JSON กรุณาติดต่อผู้ดูแลระบบ');
                    return;
                }

                if (data.success) {
                    // Registration successful
                    await Swal.fire({
                        icon: 'success',
                        title: 'ลงทะเบียนสำเร็จ!',
                        text: data.message,
                        confirmButtonText: 'เข้าสู่ระบบ'
                    });

                    // Redirect to main app
                    window.location.href = 'index.html';

                } else {
                    console.error('Registration failed:', data);
                    showError(data.message || 'เกิดข้อผิดพลาดในการลงทะเบียน');
                }
                
            } catch (error) {
                console.error('Registration error:', error);

                // Show detailed error for debugging
                const errorMessage = `
                    เกิดข้อผิดพลาดในการลงทะเบียน<br><br>
                    <strong>Error Details:</strong><br>
                    ${error.message}<br><br>
                    <strong>Debug Info:</strong><br>
                    - Employee Code: ${employeeCode}<br>
                    - Phone: ${phone}<br>
                    - LINE User: ${currentUser?.displayName || 'Unknown'}<br>
                    - LINE ID: ${currentUser?.userId || 'Unknown'}<br><br>
                    กรุณาตรวจสอบข้อมูลและลองใหม่อีกครั้ง
                `;

                showError(errorMessage);
            } finally {
                showLoading(false);
            }
        }
        
        function showLoading(show) {
            const btnText = document.querySelector('.btn-text');
            const loadingSpinner = document.querySelector('.loading-spinner');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            if (show) {
                btnText.style.display = 'none';
                loadingSpinner.style.display = 'inline';
                submitBtn.disabled = true;
            } else {
                btnText.style.display = 'inline';
                loadingSpinner.style.display = 'none';
                submitBtn.disabled = false;
            }
        }
        
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorAlert').style.display = 'block';
        }
        
        function hideError() {
            document.getElementById('errorAlert').style.display = 'none';
        }
        
        // Format phone number input
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 10) {
                value = value.substring(0, 10);
            }
            e.target.value = value;
        });
        
        // Format employee code input
        document.getElementById('employeeCode').addEventListener('input', function(e) {
            e.target.value = e.target.value.toUpperCase();
        });

        // Enhanced logout function with better SweetAlert2
        async function logout() {
            try {
                const result = await Swal.fire({
                    title: 'ออกจากระบบ?',
                    text: 'คุณต้องการออกจากระบบ HR Center หรือไม่?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: '<i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ',
                    cancelButtonText: '<i class="fas fa-times me-2"></i>ยกเลิก',
                    reverseButtons: true,
                    focusCancel: true,
                    customClass: {
                        popup: 'animated fadeIn',
                        confirmButton: 'btn btn-danger',
                        cancelButton: 'btn btn-secondary'
                    },
                    buttonsStyling: false
                });

                if (result.isConfirmed) {
                    // Show enhanced loading
                    Swal.fire({
                        title: 'กำลังออกจากระบบ...',
                        html: `
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                                <br>
                                <p class="mb-0">กรุณารอสักครู่...</p>
                            </div>
                        `,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Small delay for better UX
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    // LIFF logout
                    if (liff.isLoggedIn()) {
                        liff.logout();
                    }

                    // Show enhanced success message
                    await Swal.fire({
                        icon: 'success',
                        title: 'ออกจากระบบแล้ว',
                        html: `
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                                <br>
                                <p class="mb-0">คุณได้ออกจากระบบ HR Center เรียบร้อยแล้ว</p>
                            </div>
                        `,
                        timer: 2500,
                        timerProgressBar: true,
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        customClass: {
                            popup: 'animated fadeIn'
                        }
                    });

                    // Redirect
                    if (liff.isInClient()) {
                        liff.closeWindow();
                    } else {
                        window.location.reload();
                    }
                }

            } catch (error) {
                console.error('Logout error:', error);

                // Enhanced error message
                Swal.fire({
                    icon: 'error',
                    title: 'เกิดข้อผิดพลาด',
                    html: `
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                            <br>
                            <p class="mb-0">ไม่สามารถออกจากระบบได้ กรุณาลองใหม่อีกครั้ง</p>
                        </div>
                    `,
                    confirmButtonText: '<i class="fas fa-redo me-2"></i>ลองใหม่',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    },
                    buttonsStyling: false
                });
            }
        }
    </script>
</body>
</html>
