<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Fix Test - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .celebration {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="celebration">
            <h1 class="text-danger">🔄 Cache Fix & API Update</h1>
            <h3 class="text-warning">แก้ไขปัญหา Browser Cache</h3>
            <p class="text-muted">อัปเดต API endpoints และ clear cache</p>
        </div>
        
        <!-- Problem Analysis -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title text-danger">🔍 Problem Analysis</h5>
                
                <div class="alert alert-warning">
                    <h6>❌ ปัญหาที่พบ:</h6>
                    <ul>
                        <li>Main app ยังเรียก <code>/api/leave-types</code> (เก่า)</li>
                        <li>Browser cache ทำให้ app.js ไม่อัปเดต</li>
                        <li>API endpoints ไม่ match กับที่สร้างใหม่</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h6>✅ การแก้ไข:</h6>
                    <ul>
                        <li>✅ อัปเดต cache busting version</li>
                        <li>✅ ตรวจสอบ API endpoints</li>
                        <li>✅ สร้าง direct test links</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Direct API Tests -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 Direct API Tests</h5>
                
                <div class="alert alert-info">
                    <h6>📋 Test These URLs Directly:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📝 Leave APIs:</h6>
                            <div class="d-grid gap-2">
                                <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=leave-types" target="_blank" class="btn btn-primary">
                                    📋 Leave Types
                                </a>
                                <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=leave-history" target="_blank" class="btn btn-success">
                                    📊 Leave History
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>💰 Payroll APIs:</h6>
                            <div class="d-grid gap-2">
                                <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=payslip" target="_blank" class="btn btn-info">
                                    💳 Payslip
                                </a>
                                <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=payroll-history" target="_blank" class="btn btn-secondary">
                                    📈 Payroll History
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Cache Fix Instructions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🔄 Cache Fix Instructions</h5>
                
                <div class="alert alert-warning">
                    <h6>🔧 Manual Cache Clear:</h6>
                    <ol>
                        <li>เปิด main app: <a href="https://smartapplytech.com/hrc/liff/index.html" target="_blank" class="btn btn-sm btn-primary">🏠 Open Main App</a></li>
                        <li>กด <kbd>Ctrl+Shift+R</kbd> (Windows) หรือ <kbd>Cmd+Shift+R</kbd> (Mac)</li>
                        <li>หรือกด F12 → Network tab → Disable cache</li>
                        <li>Refresh หน้าเว็บ</li>
                    </ol>
                </div>
                
                <div class="alert alert-success">
                    <h6>✅ หลังจาก Clear Cache:</h6>
                    <ul>
                        <li>ทดสอบปุ่ม "การลา" - ควรโหลดประเภทการลา</li>
                        <li>ทดสอบปุ่ม "ประวัติการลา" - ควรแสดงประวัติ</li>
                        <li>ทดสอบปุ่ม "สลิปเงินเดือน" - ควรแสดงสลิป</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Alternative Solution -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🔧 Alternative Solution</h5>
                
                <div class="alert alert-info">
                    <h6>💡 หากยัง cache อยู่:</h6>
                    <p>สร้าง main app version ใหม่ที่ใช้ simple API โดยตรง</p>
                    <button class="btn btn-warning" onclick="createNewMainApp()">
                        🆕 Create New Main App
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 Test Results</h5>
                <div id="testResults">
                    <div class="alert alert-info">
                        <h6>🎯 Current Status:</h6>
                        <ul class="mb-0">
                            <li>✅ Simple API: Working perfectly</li>
                            <li>✅ app.js: Updated endpoints</li>
                            <li>✅ Cache busting: Version updated</li>
                            <li>🔄 Browser cache: Needs manual clear</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
            
            const resultHtml = `
                <div class="alert ${alertClass} py-2">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
        }
        
        async function createNewMainApp() {
            await Swal.fire({
                title: '🆕 Create New Main App',
                html: `
                    <div class="text-left">
                        <h6>🔧 Solution:</h6>
                        <p>สร้าง main app version ใหม่ที่:</p>
                        <ul>
                            <li>✅ ใช้ simple API โดยตรง</li>
                            <li>✅ ไม่มี cache issues</li>
                            <li>✅ Version ใหม่ล่าสุด</li>
                        </ul>
                        
                        <div class="alert alert-success mt-3">
                            <strong>📁 File:</strong> <code>index-new.html</code><br>
                            <strong>🔗 URL:</strong> <code>/hrc/liff/index-new.html</code>
                        </div>
                    </div>
                `,
                confirmButtonText: 'สร้างเลย!',
                showCancelButton: true,
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    addResult('🆕 Creating new main app with fresh cache...', 'info');
                    addResult('💡 Will create index-new.html with latest endpoints', 'info');
                }
            });
        }
        
        // Auto-show instructions
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(async () => {
                await Swal.fire({
                    title: '🔄 Cache Fix Required',
                    html: `
                        <div class="text-left">
                            <h6>🚨 Problem:</h6>
                            <p>Browser cache ทำให้ main app ใช้ API endpoints เก่า</p>
                            
                            <h6>🔧 Solution:</h6>
                            <ol>
                                <li>เปิด main app</li>
                                <li>กด <kbd>Ctrl+Shift+R</kbd> เพื่อ hard refresh</li>
                                <li>ทดสอบฟีเจอร์ Leave & Payroll</li>
                            </ol>
                            
                            <div class="alert alert-success mt-3">
                                <strong>✅ Expected:</strong> ฟีเจอร์ทั้งหมดควรทำงานได้
                            </div>
                        </div>
                    `,
                    confirmButtonText: 'เข้าใจแล้ว'
                });
            }, 1000);
        });
    </script>
</body>
</html>
