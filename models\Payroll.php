<?php
/**
 * Payroll Model
 * Human Resources Center System
 */

class Payroll {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all payroll records
     */
    public function getAll($limit = null, $offset = 0, $employeeId = null, $month = null, $year = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT p.*, e.employee_code, e.first_name, e.last_name, e.position, e.department,
                           u.username as created_by_username
                    FROM payroll p
                    JOIN employees e ON p.employee_id = e.id
                    LEFT JOIN users u ON p.created_by = u.id
                    WHERE 1=1";
            $params = [];
            
            if ($employeeId) {
                $sql .= " AND p.employee_id = ?";
                $params[] = $employeeId;
            }
            
            if ($month) {
                $sql .= " AND MONTH(p.pay_period_start) = ?";
                $params[] = $month;
            }
            
            if ($year) {
                $sql .= " AND YEAR(p.pay_period_start) = ?";
                $params[] = $year;
            }
            
            $sql .= " ORDER BY p.pay_period_start DESC, e.employee_code";
            
            if ($limit) {
                $sql .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get payrolls error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get payroll by ID
     */
    public function getById($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT p.*, e.employee_code, e.first_name, e.last_name, e.position, e.department,
                           s.basic_salary, s.allowances, s.overtime_rate
                    FROM payroll p
                    JOIN employees e ON p.employee_id = e.id
                    LEFT JOIN salaries s ON p.employee_id = s.employee_id AND s.is_active = 1
                    WHERE p.id = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get payroll by ID error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Create payroll record
     */
    public function create($data) {
        try {
            $pdo = $this->db->getConnection();
            
            // Calculate totals
            $grossPay = $data['basic_salary'] + $data['allowances'] + $data['overtime_pay'] + $data['bonus'];
            $totalDeductions = $data['tax'] + $data['social_security'] + $data['other_deductions'];
            $netPay = $grossPay - $totalDeductions;
            
            $sql = "INSERT INTO payroll (
                        employee_id, pay_period_start, pay_period_end, pay_date,
                        basic_salary, allowances, overtime_pay, bonus,
                        gross_pay, tax_deduction, social_security_deduction, other_deductions,
                        net_pay, status, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['employee_id'],
                $data['pay_period_start'],
                $data['pay_period_end'],
                $data['pay_date'],
                $data['basic_salary'],
                $data['allowances'] ?? 0,
                $data['overtime_pay'] ?? 0,
                $data['bonus'] ?? 0,
                $grossPay,
                $data['tax'] ?? 0,
                $data['social_security'] ?? 0,
                $data['other_deductions'] ?? 0,
                $netPay,
                $data['status'] ?? 'draft',
                getCurrentUserId()
            ]);
            
            if ($result) {
                $payrollId = $pdo->lastInsertId();
                
                error_log("Payroll created for employee ID: {$data['employee_id']}, payroll_id: {$payrollId}");
                
                return ['success' => true, 'id' => $payrollId, 'message' => 'Payroll created successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to create payroll'];
            
        } catch (Exception $e) {
            error_log("Create payroll error: " . $e->getMessage());
            error_log("Create payroll error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to create payroll: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update payroll record
     */
    public function update($id, $data) {
        try {
            $pdo = $this->db->getConnection();
            
            // Calculate totals
            $grossPay = $data['basic_salary'] + $data['allowances'] + $data['overtime_pay'] + $data['bonus'];
            $totalDeductions = $data['tax'] + $data['social_security'] + $data['other_deductions'];
            $netPay = $grossPay - $totalDeductions;
            
            $sql = "UPDATE payroll SET
                        pay_period_start = ?, pay_period_end = ?, pay_date = ?,
                        basic_salary = ?, allowances = ?, overtime_hours = ?, overtime_pay = ?, bonus = ?,
                        gross_pay = ?, tax_deduction = ?, social_security_deduction = ?, other_deductions = ?, net_pay = ?,
                        status = ?, updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['pay_period_start'],
                $data['pay_period_end'],
                $data['pay_date'],
                $data['basic_salary'],
                $data['allowances'] ?? 0,
                $data['overtime_pay'] ?? 0,
                $data['bonus'] ?? 0,
                $grossPay,
                $data['tax'] ?? 0,
                $data['social_security'] ?? 0,
                $data['other_deductions'] ?? 0,
                $netPay,
                $data['status'] ?? 'draft',
                $id
            ]);
            
            if ($result) {
                error_log("Payroll updated: ID {$id}");
                
                return ['success' => true, 'message' => 'Payroll updated successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to update payroll'];
            
        } catch (Exception $e) {
            error_log("Update payroll error: " . $e->getMessage());
            error_log("Update payroll error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to update payroll: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete payroll record
     */
    public function delete($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "DELETE FROM payroll WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                error_log("Payroll deleted: ID {$id}");
                
                return ['success' => true, 'message' => 'Payroll deleted successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to delete payroll'];
            
        } catch (Exception $e) {
            error_log("Delete payroll error: " . $e->getMessage());
            error_log("Delete payroll error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to delete payroll: ' . $e->getMessage()];
        }
    }
    
    /**
     * Count payroll records
     */
    public function count($employeeId = null, $month = null, $year = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT COUNT(*) FROM payroll p WHERE 1=1";
            $params = [];
            
            if ($employeeId) {
                $sql .= " AND p.employee_id = ?";
                $params[] = $employeeId;
            }
            
            if ($month) {
                $sql .= " AND MONTH(p.pay_period_start) = ?";
                $params[] = $month;
            }
            
            if ($year) {
                $sql .= " AND YEAR(p.pay_period_start) = ?";
                $params[] = $year;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log("Count payrolls error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Approve payroll
     */
    public function approve($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE payroll SET status = 'approved', pay_date = CURDATE() WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                error_log("Payroll approved: ID {$id}");
                
                return ['success' => true, 'message' => 'Payroll approved successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to approve payroll'];
            
        } catch (Exception $e) {
            error_log("Approve payroll error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to approve payroll: ' . $e->getMessage()];
        }
    }
}
