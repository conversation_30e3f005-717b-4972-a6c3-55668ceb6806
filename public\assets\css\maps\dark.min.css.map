{"version": 3, "sources": ["dark.scss", "hope-ui-design-system/helper/_reboot.scss", "hope-ui-design-system/helper/mixins/_background_varient.scss", "dark/helper/utilities/_background.scss", "dark/helper/utilities/_avatar.scss", "dark/_dark.scss", "dark/reboot/_reboot.scss", "hope-ui-design-system/_variable.scss", "dark/components/accordion/_custom-accordion.scss", "dark/components/button/_border-button.scss", "dark/components/button/_soft-btn.scss", "bootstrap/mixins/_buttons.scss", "dark/components/close/_close.scss", "dark/layout-style/dashboard_menu_style/_boxed.scss", "dark/layout-style/dashboard_menu_style/_dual-compact.scss", "dark/layout-style/dashboard_menu_style/_dual-horizontal.scss", "dark/layout-style/menu_style/_default-sidebar.scss", "dark/layout-style/menu_style/_nav-color.scss", "hope-ui-design-system/variables/_navbar-vertical.scss", "dark/layout-style/menu_style/_default_style.scss", "dark/components/footer/_footer.scss", "dark/components/nav/_navbar.scss", "bootstrap/mixins/_breakpoints.scss", "dark/components/alert/_bs-alert.scss", "bootstrap/mixins/_alert.scss", "dark/components/card/_card.scss", "dark/components/chart/_chart.scss", "dark/components/card/_icon-box.scss", "dark/components/form/_form-control.scss", "dark/components/form/_form-wizard.scss", "dark/components/table/_table.scss", "dark/components/dropdown/_dropdown.scss", "dark/components/profile/_profile.scss", "dark/components/timeline/_timeline-mega.scss", "dark/components/widgets/_credit-card.scss", "dark/components/progressbar/_progressbar.scss", "dark/components/pagination/_pagination.scss", "dark/components/popover/_popover.scss", "dark/pages/kanban/_kanban.scss", "dark/pages/pricing/_pricing.scss", "dark/pages/auth/_authentication.scss", "dark/plugins/_chart.scss", "dark/plugins/_full-calendar.scss", "dark/layout-style/_boxed.scss"], "names": [], "mappings": "AAAA;;;;;;;EAAA,OCCC,YAAA,CAED,GACI,cACA,cACA,8BACA,SACA,WAAA,CCOF,uBACE,gDAAA,CAAA,kIAKE,eACA,cACA,iDAAA,CARJ,yBACE,gDAAA,CAAA,0IAKE,eACA,cACA,iDAAA,CARJ,uBACE,+CAAA,CAAA,kIAKE,eACA,cACA,gDAAA,CARJ,oBACE,8CAAA,CAAA,sHAKE,eACA,cACA,+CAAA,CARJ,uBACE,+CAAA,CAAA,kIAKE,eACA,cACA,gDAAA,CARJ,sBACE,8CAAA,CAAA,8HAKE,eACA,cACA,+CAAA,CARJ,qBACE,gDAAA,CAAA,0HAKE,eACA,cACA,iDAAA,CARJ,oBACE,+CAAA,CAAA,sHAKE,eACA,WACA,gDAAA,CARJ,oBACE,gDAAA,CAAA,sHAKE,eACA,cACA,iDAAA,CARJ,yBACE,+CAAA,CAAA,0IAKE,eACA,cACA,gDAAA,CCdL,oBACC,wBAAA,CADD,sBACC,wBAAA,CADD,oBACC,wBAAA,CADD,iBACC,wBAAA,CADD,oBACC,wBAAA,CADD,mBACC,wBAAA,CADD,kBACC,wBAAA,CADD,iBACC,wBAAA,CADD,iBACC,wBAAA,CADD,sBACC,wBAAA,CCTE,qCACI,kBCEE,CCJV,MACI,cACA,mCAAA,CAEJ,kHACI,aDFS,CCIb,cACI,+BAAA,CAEJ,qBACI,+BAAA,CAEJ,yBACI,wBDVM,CCWN,2CACI,wBDZE,CCeF,sDACI,yBACA,aDlBC,CCsBb,+DACI,qBACA,aDxBS,CC0Bb,iBACI,yBACA,aD5BS,CC+BT,2BACI,aCxBG,CD2BX,uBACI,yIAAA,mGAAA,8FAAA,2FAAA,CAEJ,oBACI,2IAAA,kGAAA,6FAAA,4FAAA,CEzCA,0CACI,aAAA,CAEJ,wCACI,kBHDE,CGKN,iCACI,yBACA,UDHG,CCKC,qEACI,yBACA,UDPL,CCQK,qFACI,WACA,wBHbN,CGeE,2EACI,yBAAA,gBAAA,CCrBpB,kBACI,wBAAA,CACA,wBACI,oBAAA,CAEJ,yBACI,8BAAA,CAIJ,sGACI,WACA,yBACA,yBAAA,gBAAA,CCXJ,wBCkBF,wBACA,oCACA,4CACA,8BACA,0CACA,oDACA,wCACA,+BACA,2CACA,qDACA,mDACA,8BACA,8BACA,qDD1BM,yBAAA,gBAAA,CALJ,0BCkBF,wBACA,qCACA,6CACA,8BACA,2CACA,qDACA,yCACA,+BACA,4CACA,sDACA,mDACA,8BACA,8BACA,sDD1BM,yBAAA,gBAAA,CALJ,wBCkBF,wBACA,oCACA,4CACA,8BACA,0CACA,oDACA,wCACA,+BACA,2CACA,qDACA,mDACA,8BACA,8BACA,qDD1BM,yBAAA,gBAAA,CALJ,qBCkBF,wBACA,oCACA,4CACA,8BACA,0CACA,oDACA,wCACA,+BACA,2CACA,qDACA,mDACA,8BACA,8BACA,qDD1BM,yBAAA,gBAAA,CALJ,wBCkBF,wBACA,qCACA,6CACA,8BACA,2CACA,qDACA,wCACA,+BACA,4CACA,sDACA,mDACA,8BACA,8BACA,sDD1BM,yBAAA,gBAAA,CALJ,uBCkBF,wBACA,oCACA,4CACA,8BACA,0CACA,oDACA,uCACA,+BACA,2CACA,qDACA,mDACA,8BACA,8BACA,qDD1BM,yBAAA,gBAAA,CALJ,sBCkBF,wBACA,sCACA,8CACA,8BACA,4CACA,sDACA,yCACA,+BACA,6CACA,uDACA,mDACA,8BACA,8BACA,uDD1BM,yBAAA,gBAAA,CALJ,qBCkBF,wBACA,mCACA,2CACA,8BACA,yCACA,mDACA,sCACA,+BACA,0CACA,oDACA,mDACA,iCACA,8BACA,oDD1BM,yBAAA,gBAAA,CALJ,qBCkBF,wBACA,sCACA,8CACA,8BACA,4CACA,sDACA,yCACA,+BACA,6CACA,uDACA,mDACA,8BACA,8BACA,uDD1BM,yBAAA,gBAAA,CALJ,0BCkBF,wBACA,mCACA,2CACA,8BACA,yCACA,mDACA,uCACA,+BACA,0CACA,oDACA,mDACA,8BACA,8BACA,oDD1BM,yBAAA,gBAAA,CEPR,iBACI,yBAAA,gBAAA,CCDJ,YACI,uCAAA,CACA,yBACI,kBRDC,CQIT,kBACI,kBRLK,CQML,+BACI,kBRPC,CSDL,kDACI,uBAAA,CAEJ,0CACI,aPMG,CQVP,4BACI,kCAAA,CCFR,eACI,wBXGM,CWDF,yCACI,UTID,CSDP,+BACI,oBXHM,CWUE,8EACI,aTLT,CUTP,4BACI,wBVcG,CUZP,6BACI,kCCLmB,CDOvB,mCACI,8BAAA,CEHgB,wGACI,iDAAA,CCP5B,cACI,mCAAA,CACA,gBACI,cACA,kCAAA,6BAAA,yBAAA,CACA,sBACI,kCAAA,AACA,6BADA,AACA,0BAAA,ab+BF,CctCV,WACI,kBhBGM,CgBDR,qCACC,UdIQ,CAAA,iBcCP,qBACA,wBhBNM,CgBSE,iDACI,UdNL,CcOK,uDACI,aAAA,CAEJ,wDACI,uBAAA,CAMlB,2CACC,UdlBQ,CcqBP,2BACI,uGAAA,AACA,kGADA,AACA,+FAAA,oBhB1BM,CgBgCM,wDACI,ahBnCX,CgBsCG,gEACI,ahBvCP,CgBwCO,sEACI,kBAAA,CAEJ,iFACI,+BAAA,CAOxB,uBACI,sBAAA,CCqBA,4BDfQ,2DACI,kBhB1DN,CAAA,CgBiEF,iCACI,yBAAA,gBAAA,CE9DR,qBCNF,0BACA,uBACA,gCAAA,CAMA,iCACE,aAAA,CDDE,gCACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,uBCNF,0BACA,uBACA,gCAAA,CAMA,mCACE,aAAA,CDDE,kCACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,qBCNF,0BACA,uBACA,gCAAA,CAMA,iCACE,aAAA,CDDE,gCACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,kBCNF,0BACA,uBACA,gCAAA,CAMA,8BACE,aAAA,CDDE,6BACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,qBCNF,0BACA,uBACA,gCAAA,CAMA,iCACE,aAAA,CDDE,gCACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,oBCNF,0BACA,uBACA,gCAAA,CAMA,gCACE,aAAA,CDDE,+BACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,mBCNF,0BACA,uBACA,gCAAA,CAMA,+BACE,aAAA,CDDE,8BACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,kBCNF,0BACA,uBACA,gCAAA,CAMA,8BACE,aAAA,CDDE,6BACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,kBCNF,0BACA,uBACA,gCAAA,CAMA,8BACE,aAAA,CDDE,6BACE,0DAAA,AhBg9CqB,iDAAA,CgBn9CzB,uBCNF,0BACA,uBACA,gCAAA,CAMA,mCACE,aAAA,CDDE,kCACE,0DAAA,AhBg9CqB,iDAAA,CgBz8CrB,iCChBN,uBACA,uBACA,gCAAA,CAMA,6CACE,UAAA,CDSQ,4CACI,qBAAA,YAAA,CAHR,mCChBN,uBACA,uBACA,gCAAA,CAMA,+CACE,UAAA,CDSQ,8CACI,qBAAA,YAAA,CAHR,iCChBN,uBACA,uBACA,gCAAA,CAMA,6CACE,UAAA,CDSQ,4CACI,qBAAA,YAAA,CAHR,8BChBN,uBACA,uBACA,gCAAA,CAMA,0CACE,UAAA,CDSQ,yCACI,qBAAA,YAAA,CAHR,iCChBN,uBACA,uBACA,gCAAA,CAMA,6CACE,UAAA,CDSQ,4CACI,qBAAA,YAAA,CAHR,gCChBN,uBACA,uBACA,gCAAA,CAMA,4CACE,UAAA,CDSQ,2CACI,qBAAA,YAAA,CAHR,+BChBN,uBACA,uBACA,gCAAA,CAMA,2CACE,UAAA,CDSQ,0CACI,qBAAA,YAAA,CAHR,8BChBN,uBACA,uBACA,gCAAA,CAMA,0CACE,UAAA,CDSQ,yCACI,qBAAA,YAAA,CAHR,8BChBN,uBACA,uBACA,gCAAA,CAMA,0CACE,UAAA,CDSQ,yCACI,qBAAA,YAAA,CAHR,mCChBN,uBACA,uBACA,gCAAA,CAMA,+CACE,UAAA,CDSQ,8CACI,qBAAA,YAAA,CASR,gCACI,oBlBtBG,CkBqBP,kCACI,oBlBtBG,CkBqBP,gCACI,oBlBtBG,CkBqBP,6BACI,oBlBtBG,CkBqBP,gCACI,oBlBtBG,CkBqBP,+BACI,oBlBtBG,CkBqBP,8BACI,oBlBtBG,CkBqBP,6BACI,oBlBtBG,CkBqBP,6BACI,oBlBtBG,CkBqBP,kCACI,oBlBtBG,CkB4BP,+BACI,oBlB7BG,CkB4BP,iCACI,oBlB7BG,CkB4BP,+BACI,oBlB7BG,CkB4BP,4BACI,oBlB7BG,CkB4BP,+BACI,oBlB7BG,CkB4BP,8BACI,oBlB7BG,CkB4BP,6BACI,oBlB7BG,CkB4BP,4BACI,oBlB7BG,CkB4BP,4BACI,oBlB7BG,CkB4BP,iCACI,oBlB7BG,CkBmCP,iCACI,oBlBpCG,CkBmCP,mCACI,oBlBpCG,CkBmCP,iCACI,oBlBpCG,CkBmCP,8BACI,oBlBpCG,CkBmCP,iCACI,oBlBpCG,CkBmCP,gCACI,oBlBpCG,CkBmCP,+BACI,oBlBpCG,CkBmCP,8BACI,oBlBpCG,CkBmCP,8BACI,oBlBpCG,CkBmCP,mCACI,oBlBpCG,CkB0CP,kCACI,oBlB3CG,CkB0CP,oCACI,oBlB3CG,CkB0CP,kCACI,oBlB3CG,CkB0CP,+BACI,oBlB3CG,CkB0CP,kCACI,oBlB3CG,CkB0CP,iCACI,oBlB3CG,CkB0CP,gCACI,oBlB3CG,CkB0CP,+BACI,oBlB3CG,CkB0CP,+BACI,oBlB3CG,CkB0CP,oCACI,oBlB3CG,CoBTf,YACI,kBpBGM,CoBFN,kDACI,yBpBCE,2BACI,CoBGd,mBACI,4BAAA,CCRA,kDACI,mBACA,wBAAA,CACI,4EACI,mBACA,wBAAA,CAGZ,uDACI,mBACA,wBAAA,CACA,qFACI,arBVC,CsBHb,gBACI,qBACA,atBIQ,CsBHR,yBACI,wBtBFC,CsBMG,oCACI,UpBDL,CqBRX,oBACI,cACA,yBACA,oBvBEU,CuBDV,+BACI,oBrBqCE,CqBnCN,6BACI,oBrBqCE,CqBlCV,wBACI,cACA,yBACA,oBvBTU,CuBWd,mBACI,cACA,wBvBdM,CuBeN,8BACI,oBrBsBE,CqBnBV,wBACI,yBACA,oBvBpBU,CuBwBN,4BACI,cACA,wBvB7BH,CuBmCD,gCACI,cACA,wBvBrCH,CuByCT,6KAGI,wBvB1CM,CwBFF,yBACI,cxBCF,kBAFD,CwBIG,kCACI,kBxBHN,CwBIM,4CACI,UtBDT,CsBOC,gCACI,6BACA,UtBTL,CsBWK,yCACI,etBZT,CsBaS,mDAEI,uBAAA,CAKR,qCACI,kBtBgBV,CsBfU,uCACI,atBcd,CsBPM,wCACI,kBtBMV,CsBJU,0CACI,atBGd,CuB1CP,yBACa,wBzBFP,CyBQG,yBACG,aAAA,CCXf,qBACI,yB1BCK,aACI,CAAA,oC0BCL,a1BDK,C0BEL,0CACI,cACA,8BAAA,CAKR,uBACI,a1BVK,C0Beb,+BACI,wB1BjBK,C0BmBT,iCACI,a1BnBS,C2BFT,6BACI,oB3BGM,CAAA,0B2BCV,wB3BFM,C2BIV,yBACI,4BAAA,mBAAA,CCRH,0BACO,wB5BGM,CAAA,qC4BCF,mCAAA,CAKX,2BACO,wB5BPM,C4BUN,sCACI,mCAAA,CAEJ,uCACI,wB5BjBH,C6BDR,iDACC,4BAAA,CAIE,iDACI,sGAAA,iGAAA,6FAAA,CCNR,8BACC,c9BIW,C+BJV,mCACU,UACA,WACA,yBACA,oBAAA,CAKR,4BACI,yBACA,qBACA,a/BXK,C+BaG,gDACA,wB/BfP,C+BqBL,mCACI,cACA,oB/BpBM,C+BuBd,gBACI,6BAAA,CC7BJ,eACI,wBhCCK,CgCCT,sBACI,wBAAA,CAEJ,uHAGI,0BhCPK,CgCST,qHAEI,wBhCXK,CgCaT,2HAEI,2BhCfK,CgCiBT,wHAEI,yBhCnBK,CgCqBT,aACI,wBAAA,CACA,2BACI,wBAAA,CCxBN,sCACU,kBjCDH,CAAA,yBkCCN,oBlCEW,CkCDX,kCACC,6BAAA,CAID,+BACC,oBlCLU,CkCQZ,yBACC,oBlCTW,CkCad,eACC,wBhCgGU,CgC7FV,oBACC,yBhC6FS,CgC3FV,mBACC,0BhC0FS,CgCtFV,+BACC,6BAAA,CAGF,mBACC,oBlC9Ba,CkC+Bb,kCACC,kBlCnCO,CmCDR,+BACC,6BAAA,CAAA,qDCCU,YpCGA,CoCCJ,qDACI,YpCFA,CoCKR,2BACI,cpCNI,CoCSJ,6CACI,cpCVA,CqCNZ,0CACI,yBAAA,CAEJ,8RACI,+BAAA,CAEJ,8DACI,kBAAA,CAEJ,+BACI,wCAAA,CCVJ,YACI,6BAAA,CACA,yBACI,kBtCDC,CsCIT,kBACI,kBtCLK,CsCML,+BACI,kBtCPC,CAAA", "file": "../dark.min.css", "sourcesContent": ["/*!\r\n* Version: 1.2.0\r\n* Template: Example Project\r\n* Author: iqonic.design\r\n* Design and Developed by: iqonic.design\r\n* NOTE: This file contains the styling for Template.\r\n*\r\n*/\r\n\r\n// Configuration\r\n@import \"./bootstrap/functions\";\r\n// Variables\r\n@import \"./hope-ui-design-system/variable\";\r\n@import \"./hope-ui-design-system/variables/index\";\r\n@import \"./bootstrap/variables\";\r\n\r\n@import \"./bootstrap/mixins\";\r\n\r\n// Hope Ui Design System Mixin And Helper\r\n@import \"./hope-ui-design-system/helper/functions\";\r\n@import \"./hope-ui-design-system/helper/mixins\";\r\n@import \"./hope-ui-design-system/helper/reboot\";\r\n\r\n@import \"./dark/index\";\r\n", ":focus{\r\n\toutline: none;\r\n}\r\nhr {\r\n    margin: 1rem 0;\r\n    color: inherit;\r\n    background-color: currentColor;\r\n    border: 0;\r\n    opacity: .25;\r\n}", "@mixin bg-variant($parent, $color) {\r\n  #{$parent} {\r\n    color: shift-color($color, 10%);\r\n    background-color: rgba($color,.2) !important;\r\n  }\r\n  a#{$parent},\r\n  button#{$parent} {\r\n    @include hover-focus() {\r\n      color: darken($color, 15%);\r\n      background-color: rgba($color,.3) !important;\r\n    }\r\n  }\r\n}\r\n\r\n@mixin bg-variant-dark($parent, $color) {\r\n  #{$parent} {\r\n    background-color: rgba(lighten(darken($color, 30%), 40%), .1)!important;\r\n  }\r\n  a#{$parent},\r\n  button#{$parent} {\r\n    @include hover-focus() {\r\n      cursor: pointer;\r\n      color: darken($color, 15%);\r\n      background-color: rgba(lighten(darken($color, 30%), 40%), .15) !important;\r\n    }\r\n  }\r\n}", "/*\r\n * Background color \r\n */\r\n\r\n@each $color, $value in $theme-colors{\r\n\t@include bg-variant-dark(\".bg-soft-#{$color}\", $value);\r\n}\r\n\r\n@each $color, $value in $theme-colors{\r\n\t.text-#{$color} {\r\n\t\tcolor: #{$value} !important;\r\n\t}\r\n}", ".iq-media-group{\r\n    .iq-icon-box-3 {\r\n        background: $card-bg;\r\n\r\n    } \r\n} ", "// Main\r\n\r\n$body-bg:#151824;\r\n$body-color: #8A92A6;\r\n$card-bg: #222738;\r\n$border-color:#30384f;\r\n$secondary: #5c6ca5;\r\n$dark: #60658a;\r\n\r\n$theme-colors: map-merge($theme-colors,\r\n  (\r\n    \"secondary\":       $secondary,\r\n  )\r\n);\r\n\r\n$alert-bg-scale: 60%;\r\n$alert-border-scale: 80%;\r\n$alert-color-scale: 80%;", "&{\r\n    color: $body-color;\r\n    background-color: $body-bg !important;\r\n}\r\nh1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {\r\n    color: $body-color;\r\n}\r\n.border {\r\n    border-color: $border-color !important;\r\n}\r\n.border-bottom {\r\n    border-color: $border-color !important;\r\n}\r\n.datepicker-picker {\r\n    background-color: $card-bg;\r\n    .datepicker-title {\r\n        background-color: $card-bg;\r\n    }\r\n    .datepicker-controls {\r\n        .button {\r\n            background-color: $card-bg;\r\n            color: $body-color;\r\n        }\r\n    }\r\n}\r\nthead, tbody, tfoot, tr, td, th {\r\n    border-color: $border-color;\r\n    color: $body-color;\r\n}\r\n.offcanvas {\r\n    background-color: $card-bg;\r\n    color: $body-color;\r\n}\r\nbutton {\r\n    &.close-btn-res {\r\n        color: $light;\r\n    }\r\n}\r\nhr.hr-horizontal{\r\n    background-image: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)\r\n}\r\nhr.hr-vertial {\r\n    background-image: linear-gradient(180deg, transparent, rgba(255,255,255,0.4), transparent)\r\n}", "// Variables\r\n//\r\n// Variables should follow the `$component-state-property-size` formula for\r\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\r\n\r\n// Color system\r\n\r\n// scss-docs-start gray-color-variables\r\n$white:    #ffffff !default;\r\n$gray-100: #f8f9fa !default;\r\n$gray-200: #e9ecef !default;\r\n$gray-300: #dee2e6 !default;\r\n$gray-400: #ced4da !default;\r\n$gray-500: #adb5bd !default;\r\n$gray-600: #6c757d !default;\r\n$gray-700: #495057 !default;\r\n$gray-800: #343a40 !default;\r\n$gray-900: #212529 !default;\r\n$black:    #000 !default;\r\n// scss-docs-end gray-color-variables\r\n\r\n// fusv-disable\r\n// scss-docs-start gray-colors-map\r\n$grays: (\r\n  \"100\": $gray-100,\r\n  \"200\": $gray-200,\r\n  \"300\": $gray-300,\r\n  \"400\": $gray-400,\r\n  \"500\": $gray-500,\r\n  \"600\": $gray-600,\r\n  \"700\": $gray-700,\r\n  \"800\": $gray-800,\r\n  \"900\": $gray-900\r\n) !default;\r\n// scss-docs-end gray-colors-map\r\n// fusv-enable\r\n\r\n// scss-docs-start color-variables\r\n$blue:    #3a57e8  !default;\r\n$indigo:  #6610f2 !default;\r\n$purple:  #6f42c1 !default;\r\n$pink:    #d63384 !default;\r\n$red:     #c03221  !default;\r\n$orange:  #FAA938 !default;\r\n$yellow:  #f16a1b  !default;\r\n$green:   #1aa053  !default;\r\n$teal:    #001F4D !default;\r\n$cyan:    #079aa2  !default;\r\n// scss-docs-end color-variables\r\n\r\n// shadow color\r\n\r\n$shadow-color: #8898AA;\r\n\r\n// scss-docs-start colors-map\r\n$colors: (\r\n  \"blue\":       $blue,\r\n  \"indigo\":     $indigo,\r\n  \"purple\":     $purple,\r\n  \"pink\":       $pink,\r\n  \"red\":        $red,\r\n  \"orange\":     $orange,\r\n  \"yellow\":     $yellow,\r\n  \"green\":      $green,\r\n  \"teal\":       $teal,\r\n  \"cyan\":       $cyan,\r\n  \"white\":      $white,\r\n  \"gray\":       $gray-600,\r\n  \"gray-dark\":  $gray-800\r\n) !default;\r\n// scss-docs-end colors-map\r\n\r\n// scss-docs-start theme-color-variables\r\n$primary:       $blue !default;\r\n$secondary:     $teal !default;\r\n$success:       $green !default;\r\n$info:          $cyan !default;\r\n$warning:       $yellow !default;\r\n$danger:        $red !default;\r\n$light:         $gray-300 !default;\r\n$dark:          $gray-900 !default;\r\n$gray:          $gray-600 !default;\r\n$gray-dark:     $gray-800 !default;\r\n// scss-docs-end theme-color-variables\r\n\r\n// scss-docs-start theme-colors-map\r\n$theme-colors: (\r\n  \"primary\":    $primary,\r\n  \"secondary\":  $secondary,\r\n  \"success\":    $success,\r\n  \"info\":       $info,\r\n  \"warning\":    $warning,\r\n  \"danger\":     $danger,\r\n  \"light\":      $light,\r\n  \"dark\":       $dark,\r\n  \"gray\":       $gray,\r\n  \"gray-dark\":   $gray-dark,\r\n) !default;\r\n// scss-docs-end theme-colors-map\r\n\r\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\r\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\r\n$min-contrast-ratio:   3 !default;\r\n\r\n// Customize the light and dark text colors for use in our color contrast function.\r\n$color-contrast-dark:      $black !default;\r\n$color-contrast-light:     $white !default;\r\n\r\n// fusv-disable\r\n$blue-100: tint-color($blue, 80%) !default;\r\n$blue-200: tint-color($blue, 60%) !default;\r\n$blue-300: tint-color($blue, 40%) !default;\r\n$blue-400: tint-color($blue, 20%) !default;\r\n$blue-500: $blue !default;\r\n$blue-600: shade-color($blue, 20%) !default;\r\n$blue-700: shade-color($blue, 40%) !default;\r\n$blue-800: shade-color($blue, 60%) !default;\r\n$blue-900: shade-color($blue, 80%) !default;\r\n\r\n$indigo-100: tint-color($indigo, 80%) !default;\r\n$indigo-200: tint-color($indigo, 60%) !default;\r\n$indigo-300: tint-color($indigo, 40%) !default;\r\n$indigo-400: tint-color($indigo, 20%) !default;\r\n$indigo-500: $indigo !default;\r\n$indigo-600: shade-color($indigo, 20%) !default;\r\n$indigo-700: shade-color($indigo, 40%) !default;\r\n$indigo-800: shade-color($indigo, 60%) !default;\r\n$indigo-900: shade-color($indigo, 80%) !default;\r\n\r\n$purple-100: tint-color($purple, 80%) !default;\r\n$purple-200: tint-color($purple, 60%) !default;\r\n$purple-300: tint-color($purple, 40%) !default;\r\n$purple-400: tint-color($purple, 20%) !default;\r\n$purple-500: $purple !default;\r\n$purple-600: shade-color($purple, 20%) !default;\r\n$purple-700: shade-color($purple, 40%) !default;\r\n$purple-800: shade-color($purple, 60%) !default;\r\n$purple-900: shade-color($purple, 80%) !default;\r\n\r\n$pink-100: tint-color($pink, 80%) !default;\r\n$pink-200: tint-color($pink, 60%) !default;\r\n$pink-300: tint-color($pink, 40%) !default;\r\n$pink-400: tint-color($pink, 20%) !default;\r\n$pink-500: $pink !default;\r\n$pink-600: shade-color($pink, 20%) !default;\r\n$pink-700: shade-color($pink, 40%) !default;\r\n$pink-800: shade-color($pink, 60%) !default;\r\n$pink-900: shade-color($pink, 80%) !default;\r\n\r\n$red-100: tint-color($red, 80%) !default;\r\n$red-200: tint-color($red, 60%) !default;\r\n$red-300: tint-color($red, 40%) !default;\r\n$red-400: tint-color($red, 20%) !default;\r\n$red-500: $red !default;\r\n$red-600: shade-color($red, 20%) !default;\r\n$red-700: shade-color($red, 40%) !default;\r\n$red-800: shade-color($red, 60%) !default;\r\n$red-900: shade-color($red, 80%) !default;\r\n\r\n$orange-100: tint-color($orange, 80%) !default;\r\n$orange-200: tint-color($orange, 60%) !default;\r\n$orange-300: tint-color($orange, 40%) !default;\r\n$orange-400: tint-color($orange, 20%) !default;\r\n$orange-500: $orange !default;\r\n$orange-600: shade-color($orange, 20%) !default;\r\n$orange-700: shade-color($orange, 40%) !default;\r\n$orange-800: shade-color($orange, 60%) !default;\r\n$orange-900: shade-color($orange, 80%) !default;\r\n\r\n$yellow-100: tint-color($yellow, 80%) !default;\r\n$yellow-200: tint-color($yellow, 60%) !default;\r\n$yellow-300: tint-color($yellow, 40%) !default;\r\n$yellow-400: tint-color($yellow, 20%) !default;\r\n$yellow-500: $yellow !default;\r\n$yellow-600: shade-color($yellow, 20%) !default;\r\n$yellow-700: shade-color($yellow, 40%) !default;\r\n$yellow-800: shade-color($yellow, 60%) !default;\r\n$yellow-900: shade-color($yellow, 80%) !default;\r\n\r\n$green-100: tint-color($green, 80%) !default;\r\n$green-200: tint-color($green, 60%) !default;\r\n$green-300: tint-color($green, 40%) !default;\r\n$green-400: tint-color($green, 20%) !default;\r\n$green-500: $green !default;\r\n$green-600: shade-color($green, 20%) !default;\r\n$green-700: shade-color($green, 40%) !default;\r\n$green-800: shade-color($green, 60%) !default;\r\n$green-900: shade-color($green, 80%) !default;\r\n\r\n$teal-100: tint-color($teal, 80%) !default;\r\n$teal-200: tint-color($teal, 60%) !default;\r\n$teal-300: tint-color($teal, 40%) !default;\r\n$teal-400: tint-color($teal, 20%) !default;\r\n$teal-500: $teal !default;\r\n$teal-600: shade-color($teal, 20%) !default;\r\n$teal-700: shade-color($teal, 40%) !default;\r\n$teal-800: shade-color($teal, 60%) !default;\r\n$teal-900: shade-color($teal, 80%) !default;\r\n\r\n$cyan-100: tint-color($cyan, 80%) !default;\r\n$cyan-200: tint-color($cyan, 60%) !default;\r\n$cyan-300: tint-color($cyan, 40%) !default;\r\n$cyan-400: tint-color($cyan, 20%) !default;\r\n$cyan-500: $cyan !default;\r\n$cyan-600: shade-color($cyan, 20%) !default;\r\n$cyan-700: shade-color($cyan, 40%) !default;\r\n$cyan-800: shade-color($cyan, 60%) !default;\r\n$cyan-900: shade-color($cyan, 80%) !default;\r\n// fusv-enable\r\n\r\n// Characters which are escaped by the escape-svg function\r\n$escaped-characters: (\r\n  (\"<\", \"%3c\"),\r\n  (\">\", \"%3e\"),\r\n  (\"#\", \"%23\"),\r\n  (\"(\", \"%28\"),\r\n  (\")\", \"%29\"),\r\n) !default;\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n\r\n$enable-caret:                true !default;\r\n$enable-rounded:              true !default;\r\n$enable-shadows:              true;\r\n$enable-gradients:            false !default;\r\n$enable-transitions:          true !default;\r\n$enable-reduced-motion:       true !default;\r\n$enable-smooth-scroll:        true !default;\r\n$enable-grid-classes:         true !default;\r\n$enable-button-pointers:      true !default;\r\n$enable-rfs:                  true !default;\r\n$enable-validation-icons:     true !default;\r\n$enable-negative-margins:     true !default;\r\n$enable-deprecation-messages: true !default;\r\n$enable-important-utilities:  true !default;\r\n\r\n// Prefix for :root CSS variables\r\n\r\n$variable-prefix:             bs- !default;\r\n\r\n// Gradient\r\n//\r\n// The gradient which is added to components if `$enable-gradients` is `true`\r\n// This gradient is also added to elements with `.bg-gradient`\r\n// scss-docs-start variable-gradient\r\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\r\n// scss-docs-end variable-gradient\r\n\r\n// Spacing\r\n//\r\n// Control the default styling of most Bootstrap elements by modifying these\r\n// variables. Mostly focused on spacing.\r\n// You can add more entries to the $spacers map, should you need more variation.\r\n\r\n// scss-docs-start spacer-variables-maps\r\n$spacer: 1rem !default;\r\n$spacers: (\r\n  0: 0,\r\n  1: $spacer * .25,\r\n  2: $spacer * .5,\r\n  3: $spacer,\r\n  4: $spacer * 1.5,\r\n  5: $spacer * 3,\r\n) !default;\r\n\r\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\r\n// scss-docs-end spacer-variables-maps\r\n\r\n// Position\r\n//\r\n// Define the edge positioning anchors of the position utilities.\r\n\r\n// scss-docs-start position-map\r\n$position-values: (\r\n  0: 0,\r\n  50: 50%,\r\n  100: 100%\r\n) !default;\r\n// scss-docs-end position-map\r\n\r\n// Body\r\n//\r\n// Settings for the `<body>` element.\r\n\r\n$body-bg:                   #F5F6FA !default;\r\n$body-color:                #8A92A6 !default;\r\n$body-text-align:           null !default;\r\n\r\n\r\n// Links\r\n//\r\n// Style anchor elements.\r\n\r\n$link-color:                              $primary !default;\r\n$link-decoration:                         none !default;\r\n$link-shade-percentage:                   20% !default;\r\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\r\n$link-hover-decoration:                   null !default;\r\n\r\n$stretched-link-pseudo-element:           after !default;\r\n$stretched-link-z-index:                  1 !default;\r\n\r\n// Paragraphs\r\n//\r\n// Style p element.\r\n\r\n$paragraph-margin-bottom:   1rem !default;\r\n\r\n\r\n// Grid breakpoints\r\n//\r\n// Define the minimum dimensions at which your layout will change,\r\n// adapting to different screen sizes, for use in media queries.\r\n\r\n// scss-docs-start grid-breakpoints\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px,\r\n  xxl: 1400px\r\n) !default;\r\n// scss-docs-end grid-breakpoints\r\n\r\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\r\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\r\n\r\n\r\n// Grid containers\r\n//\r\n// Define the maximum width of `.container` for different screen sizes.\r\n\r\n// scss-docs-start container-max-widths\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1140px,\r\n  xxl: 1320px\r\n) !default;\r\n// scss-docs-end container-max-widths\r\n\r\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\r\n\r\n\r\n// Grid columns\r\n//\r\n// Set the number of columns and specify the width of the gutters.\r\n\r\n$grid-columns:                12 !default;\r\n$grid-gutter-width:           2rem !default;\r\n$grid-row-columns:            6 !default;\r\n\r\n$gutters: $spacers !default;\r\n\r\n// Container padding\r\n\r\n$container-padding-x: $grid-gutter-width * .5 !default;\r\n\r\n\r\n// Components\r\n//\r\n// Define common padding and border radius sizes and more.\r\n\r\n// scss-docs-start border-variables\r\n$border-width:                1px !default;\r\n$border-widths: (\r\n  1: 1px,\r\n  2: 2px,\r\n  3: 3px,\r\n  4: 4px,\r\n  5: 5px\r\n) !default;\r\n\r\n$border-color:                #eee !default;\r\n// scss-docs-end border-variables\r\n\r\n// scss-docs-start border-radius-variables\r\n$border-radius-xs:            0.125rem !default;\r\n$border-radius:               0.5rem !default;\r\n$border-radius-sm:            0.25rem !default;\r\n$border-radius-lg:            1rem !default;\r\n$border-radius-pill:          50rem !default;\r\n// scss-docs-end border-radius-variables\r\n\r\n// scss-docs-start box-shadow-variables\r\n$box-shadow:                  0 .5rem 1rem rgba(darken($primary, 25%), .05) !default;\r\n$box-shadow-sm:               0 .125rem .25rem rgba(darken($primary, 25%), .1) !default;\r\n$box-shadow-lg:               0 10px 30px 0 rgba(darken($primary, 25%), .05)!default;\r\n$box-shadow-inset:            inset 0 4px 8px rgba($black, .16) !default;\r\n\r\n//Color Shadow\r\n$box-color-shadow:            0 .125rem .25rem !default;\r\n$box-color-shadow-hover:      0 .125rem .5rem !default;\r\n$box-color-shadow-tint:         .30!default;\r\n$box-color-shadow-shade:        .35!default;\r\n\r\n// scss-docs-end box-shadow-variables\r\n\r\n$component-active-color:      $white !default;\r\n$component-active-bg:         $primary !default;\r\n$component-active-shadow:     0 .125rem .25rem rgba($primary,.10)!default;\r\n$component-hover-shadow:     0 .125rem .25rem rgba($primary,.15)!default;\r\n$component-success-shadow:     0 .125rem .25rem rgba($success,.10)!default;\r\n$component-error-shadow:     0 .125rem .25rem rgba($danger,.10)!default;\r\n\r\n// scss-docs-start caret-variables\r\n$caret-width:                 .3em !default;\r\n$caret-vertical-align:        $caret-width * .85 !default;\r\n$caret-spacing:               $caret-width * .85 !default;\r\n// scss-docs-end caret-variables\r\n\r\n$transition-base:             all .2s ease-in-out !default;\r\n$transition-fade:             opacity .15s linear !default;\r\n// scss-docs-start collapse-transition\r\n$transition-collapse:         height .35s ease !default;\r\n// scss-docs-end collapse-transition\r\n\r\n// stylelint-disable function-disallowed-list\r\n// scss-docs-start aspect-ratios\r\n$aspect-ratios: (\r\n  \"1x1\": 100%,\r\n  \"4x3\": calc(3 / 4 * 100%),\r\n  \"16x9\": calc(9 / 16 * 100%),\r\n  \"21x9\": calc(9 / 21 * 100%)\r\n) !default;\r\n// scss-docs-end aspect-ratios\r\n// stylelint-enable function-disallowed-list\r\n\r\n// Typography\r\n//\r\n// Font, line-height, and color for body text, headings, and more.\r\n\r\n// scss-docs-start font-variables\r\n// stylelint-disable value-keyword-case\r\n$font-family-sans-serif:      'Inter', sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\r\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\r\n// stylelint-enable value-keyword-case\r\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif) !default;\r\n$font-family-code:            var(--#{$variable-prefix}font-monospace) !default;\r\n\r\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\r\n// $font-size-base affects the font size of the body text\r\n$font-size-root:              null !default;\r\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\r\n$font-size-xs:                $font-size-base * .800 !default;\r\n$font-size-sm:                $font-size-base * .875 !default;\r\n$font-size-lg:                $font-size-base * 1.25 !default;\r\n\r\n$font-weight-lighter:         lighter !default;\r\n$font-weight-light:           300 !default;\r\n$font-weight-normal:          400 !default;\r\n$font-weight-bold:            700 !default;\r\n$font-weight-bolder:          bolder !default;\r\n\r\n$font-weight-base:            $font-weight-normal !default;\r\n\r\n$line-height-base:            1.5 !default;\r\n$line-height-sm:              1.25 !default;\r\n$line-height-lg:              2 !default;\r\n\r\n$h1-font-size:                $font-size-base * 2.5 !default;\r\n$h2-font-size:                $font-size-base * 2 !default;\r\n$h3-font-size:                $font-size-base * 1.75 !default;\r\n$h4-font-size:                $font-size-base * 1.5 !default;\r\n$h5-font-size:                $font-size-base * 1.25 !default;\r\n$h6-font-size:                $font-size-base !default;\r\n// scss-docs-end font-variables\r\n\r\n// scss-docs-start font-sizes\r\n$font-sizes: (\r\n  1: $h1-font-size,\r\n  2: $h2-font-size,\r\n  3: $h3-font-size,\r\n  4: $h4-font-size,\r\n  5: $h5-font-size,\r\n  6: $h6-font-size\r\n) !default;\r\n// scss-docs-end font-sizes\r\n\r\n// scss-docs-start headings-variables\r\n$headings-margin-bottom:      0 !default;\r\n$headings-font-family:        null !default;\r\n$headings-font-style:         null !default;\r\n$headings-font-weight:        500 !default;\r\n$headings-line-height:        1.2 !default;\r\n$headings-color:              #232D42 !default;\r\n// scss-docs-end headings-variables\r\n\r\n// scss-docs-start display-headings\r\n$display-font-sizes: (\r\n  1: 5rem,\r\n  2: 4.5rem,\r\n  3: 4rem,\r\n  4: 3.5rem,\r\n  5: 3rem,\r\n  6: 2.5rem\r\n) !default;\r\n\r\n$display-font-weight: 300 !default;\r\n$display-line-height: $headings-line-height !default;\r\n// scss-docs-end display-headings\r\n\r\n// scss-docs-start type-variables\r\n$lead-font-size:              $font-size-base * 1.25 !default;\r\n$lead-font-weight:            300 !default;\r\n\r\n$small-font-size:             .875em !default;\r\n\r\n$sub-sup-font-size:           .75em !default;\r\n\r\n$text-muted:                  $gray-600 !default;\r\n\r\n$initialism-font-size:        $small-font-size !default;\r\n\r\n$blockquote-margin-y:         $spacer !default;\r\n$blockquote-font-size:        $font-size-base * 1.25 !default;\r\n$blockquote-footer-color:     $gray-600 !default;\r\n$blockquote-footer-font-size: $small-font-size !default;\r\n\r\n$hr-margin-y:                 $spacer !default;\r\n$hr-color:                    inherit !default;\r\n$hr-height:                   $border-width !default;\r\n$hr-opacity:                  .25 !default;\r\n\r\n$legend-margin-bottom:        .5rem !default;\r\n$legend-font-size:            1.5rem !default;\r\n$legend-font-weight:          null !default;\r\n\r\n$mark-padding:                .2em !default;\r\n\r\n$dt-font-weight:              $font-weight-bold !default;\r\n\r\n$nested-kbd-font-weight:      $font-weight-bold !default;\r\n\r\n$list-inline-padding:         .5rem !default;\r\n\r\n$mark-bg:                     #fcf8e3 !default;\r\n// scss-docs-end type-variables\r\n\r\n\r\n// Tables\r\n//\r\n// Customizes the `.table` component with basic values, each used across all table variations.\r\n\r\n// scss-docs-start table-variables\r\n$table-th-padding-y:        .75rem !default;\r\n$table-th-padding-x:        1.5rem !default;\r\n$table-cell-padding-y:        1rem !default;\r\n$table-cell-padding-x:        1.5rem !default;\r\n$table-cell-padding-y-sm:     .25rem !default;\r\n$table-cell-padding-x-sm:     .25rem !default;\r\n\r\n$table-cell-vertical-align:   center !default;\r\n\r\n$table-color:                 $body-color !default;\r\n$table-td-color:              $headings-color !default;\r\n$table-bg:                    transparent !default;\r\n$table-radius:                null !default;\r\n$table-accent-bg:             transparent !default;\r\n\r\n$table-th-font-weight:        500 !default;\r\n\r\n$table-striped-color:         $table-color !default;\r\n$table-striped-bg-factor:     .03 !default;\r\n$table-striped-bg:            rgba($body-color, $table-striped-bg-factor) !default;\r\n\r\n$table-active-color:          $table-color !default;\r\n$table-active-bg-factor:      .1 !default;\r\n$table-active-bg:             rgba($body-color, $table-active-bg-factor) !default;\r\n\r\n$table-hover-color:           $table-color !default;\r\n$table-hover-bg-factor:       .075 !default;\r\n$table-hover-bg:              rgba($body-color, $table-hover-bg-factor) !default;\r\n\r\n$table-border-factor:         .1 !default;\r\n$table-border-width:          $border-width !default;\r\n$table-border-color:          $border-color !default;\r\n\r\n$table-striped-order:         even !default;\r\n\r\n$table-group-separator-color: currentColor !default;\r\n\r\n$table-caption-color:         $text-muted !default;\r\n\r\n$table-bg-scale:              -80% !default;\r\n// scss-docs-end table-variables\r\n\r\n// scss-docs-start table-loop\r\n$table-variants: (\r\n  \"primary\":    shift-color($primary, $table-bg-scale),\r\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\r\n  \"success\":    shift-color($success, $table-bg-scale),\r\n  \"info\":       shift-color($info, $table-bg-scale),\r\n  \"warning\":    shift-color($warning, $table-bg-scale),\r\n  \"danger\":     shift-color($danger, $table-bg-scale),\r\n  \"light\":      $light,\r\n  \"dark\":       $dark,\r\n  \"gray\":       shift-color($gray, $table-bg-scale),\r\n) !default;\r\n// scss-docs-end table-loop\r\n\r\n\r\n// Buttons + Forms\r\n//\r\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\r\n\r\n// scss-docs-start input-btn-variables\r\n$input-btn-padding-y:         .5rem !default;\r\n$input-btn-padding-x:         1.5rem !default;\r\n$input-btn-font-family:       null !default;\r\n$input-btn-font-size:         $font-size-base !default;\r\n$input-btn-line-height:       $line-height-base !default;\r\n\r\n$input-btn-focus-width:         0rem !default;\r\n$input-btn-focus-color-opacity: .15 !default;\r\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\r\n$input-btn-focus-blur:          .25rem !default;\r\n$input-btn-focus-box-shadow:    0 .125rem $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\r\n\r\n$input-btn-padding-y-xs:      0.125rem !default;\r\n$input-btn-padding-x-xs:      0.5rem !default;\r\n$input-btn-font-size-xs:      $font-size-xs !default;\r\n\r\n$input-btn-padding-y-sm:      .25rem !default;\r\n$input-btn-padding-x-sm:      1rem !default;\r\n$input-btn-font-size-sm:      $font-size-sm !default;\r\n\r\n$input-btn-padding-y-lg:      .5rem !default;\r\n$input-btn-padding-x-lg:      1.5rem !default;\r\n$input-btn-font-size-lg:      $font-size-lg !default;\r\n\r\n$input-btn-border-width:      $border-width !default;\r\n// scss-docs-end input-btn-variables\r\n\r\n\r\n// Buttons\r\n//\r\n// For each of Bootstrap's buttons, define text, background, and border color.\r\n\r\n// scss-docs-start btn-variables\r\n$btn-padding-y:               $input-btn-padding-y !default;\r\n$btn-padding-x:               $input-btn-padding-x !default;\r\n$btn-font-family:             $input-btn-font-family !default;\r\n$btn-font-size:               $input-btn-font-size !default;\r\n$btn-line-height:             $input-btn-line-height !default;\r\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\r\n\r\n$btn-padding-y-xs:            $input-btn-padding-y-xs !default;\r\n$btn-padding-x-xs:            $input-btn-padding-x-xs !default;\r\n$btn-font-size-xs:            $input-btn-font-size-xs !default;\r\n\r\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\r\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\r\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\r\n\r\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\r\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\r\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\r\n\r\n$btn-border-width:            $input-btn-border-width !default;\r\n\r\n$btn-font-weight:             $font-weight-normal !default;\r\n$btn-box-shadow:              0 0px 0px 0 rgba($black,0) !default;\r\n$btn-box-color-shadow:        $box-color-shadow !default;\r\n$btn-box-color-shadow-hover:  $box-color-shadow-hover !default;\r\n$btn-box-shadow-tint:         $box-color-shadow-tint!default;\r\n$btn-box-shadow-shade:        $box-color-shadow-shade!default;\r\n$btn-focus-width:             $input-btn-focus-width !default;\r\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\r\n$btn-disabled-opacity:        .65 !default;\r\n$btn-active-box-shadow:       0 0px 0px rgba($black, .0) !default;\r\n\r\n$btn-link-color:              $link-color !default;\r\n$btn-link-hover-color:        $link-hover-color !default;\r\n$btn-link-disabled-color:     $gray-600 !default;\r\n\r\n// Allows for customizing button radius independently from global border radius\r\n$btn-border-radius-xs: $border-radius-xs!default;\r\n$btn-border-radius:           $border-radius-sm !default;\r\n$btn-border-radius-sm:        $border-radius-sm !default;\r\n$btn-border-radius-lg:        $border-radius-sm !default;\r\n\r\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$btn-hover-bg-shade-amount:       15% !default;\r\n$btn-hover-bg-tint-amount:        15% !default;\r\n$btn-hover-border-shade-amount:   20% !default;\r\n$btn-hover-border-tint-amount:    10% !default;\r\n$btn-active-bg-shade-amount:      20% !default;\r\n$btn-active-bg-tint-amount:       20% !default;\r\n$btn-active-border-shade-amount:  25% !default;\r\n$btn-active-border-tint-amount:   10% !default;\r\n// scss-docs-end btn-variables\r\n\r\n\r\n// Forms\r\n\r\n// scss-docs-start form-text-variables\r\n$form-text-margin-top:                  .25rem !default;\r\n$form-text-font-size:                   $small-font-size !default;\r\n$form-text-font-style:                  null !default;\r\n$form-text-font-weight:                 null !default;\r\n$form-text-color:                       $text-muted !default;\r\n// scss-docs-end form-text-variables\r\n\r\n// scss-docs-start form-label-variables\r\n$form-label-margin-bottom:              .5rem !default;\r\n$form-label-font-size:                  null !default;\r\n$form-label-font-style:                 null !default;\r\n$form-label-font-weight:                null !default;\r\n$form-label-color:                      null !default;\r\n// scss-docs-end form-label-variables\r\n\r\n// scss-docs-start form-input-variables\r\n$input-padding-y:                       .5rem;\r\n$input-padding-x:                       1rem;\r\n$input-font-family:                     $input-btn-font-family !default;\r\n$input-font-size:                       $input-btn-font-size !default;\r\n$input-font-weight:                     $font-weight-base !default;\r\n$input-line-height:                     $input-btn-line-height !default;\r\n\r\n$input-padding-y-xs:                    $input-btn-padding-y-xs !default;\r\n$input-padding-x-xs:                    $input-btn-padding-x-xs !default;\r\n$input-font-size-xs:                    $input-btn-font-size-xs !default;\r\n\r\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\r\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\r\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\r\n\r\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\r\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\r\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\r\n\r\n$input-bg:                              $white !default;\r\n$input-disabled-bg:                     $gray-200 !default;\r\n$input-disabled-border-color:           null !default;\r\n\r\n$input-color:                           $body-color !default;\r\n$input-border-color:                    $border-color !default;\r\n$input-border-width:                    $input-btn-border-width !default;\r\n$input-box-shadow:                      0 0 0 0 !default;\r\n\r\n$input-border-radius:                   .25rem !default;\r\n$input-border-radius-sm:                $input-border-radius !default;\r\n$input-border-radius-lg:                $input-border-radius !default;\r\n\r\n$input-focus-bg:                        $input-bg !default;\r\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\r\n$input-focus-color:                     $input-color !default;\r\n$input-focus-width:                     $input-btn-focus-width !default;\r\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\r\n\r\n$input-placeholder-color:               $gray-600 !default;\r\n$input-plaintext-color:                 $body-color !default;\r\n\r\n$input-height-border:                   $input-border-width * 2 !default;\r\n\r\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\r\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\r\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\r\n\r\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\r\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\r\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\r\n\r\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n// scss-docs-end form-input-variables\r\n\r\n// scss-docs-start form-check-variables\r\n$form-check-input-width:                  1em !default;\r\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\r\n$form-check-padding-start:                $form-check-input-width + .5em !default;\r\n$form-check-margin-bottom:                .125rem !default;\r\n$form-check-label-color:                  null !default;\r\n$form-check-label-cursor:                 null !default;\r\n$form-check-transition:                   color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$form-check-input-active-filter:          brightness(90%) !default;\r\n\r\n$form-check-input-bg:                     $input-bg !default;\r\n$form-check-input-border:                 1px solid rgba($black, .25) !default;\r\n$form-check-input-border-radius:          .25em !default;\r\n$form-check-radio-border-radius:          50% !default;\r\n$form-check-input-focus-border:           $input-focus-border-color !default;\r\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow !default;\r\n\r\n$form-check-input-checked-color:          $component-active-color !default;\r\n$form-check-input-checked-bg-color:       $component-active-bg !default;\r\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\r\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\r\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\r\n\r\n$form-check-input-indeterminate-color:          $component-active-color !default;\r\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\r\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\r\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\r\n\r\n$form-check-input-disabled-opacity:        .5 !default;\r\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\r\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\r\n\r\n$form-check-inline-margin-end:    1rem !default;\r\n// scss-docs-end form-check-variables\r\n\r\n// scss-docs-start form-switch-variables\r\n// $form-switch-color:               rgba(0, 0, 0, .25) !default;\r\n// $form-switch-width:               2em !default;\r\n// $form-switch-size:                .5rem !default;\r\n// $form-switch-padding-start:       $form-switch-width + .5em !default;\r\n// $form-switch-bg-image:            url(\"data:image/svg+xml,<svg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='1' y='1' width='18' height='18' rx='9' fill='white' stroke='#{$white}' stroke-width='2'/></svg>\") !default;\r\n// $form-switch-border-radius:       $form-switch-width !default;\r\n// $form-switch-transition:          background-position .15s ease-in-out !default;\r\n\r\n// $form-switch-focus-color:         $input-focus-border-color !default;\r\n// $form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='1' y='1' width='18' height='18' rx='9' fill='white' stroke='#{$white}' stroke-width='2'/></svg>\") !default;\r\n\r\n// $form-switch-checked-color:       $component-active-bg !default;\r\n// $form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='1' y='1' width='18' height='18' rx='9' fill='white' stroke='#{$form-check-input-checked-color}' stroke-width='2'/></svg>\") !default;\r\n// $form-switch-checked-bg-position: right center !default;\r\n$form-switch-color:               rgba($black, .25) !default;\r\n$form-switch-width:               2em !default;\r\n$form-switch-padding-start:       $form-switch-width + .5em !default;\r\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\r\n$form-switch-border-radius:       $form-switch-width !default;\r\n$form-switch-transition:          background-position .15s ease-in-out !default;\r\n\r\n$form-switch-focus-color:         $input-focus-border-color !default;\r\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\r\n\r\n$form-switch-checked-color:       $component-active-color !default;\r\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\r\n$form-switch-checked-bg-position: right center !default;\r\n// scss-docs-end form-switch-variables\r\n\r\n// scss-docs-start input-group-variables\r\n$input-group-addon-padding-y:           $input-padding-y !default;\r\n$input-group-addon-padding-x:           $input-padding-x !default;\r\n$input-group-addon-font-weight:         $input-font-weight !default;\r\n$input-group-addon-color:               $input-color !default;\r\n$input-group-addon-bg:                  $input-bg !default;\r\n$input-group-addon-border-color:        $input-border-color !default;\r\n// scss-docs-end input-group-variables\r\n\r\n// scss-docs-start form-select-variables\r\n$form-select-padding-y:             $input-padding-y !default;\r\n$form-select-padding-x:             $input-padding-x !default;\r\n$form-select-font-family:           $input-font-family !default;\r\n$form-select-font-size:             $input-font-size !default;\r\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\r\n$form-select-font-weight:           $input-font-weight !default;\r\n$form-select-line-height:           $input-line-height !default;\r\n$form-select-color:                 $input-color !default;\r\n$form-select-bg:                    $input-bg !default;\r\n$form-select-disabled-color:        null !default;\r\n$form-select-disabled-bg:           $gray-200 !default;\r\n$form-select-disabled-border-color: $input-disabled-border-color !default;\r\n$form-select-bg-position:           right $form-select-padding-x center !default;\r\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\r\n$form-select-indicator-color:       $gray-800 !default;\r\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\r\n\r\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\r\n\r\n$form-select-border-width:        $input-border-width !default;\r\n$form-select-border-color:        $input-border-color !default;\r\n$form-select-border-radius:       $border-radius !default;\r\n$form-select-box-shadow:          $box-shadow-inset !default;\r\n\r\n$form-select-focus-border-color:  $input-focus-border-color !default;\r\n$form-select-focus-width:         $input-focus-width !default;\r\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\r\n\r\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\r\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\r\n$form-select-font-size-sm:        $input-font-size-sm !default;\r\n\r\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\r\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\r\n$form-select-font-size-lg:        $input-font-size-lg !default;\r\n\r\n$form-select-transition:          $input-transition !default;\r\n// scss-docs-end form-select-variables\r\n\r\n// scss-docs-start form-range-variables\r\n$form-range-track-width:          100% !default;\r\n$form-range-track-height:         .25rem !default;\r\n$form-range-track-cursor:         pointer !default;\r\n$form-range-track-bg:             tint-color($primary, 80%) !default;\r\n$form-range-track-border-radius:  1rem !default;\r\n$form-range-track-box-shadow:     $box-color-shadow rgba($primary, .1) !default;\r\n\r\n$form-range-thumb-width:                   1rem !default;\r\n$form-range-thumb-height:                  $form-range-thumb-width !default;\r\n$form-range-thumb-bg:                      $white !default;\r\n$form-range-thumb-border:                  2px solid $primary !default;\r\n$form-range-thumb-border-radius:           1rem !default;\r\n$form-range-thumb-box-shadow:              $box-color-shadow-hover rgba($primary, .5) !default;\r\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\r\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\r\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 80%) !default;\r\n$form-range-thumb-disabled-bg:             $gray-500 !default;\r\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n// scss-docs-end form-range-variables\r\n\r\n// scss-docs-start form-file-variables\r\n$form-file-button-color:          $input-color !default;\r\n$form-file-button-bg:             $input-group-addon-bg !default;\r\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%) !default;\r\n// scss-docs-end form-file-variables\r\n\r\n// scss-docs-start form-floating-variables\r\n$form-floating-height:            add(3.5rem, $input-height-border) !default;\r\n$form-floating-line-height:       1.25 !default;\r\n$form-floating-padding-x:         $input-padding-x !default;\r\n$form-floating-padding-y:         1rem !default;\r\n$form-floating-input-padding-t:   1.625rem !default;\r\n$form-floating-input-padding-b:   .625rem !default;\r\n$form-floating-label-opacity:     .65 !default;\r\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem) !default;\r\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out !default;\r\n// scss-docs-end form-floating-variables\r\n\r\n// Form validation\r\n\r\n// scss-docs-start form-feedback-variables\r\n$form-feedback-margin-top:          $form-text-margin-top !default;\r\n$form-feedback-font-size:           $form-text-font-size !default;\r\n$form-feedback-font-style:          $form-text-font-style !default;\r\n$form-feedback-valid-color:         $success !default;\r\n$form-feedback-invalid-color:       $danger !default;\r\n\r\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\r\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\r\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\r\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\r\n// scss-docs-end form-feedback-variables\r\n\r\n// scss-docs-start form-validation-states\r\n$form-validation-states: (\r\n  \"valid\": (\r\n    \"color\": $form-feedback-valid-color,\r\n    \"icon\": $form-feedback-icon-valid\r\n  ),\r\n  \"invalid\": (\r\n    \"color\": $form-feedback-invalid-color,\r\n    \"icon\": $form-feedback-icon-invalid\r\n  )\r\n) !default;\r\n// scss-docs-end form-validation-states\r\n\r\n// Z-index master list\r\n//\r\n// Warning: Avoid customizing these values. They're used for a bird's eye view\r\n// of components dependent on the z-axis and are designed to all work together.\r\n\r\n// scss-docs-start zindex-stack\r\n$zindex-general:                    900 !default;\r\n$zindex-dropdown:                   1000 !default;\r\n$zindex-sticky:                     1020 !default;\r\n$zindex-fixed:                      1030 !default;\r\n$zindex-modal-backdrop:             1040 !default;\r\n$zindex-offcanvas:                  1050 !default;\r\n$zindex-modal:                      1060 !default;\r\n$zindex-popover:                    1070 !default;\r\n$zindex-tooltip:                    1080 !default;\r\n// scss-docs-end zindex-stack\r\n\r\n\r\n// Navs\r\n\r\n// scss-docs-start nav-variables\r\n$nav-link-padding-y:                $spacer * .5 !default;\r\n$nav-link-padding-x:                $spacer * 1 !default;\r\n$nav-link-font-size:                null !default;\r\n$nav-link-font-weight:              null !default;\r\n$nav-link-color:                    $link-color !default;\r\n$nav-link-hover-color:              $link-hover-color !default;\r\n$nav-link-transition:               all 300ms ease-in-out!default;\r\n$nav-link-disabled-color:           $gray-500 !default;\r\n\r\n$nav-tabs-border-color:             $gray-300 !default;\r\n$nav-tabs-border-width:             0 !default;\r\n$nav-tabs-border-radius:            $border-radius-sm !default;\r\n$nav-tabs-shadow:                   $box-shadow-sm !default;\r\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\r\n$nav-tabs-link-active-color:        $white !default;\r\n$nav-tabs-link-active-bg:           $primary !default;\r\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\r\n\r\n$nav-pills-border-radius:           $border-radius-pill;\r\n$nav-pills-shadow:                  null !default;\r\n$nav-pills-link-active-color:       $component-active-color !default;\r\n$nav-pills-link-active-bg:          $component-active-bg !default;\r\n// scss-docs-end nav-variables\r\n\r\n\r\n// Navbar\r\n\r\n// scss-docs-start navbar-variables\r\n$navbar-padding-y:                  $spacer * .5 !default;\r\n$navbar-padding-x:                  null !default;\r\n\r\n$navbar-nav-link-padding-x:         .5rem !default;\r\n\r\n$navbar-brand-font-size:            $font-size-lg !default;\r\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\r\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\r\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\r\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\r\n$navbar-brand-margin-end:           1rem !default;\r\n\r\n$navbar-toggler-padding-y:          .25rem !default;\r\n$navbar-toggler-padding-x:          .75rem !default;\r\n$navbar-toggler-font-size:          $font-size-lg !default;\r\n$navbar-toggler-border-radius:      $btn-border-radius !default;\r\n$navbar-toggler-focus-width:        $btn-focus-width !default;\r\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\r\n// scss-docs-end navbar-variables\r\n\r\n// scss-docs-start navbar-theme-variables\r\n$navbar-dark-color:                 rgba($white, .55) !default;\r\n$navbar-dark-hover-color:           rgba($white, .75) !default;\r\n$navbar-dark-active-color:          $white !default;\r\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\r\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\r\n\r\n$navbar-light-color:                rgba($black, .55) !default;\r\n$navbar-light-hover-color:          rgba($black, .7) !default;\r\n$navbar-light-active-color:         rgba($black, .9) !default;\r\n$navbar-light-disabled-color:       rgba($black, .3) !default;\r\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\r\n\r\n$navbar-light-brand-color:                $navbar-light-active-color !default;\r\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\r\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\r\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\r\n// scss-docs-end navbar-theme-variables\r\n\r\n\r\n// Dropdowns\r\n//\r\n// Dropdown menu container and contents.\r\n\r\n// scss-docs-start dropdown-variables\r\n$dropdown-min-width:                10rem !default;\r\n$dropdown-padding-x:                0 !default;\r\n$dropdown-padding-y:                .5rem !default;\r\n$dropdown-spacer:                   .125rem !default;\r\n$dropdown-font-size:                $font-size-base !default;\r\n$dropdown-color:                    $body-color !default;\r\n$dropdown-bg:                       $white !default;\r\n$dropdown-border-color:             rgba($black, .0) !default;\r\n$dropdown-border-radius:            $border-radius-sm !default;\r\n$dropdown-border-width:             $border-width !default;\r\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\r\n$dropdown-divider-bg:               $dropdown-border-color !default;\r\n$dropdown-divider-margin-y:         $spacer * .5 !default;\r\n$dropdown-box-shadow:               $box-shadow-lg !default;\r\n\r\n$dropdown-link-color:               $gray-600 !default;\r\n$dropdown-link-hover-color:         shade-color($primary, 10%) !default;\r\n$dropdown-link-hover-bg:            transparent !default;\r\n\r\n$dropdown-link-active-color:        $component-active-color !default;\r\n$dropdown-link-active-bg:           $component-active-bg !default;\r\n\r\n$dropdown-link-disabled-color:      $gray-500 !default;\r\n\r\n$dropdown-item-padding-y:           $spacer * .25 !default;\r\n$dropdown-item-padding-x:           $spacer !default;\r\n\r\n$dropdown-header-color:             $gray-600 !default;\r\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\r\n// scss-docs-end dropdown-variables\r\n\r\n// scss-docs-start dropdown-dark-variables\r\n$dropdown-dark-color:               $gray-300 !default;\r\n$dropdown-dark-bg:                  $gray-800 !default;\r\n$dropdown-dark-border-color:        $dropdown-border-color !default;\r\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\r\n$dropdown-dark-box-shadow:          null !default;\r\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\r\n$dropdown-dark-link-hover-color:    $white !default;\r\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\r\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\r\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\r\n$dropdown-dark-link-disabled-color: $gray-500 !default;\r\n$dropdown-dark-header-color:        $gray-500 !default;\r\n// scss-docs-end dropdown-dark-variables\r\n\r\n\r\n// Pagination\r\n\r\n// scss-docs-start pagination-variables\r\n$pagination-padding-y:              .25rem !default;\r\n$pagination-padding-x:              1rem !default;\r\n$pagination-padding-y-sm:           .125rem !default;\r\n$pagination-padding-x-sm:           .75rem !default;\r\n$pagination-padding-y-lg:           .5rem !default;\r\n$pagination-padding-x-lg:           1.5rem !default;\r\n\r\n$pagination-color:                  $link-color !default;\r\n$pagination-bg:                     $white !default;\r\n$pagination-border-width:           $border-width !default;\r\n$pagination-border-radius:          $border-radius !default;\r\n$pagination-margin-start:           -$pagination-border-width !default;\r\n$pagination-border-color:           $gray-300 !default;\r\n\r\n$pagination-focus-color:            $link-hover-color !default;\r\n$pagination-focus-bg:               $gray-200 !default;\r\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\r\n$pagination-focus-outline:          0 !default;\r\n\r\n$pagination-hover-color:            $link-hover-color !default;\r\n$pagination-hover-bg:               $gray-200 !default;\r\n$pagination-hover-border-color:     $gray-300 !default;\r\n\r\n$pagination-active-color:           $component-active-color !default;\r\n$pagination-active-bg:              $component-active-bg !default;\r\n$pagination-active-border-color:    $pagination-active-bg !default;\r\n\r\n$pagination-disabled-color:         $gray-600 !default;\r\n$pagination-disabled-bg:            $white !default;\r\n$pagination-disabled-border-color:  $gray-300 !default;\r\n\r\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$pagination-border-radius-sm:       $border-radius-sm !default;\r\n$pagination-border-radius-lg:       $border-radius !default;\r\n// scss-docs-end pagination-variables\r\n\r\n\r\n// Cards\r\n\r\n// scss-docs-start card-variables\r\n$card-spacer-y:                     $spacer * 1.5 !default;\r\n$card-spacer-x:                     $spacer * 1.5 !default;\r\n$card-title-spacer-y:               $spacer * .5 !default;\r\n$card-border-width:                 0 !default;\r\n$card-border-radius:                $border-radius;\r\n$card-border-color:                 rgba($black, .125) !default;\r\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\r\n$card-cap-padding-y:                $card-spacer-y !default;\r\n$card-cap-padding-x:                $card-spacer-x !default;\r\n$card-cap-bg:                       $white !default;\r\n$card-cap-color:                    null !default;\r\n$card-height:                       null !default;\r\n$card-color:                        null !default;\r\n$card-bg:                           $white !default;\r\n$card-img-overlay-padding:          $spacer !default;\r\n$card-group-margin:                 $grid-gutter-width * .5 !default;\r\n\r\n$card-box-shadow:                   $box-shadow-lg !default;\r\n// scss-docs-end card-variables\r\n\r\n// Accordion\r\n\r\n// scss-docs-start accordion-variables\r\n$accordion-padding-y:                     1rem !default;\r\n$accordion-padding-x:                     1.5rem !default;\r\n$accordion-color:                         $body-color !default;\r\n$accordion-bg:                            $body-bg !default;\r\n$accordion-border-width:                  $border-width !default;\r\n$accordion-border-color:                  rgba($black, .125) !default;\r\n$accordion-border-radius:                 $border-radius !default;\r\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\r\n\r\n$accordion-body-padding-y:                $accordion-padding-y !default;\r\n$accordion-body-padding-x:                $accordion-padding-x !default;\r\n\r\n$accordion-button-padding-y:              $accordion-padding-y !default;\r\n$accordion-button-padding-x:              $accordion-padding-x !default;\r\n$accordion-button-color:                  $accordion-color !default;\r\n$accordion-button-bg:                     $accordion-bg !default;\r\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\r\n$accordion-button-active-bg:              tint-color($component-active-bg, 90%) !default;\r\n$accordion-button-active-color:           shade-color($primary, 10%) !default;\r\n\r\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\r\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\r\n\r\n$accordion-icon-width:                    1.25rem !default;\r\n$accordion-icon-color:                    $accordion-color !default;\r\n$accordion-icon-active-color:             $accordion-button-active-color !default;\r\n$accordion-icon-transition:               transform .2s ease-in-out !default;\r\n$accordion-icon-transform:                rotate(-180deg) !default;\r\n\r\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n// scss-docs-end accordion-variables\r\n\r\n// Tooltips\r\n\r\n// scss-docs-start tooltip-variables\r\n$tooltip-font-size:                 $font-size-sm !default;\r\n$tooltip-max-width:                 200px !default;\r\n$tooltip-color:                     $white !default;\r\n$tooltip-bg:                        $black !default;\r\n$tooltip-border-radius:             $border-radius-sm !default;\r\n$tooltip-opacity:                   .9 !default;\r\n$tooltip-padding-y:                 $spacer * .25 !default;\r\n$tooltip-padding-x:                 $spacer * .5 !default;\r\n$tooltip-margin:                    0 !default;\r\n\r\n$tooltip-arrow-width:               .8rem !default;\r\n$tooltip-arrow-height:              .4rem !default;\r\n$tooltip-arrow-color:               $tooltip-bg !default;\r\n// scss-docs-end tooltip-variables\r\n\r\n// Form tooltips must come after regular tooltips\r\n// scss-docs-start tooltip-feedback-variables\r\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\r\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\r\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\r\n$form-feedback-tooltip-line-height:   null !default;\r\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\r\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\r\n// scss-docs-end tooltip-feedback-variables\r\n\r\n\r\n// Popovers\r\n\r\n// scss-docs-start popover-variables\r\n$popover-font-size:                 $font-size-sm !default;\r\n$popover-bg:                        $white !default;\r\n$popover-max-width:                 276px !default;\r\n$popover-border-width:              $border-width !default;\r\n$popover-border-color:              rgba($black, .1) !default;\r\n$popover-border-radius:             $border-radius-sm !default;\r\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\r\n$popover-box-shadow:                $box-shadow !default;\r\n\r\n$popover-header-bg:                 shade-color($popover-bg, 6%) !default;\r\n$popover-header-color:              $headings-color !default;\r\n$popover-header-padding-y:          .5rem !default;\r\n$popover-header-padding-x:          $spacer !default;\r\n\r\n$popover-body-color:                $body-color !default;\r\n$popover-body-padding-y:            $spacer !default;\r\n$popover-body-padding-x:            $spacer !default;\r\n\r\n$popover-arrow-width:               1rem !default;\r\n$popover-arrow-height:              .5rem !default;\r\n$popover-arrow-color:               $popover-bg !default;\r\n\r\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\r\n// scss-docs-end popover-variables\r\n\r\n\r\n// Toasts\r\n\r\n// scss-docs-start toast-variables\r\n$toast-max-width:                   350px !default;\r\n$toast-padding-x:                   1rem !default;\r\n$toast-padding-y:                   .5rem !default;\r\n$toast-font-size:                   .875rem !default;\r\n$toast-color:                       null !default;\r\n$toast-background-color:            rgba($white, .85) !default;\r\n$toast-border-width:                1px !default;\r\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\r\n$toast-border-radius:               $border-radius-sm !default;\r\n$toast-box-shadow:                  $box-shadow !default;\r\n$toast-spacing:                     $container-padding-x !default;\r\n\r\n$toast-header-color:                $gray-600 !default;\r\n$toast-header-background-color:     rgba($white, .85) !default;\r\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\r\n// scss-docs-end toast-variables\r\n\r\n\r\n// Badges\r\n\r\n// scss-docs-start badge-variables\r\n$badge-font-size:                   .75em !default;\r\n$badge-font-weight:                 $font-weight-bold !default;\r\n$badge-color:                       $white !default;\r\n$badge-padding-y:                   .125rem !default;\r\n$badge-padding-x:                   .5rem !default;\r\n$badge-border-radius:               $border-radius-sm !default;\r\n// scss-docs-end badge-variables\r\n\r\n\r\n// Modals\r\n\r\n// scss-docs-start modal-variables\r\n$modal-inner-padding:               $spacer !default;\r\n\r\n$modal-footer-margin-between:       .5rem !default;\r\n\r\n$modal-dialog-margin:               .5rem !default;\r\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\r\n\r\n$modal-title-line-height:           $line-height-base !default;\r\n\r\n$modal-content-color:               null !default;\r\n$modal-content-bg:                  $white !default;\r\n$modal-content-border-color:        rgba($black, .1) !default;\r\n$modal-content-border-width:        $border-width !default;\r\n$modal-content-border-radius:       $border-radius !default;\r\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\r\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\r\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\r\n\r\n$modal-backdrop-bg:                 $black !default;\r\n$modal-backdrop-opacity:            .5 !default;\r\n$modal-header-border-color:         $border-color !default;\r\n$modal-footer-border-color:         $modal-header-border-color !default;\r\n$modal-header-border-width:         $modal-content-border-width !default;\r\n$modal-footer-border-width:         $modal-header-border-width !default;\r\n$modal-header-padding-y:            $modal-inner-padding !default;\r\n$modal-header-padding-x:            $modal-inner-padding !default;\r\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n\r\n$modal-sm:                          300px !default;\r\n$modal-md:                          500px !default;\r\n$modal-lg:                          800px !default;\r\n$modal-xl:                          1140px !default;\r\n\r\n$modal-fade-transform:              translate(0, -50px) !default;\r\n$modal-show-transform:              none !default;\r\n$modal-transition:                  transform .3s ease-out !default;\r\n$modal-scale-transform:             scale(1.02) !default;\r\n// scss-docs-end modal-variables\r\n\r\n\r\n// Alerts\r\n//\r\n// Define alert colors, border radius, and padding.\r\n\r\n// scss-docs-start alert-variables\r\n$alert-padding-y:               $spacer !default;\r\n$alert-padding-x:               $spacer !default;\r\n$alert-margin-bottom:           1rem !default;\r\n$alert-border-radius:           .25rem;\r\n$alert-link-font-weight:        $font-weight-bold !default;\r\n$alert-border-width:            $border-width * 2 !default;\r\n$alert-bg-scale:                -80% !default;\r\n$alert-border-scale:            0% !default;\r\n$alert-color-scale:             30% !default;\r\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\r\n// scss-docs-end alert-variables\r\n\r\n\r\n// Progress bars\r\n\r\n// scss-docs-start progress-variables\r\n$progress-height:                   1rem !default;\r\n$progress-font-size:                $font-size-base * .75 !default;\r\n$progress-bg:                       $gray-200 !default;\r\n$progress-border-radius:            $border-radius !default;\r\n$progress-box-shadow:               $box-shadow-inset !default;\r\n$progress-bar-color:                $white !default;\r\n$progress-bar-bg:                   $primary !default;\r\n$progress-bar-animation-timing:     1s linear infinite !default;\r\n$progress-bar-transition:           width .6s ease !default;\r\n// scss-docs-end progress-variables\r\n\r\n\r\n// List group\r\n\r\n// scss-docs-start list-group-variables\r\n$list-group-color:                  $gray-900 !default;\r\n$list-group-bg:                     $white !default;\r\n$list-group-border-color:           rgba($black, .125) !default;\r\n$list-group-border-width:           $border-width !default;\r\n$list-group-border-radius:          $border-radius-sm !default;\r\n\r\n$list-group-item-padding-y:         $spacer * .5 !default;\r\n$list-group-item-padding-x:         $spacer !default;\r\n$list-group-item-bg-scale:          -80% !default;\r\n$list-group-item-color-scale:       40% !default;\r\n\r\n$list-group-hover-bg:               $gray-100 !default;\r\n$list-group-active-color:           $component-active-color !default;\r\n$list-group-active-bg:              $component-active-bg !default;\r\n$list-group-active-border-color:    $list-group-active-bg !default;\r\n\r\n$list-group-disabled-color:         $gray-600 !default;\r\n$list-group-disabled-bg:            $list-group-bg !default;\r\n\r\n$list-group-action-color:           $gray-700 !default;\r\n$list-group-action-hover-color:     $list-group-action-color !default;\r\n\r\n$list-group-action-active-color:    $body-color !default;\r\n$list-group-action-active-bg:       $gray-200 !default;\r\n// scss-docs-end list-group-variables\r\n\r\n\r\n// Image thumbnails\r\n\r\n// scss-docs-start thumbnail-variables\r\n$thumbnail-padding:                 .25rem !default;\r\n$thumbnail-bg:                      $body-bg !default;\r\n$thumbnail-border-width:            $border-width !default;\r\n$thumbnail-border-color:            $gray-300 !default;\r\n$thumbnail-border-radius:           $border-radius !default;\r\n$thumbnail-box-shadow:              $box-shadow-sm !default;\r\n// scss-docs-end thumbnail-variables\r\n\r\n\r\n// Figures\r\n\r\n// scss-docs-start figure-variables\r\n$figure-caption-font-size:          $small-font-size !default;\r\n$figure-caption-color:              $gray-600 !default;\r\n// scss-docs-end figure-variables\r\n\r\n\r\n// Breadcrumbs\r\n\r\n// scss-docs-start breadcrumb-variables\r\n$breadcrumb-font-size:              null !default;\r\n$breadcrumb-padding-y:              0 !default;\r\n$breadcrumb-padding-x:              0 !default;\r\n$breadcrumb-item-padding-x:         .5rem !default;\r\n$breadcrumb-margin-bottom:          1rem !default;\r\n$breadcrumb-bg:                     null !default;\r\n$breadcrumb-divider-color:          $gray-600 !default;\r\n$breadcrumb-active-color:           $gray-600 !default;\r\n$breadcrumb-divider:                quote(\"/\") !default;\r\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\r\n$breadcrumb-border-radius:          null !default;\r\n// scss-docs-end breadcrumb-variables\r\n\r\n// Carousel\r\n\r\n// scss-docs-start carousel-variables\r\n$carousel-control-color:             $white !default;\r\n$carousel-control-width:             15% !default;\r\n$carousel-control-opacity:           .5 !default;\r\n$carousel-control-hover-opacity:     .9 !default;\r\n$carousel-control-transition:        opacity .15s ease !default;\r\n\r\n$carousel-indicator-width:           20px !default;\r\n$carousel-indicator-height:          20px !default;\r\n$carousel-indicator-hit-area-height: 0px !default;\r\n$carousel-indicator-spacer:          3px !default;\r\n$carousel-indicator-opacity:         .5 !default;\r\n$carousel-indicator-active-bg:       $white !default;\r\n$carousel-indicator-active-opacity:  1 !default;\r\n$carousel-indicator-transition:      opacity .6s ease !default;\r\n\r\n$carousel-caption-width:             70% !default;\r\n$carousel-caption-color:             $white !default;\r\n$carousel-caption-padding-y:         1.25rem !default;\r\n$carousel-caption-spacer:            1.25rem !default;\r\n\r\n$carousel-control-icon-width:        2rem !default;\r\n\r\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\r\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n\r\n$carousel-transition-duration:       .6s !default;\r\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\r\n\r\n$carousel-dark-indicator-active-bg:  $black !default;\r\n$carousel-dark-caption-color:        $black !default;\r\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\r\n// scss-docs-end carousel-variables\r\n\r\n\r\n// Spinners\r\n\r\n// scss-docs-start spinner-variables\r\n$spinner-width:           2rem !default;\r\n$spinner-height:          $spinner-width !default;\r\n$spinner-vertical-align:  -.125em !default;\r\n$spinner-border-width:    .25em !default;\r\n$spinner-animation-speed: .75s !default;\r\n\r\n$spinner-width-sm:        1rem !default;\r\n$spinner-height-sm:       $spinner-width-sm !default;\r\n$spinner-border-width-sm: .2em !default;\r\n// scss-docs-end spinner-variables\r\n\r\n\r\n// Close\r\n\r\n// scss-docs-start close-variables\r\n$btn-close-width:            1em !default;\r\n$btn-close-height:           $btn-close-width !default;\r\n$btn-close-padding-x:        .25em !default;\r\n$btn-close-padding-y:        $btn-close-padding-x !default;\r\n$btn-close-color:            $black !default;\r\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\r\n$btn-close-focus-shadow:     $input-btn-focus-box-shadow !default;\r\n$btn-close-opacity:          1 !default;\r\n$btn-close-radius:           $border-radius-sm !default;\r\n$btn-close-hover-opacity:    1 !default;\r\n$btn-close-focus-opacity:    1 !default;\r\n$btn-close-disabled-opacity: .25 !default;\r\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\r\n// scss-docs-end close-variables\r\n\r\n\r\n// Offcanvas\r\n\r\n// scss-docs-start offcanvas-variables\r\n$offcanvas-padding-y:               $modal-inner-padding !default;\r\n$offcanvas-padding-x:               $modal-inner-padding !default;\r\n$offcanvas-horizontal-width:        400px !default;\r\n$offcanvas-vertical-height:         30vh !default;\r\n$offcanvas-transition-duration:     .3s !default;\r\n$offcanvas-border-color:            $modal-content-border-color !default;\r\n$offcanvas-border-width:            $modal-content-border-width !default;\r\n$offcanvas-title-line-height:       $modal-title-line-height !default;\r\n$offcanvas-bg-color:                $modal-content-bg !default;\r\n$offcanvas-color:                   $modal-content-color !default;\r\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\r\n// scss-docs-end offcanvas-variables\r\n\r\n// Code\r\n\r\n$code-font-size:                    $small-font-size !default;\r\n$code-color:                        $pink !default;\r\n\r\n$kbd-padding-y:                     .2rem !default;\r\n$kbd-padding-x:                     .4rem !default;\r\n$kbd-font-size:                     $code-font-size !default;\r\n$kbd-color:                         $white !default;\r\n$kbd-bg:                            $gray-900 !default;\r\n\r\n$pre-color:                         null !default;", ".custom-accordion {\r\n    .accordion-button {\r\n        color: tint-color($body-color, 30%);\r\n    }\r\n    .accordion-item {\r\n        background: $card-bg;\r\n    }\r\n}\r\n.accordion{\r\n    .accordion-item{\r\n        background-color: $body-bg;\r\n        color: $white;\r\n        .accordion-header{\r\n            .accordion-button{\r\n                background-color: $border-color;\r\n                color: $white;\r\n                &:not(.collapsed){\r\n                    color: $white;\r\n                    background-color: $border-color;\r\n                }\r\n                &:focus{\r\n                    box-shadow: unset;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n", ".btn-border {\r\n    border: $border-width * 2 solid $border-color;\r\n    &:hover {\r\n        border-color: lighten($border-color, 20%);\r\n    }\r\n    &.active {\r\n        border-color: var(--bs-primary);\r\n    }\r\n}\r\n.bd-aside{\r\n    .btn:hover, .btn:focus, .active, a:hover{\r\n        color: #fff;\r\n        background-color: #151824;\r\n        box-shadow: unset;\r\n    }\r\n}", "// Soft Button\r\n@each $color, $value in $theme-colors {\r\n    .btn-soft-#{$color} {\r\n        @include button-variant(\r\n          rgba($value, .1), rgba($value, .0), tint-color($value, 20%),\r\n          rgba($value, .2), rgba($value, .2), tint-color($value, 30%),\r\n          rgba($value, .3), rgba($value, .2), rgba($value,1), tint-color($value, 50%));\r\n        box-shadow: unset;\r\n    }\r\n}", "// Button variants\r\n//\r\n// Easily pump out default styles, as well as :hover, :focus, :active,\r\n// and disabled options for all buttons\r\n\r\n// scss-docs-start btn-variant-mixin\r\n@mixin button-variant(\r\n  $background,\r\n  $border,\r\n  $color: color-contrast($background),\r\n  $hover-background: if($color == $color-contrast-light, shade-color($background, $btn-hover-bg-shade-amount), tint-color($background, $btn-hover-bg-tint-amount)),\r\n  $hover-border: if($color == $color-contrast-light, shade-color($border, $btn-hover-border-shade-amount), tint-color($border, $btn-hover-border-tint-amount)),\r\n  $hover-color: color-contrast($hover-background),\r\n  $active-background: if($color == $color-contrast-light, shade-color($background, $btn-active-bg-shade-amount), tint-color($background, $btn-active-bg-tint-amount)),\r\n  $active-border: if($color == $color-contrast-light, shade-color($border, $btn-active-border-shade-amount), tint-color($border, $btn-active-border-tint-amount)),\r\n  $active-color: color-contrast($active-background),\r\n  $disabled-background: $background,\r\n  $disabled-border: $border,\r\n  $disabled-color: color-contrast($disabled-background)\r\n) {\r\n  --#{$prefix}btn-color: #{$color};\r\n  --#{$prefix}btn-bg: #{$background};\r\n  --#{$prefix}btn-border-color: #{$border};\r\n  --#{$prefix}btn-hover-color: #{$hover-color};\r\n  --#{$prefix}btn-hover-bg: #{$hover-background};\r\n  --#{$prefix}btn-hover-border-color: #{$hover-border};\r\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb(mix($color, $border, 15%))};\r\n  --#{$prefix}btn-active-color: #{$active-color};\r\n  --#{$prefix}btn-active-bg: #{$active-background};\r\n  --#{$prefix}btn-active-border-color: #{$active-border};\r\n  --#{$prefix}btn-active-shadow: #{$btn-active-box-shadow};\r\n  --#{$prefix}btn-disabled-color: #{$disabled-color};\r\n  --#{$prefix}btn-disabled-bg: #{$disabled-background};\r\n  --#{$prefix}btn-disabled-border-color: #{$disabled-border};\r\n}\r\n// scss-docs-end btn-variant-mixin\r\n\r\n// scss-docs-start btn-outline-variant-mixin\r\n@mixin button-outline-variant(\r\n  $color,\r\n  $color-hover: color-contrast($color),\r\n  $active-background: $color,\r\n  $active-border: $color,\r\n  $active-color: color-contrast($active-background)\r\n) {\r\n  --#{$prefix}btn-color: #{$color};\r\n  --#{$prefix}btn-border-color: #{$color};\r\n  --#{$prefix}btn-hover-color: #{$color-hover};\r\n  --#{$prefix}btn-hover-bg: #{$active-background};\r\n  --#{$prefix}btn-hover-border-color: #{$active-border};\r\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb($color)};\r\n  --#{$prefix}btn-active-color: #{$active-color};\r\n  --#{$prefix}btn-active-bg: #{$active-background};\r\n  --#{$prefix}btn-active-border-color: #{$active-border};\r\n  --#{$prefix}btn-active-shadow: #{$btn-active-box-shadow};\r\n  --#{$prefix}btn-disabled-color: #{$color};\r\n  --#{$prefix}btn-disabled-bg: transparent;\r\n  --#{$prefix}btn-disabled-border-color: #{$color};\r\n  --#{$prefix}gradient: none;\r\n}\r\n// scss-docs-end btn-outline-variant-mixin\r\n\r\n// scss-docs-start btn-size-mixin\r\n@mixin button-size($padding-y, $padding-x, $font-size, $border-radius) {\r\n  --#{$prefix}btn-padding-y: #{$padding-y};\r\n  --#{$prefix}btn-padding-x: #{$padding-x};\r\n  @include rfs($font-size, --#{$prefix}btn-font-size);\r\n  --#{$prefix}btn-border-radius: #{$border-radius};\r\n}\r\n// scss-docs-end btn-size-mixin\r\n", ".btn-close {\r\n    filter: invert(1);\r\n}", "&.boxed {\r\n    background: var(--#{$variable-prefix}primary) !important;\r\n    .boxed-inner {\r\n        background: $body-bg;\r\n    }\r\n}\r\n&.boxed-fancy {\r\n    background: $body-bg;\r\n    .boxed-inner{\r\n        background: $body-bg; \r\n    }\r\n}", "&.dual-compact{\r\n    .nav-underline .nav-link:hover {\r\n        color: var(--#{$variable-prefix}primary);\r\n      }\r\n    .nav-underline .active {\r\n        color: $gray-300;\r\n      }\r\n}", ".dual-horizontal {\r\n    .nav {\r\n        background-color: var(--#{$variable-prefix}primary);\r\n    }\r\n}", ".sidebar {\r\n    background-color: $card-bg;\r\n    .navbar-brand {\r\n        .logo-title {\r\n            color: $white;\r\n        }\r\n    }\r\n    .sidebar-header {\r\n        border-color: $border-color;\r\n    }\r\n}\r\n.sidebar-list{\r\n    .navbar-nav{\r\n        .nav-item{\r\n            .nav-link.static-item{\r\n                .default-icon{\r\n                    color: $light;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// Sidebar Background Colors\r\n.sidebar {\r\n    &.sidebar-dark {\r\n        background-color: $navbar-vertical-dark-bg;\r\n    }\r\n    &.sidebar-color {\r\n        background-color: $navbar-vertical-color-bg;\r\n    }\r\n    &.sidebar-transparent {\r\n        background-color: transparent;\r\n    }\r\n    // &.sidebar-white{\r\n    //     background-color:$white;\r\n    //     .navbar-brand{\r\n    //         .logo-title{\r\n    //             color: $dark;\r\n    //         }\r\n    //     }\r\n    // }\r\n    \r\n}\r\n", "$navbar-vertical-dark-bg: $dark !default;\r\n$navbar-vertical-color-bg: var(--#{$variable-prefix}primary) !default;\r\n\r\n$navbar-vertical-width: 16.2rem !default;\r\n$navbar-vertical-mini-width: 4.8rem !default;\r\n\r\n$navbar-vertical-active-shadow: 0 10px 20px -10px rgba(darken($primary, 25%), .4);\r\n$navbar-vertical-hover-shadow: null;\r\n\r\n$navbar-vertical-shadow-enable: true;\r\n$navbar-horizontal-shadow-enable: true;\r\n\r\n$navbar-vertical-shadow: 0 0px 30px 0 rgba(darken($primary, 25%), .05);\r\n$navbar-horizontal-shadow: 0 0px 30px 0 rgba(darken($primary, 25%), .05);\r\n\r\n$navbar-vertical-transition: all;\r\n$navbar-vertical-transition-duration: 400ms;\r\n$navbar-vertical-transition-function-ease: ease;\r\n$navbar-vertical-transition-function-ease-in-out: ease-in-out;\r\n\r\n\r\n$navbar-small-item: .625rem .75rem;\r\n", ".sidebar{\r\n    &.sidebar-default{\r\n        .nav-link{\r\n            &:not(.static-item){\r\n                &:hover{\r\n                    &:not(.active){\r\n                        &:not([aria-expanded=true]){\r\n                            background-color: rgba(var(--#{$variable-prefix}primary-rgb), .2);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n", ".footer {\r\n    background-color: $card-bg !important;\r\n    a {\r\n        color: $body-color;\r\n        transition: all 400ms ease;\r\n        &:hover {\r\n            transition: all 400ms ease;\r\n            color: $primary;\r\n        }\r\n    }\r\n}", ".nav{\r\n    background: $card-bg;\r\n    .navbar-brand {\r\n\t\t.logo-title {\r\n\t\t\tcolor: $white;\r\n\t\t}\r\n\t}\r\n}\r\n.iq-navbar {\r\n    border-color: $card-bg;\r\n    background-color: $card-bg;\r\n    .navbar-nav{\r\n        .nav-item{\r\n            .nav-link{\r\n                color: $white;\r\n                &:hover {\r\n                    color: tint-color($body-color, 40%);\r\n                }\r\n                &.active{\r\n                    color: var(--#{$variable-prefix}primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\t.navbar-brand {\r\n\t\t.logo-title {\r\n\t\t\tcolor: $white;\r\n\t\t}\r\n\t}\r\n    &.nav-glass{\r\n        background:linear-gradient(120deg, rgba($dark, 0.5) -8%, rgba($dark, 0.01) 120%);\r\n        border-color: $border-color;\r\n    }\r\n    .dropdown {\r\n        .dropdown-menu {\r\n            &.sub-drop {\r\n                li {\r\n                    a {\r\n                        color: $body-color;\r\n                    }\r\n                }\r\n                .iq-sub-card {\r\n                    color: $body-color;\r\n                    &:hover{\r\n                        background: shade-color($primary, 90%)\r\n                    }\r\n                    &:not(:last-child) {\r\n                        border-bottom: $border-width solid $border-color;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.list-group-item{\r\n    background-color: unset;\r\n}\r\n\r\n@include media-breakpoint-down(lg){\r\n    .iq-navbar {\r\n        .navbar-collapse {\r\n            &:not(.offcanvas-collapse) {\r\n                background: $card-bg;\r\n            }\r\n        }\r\n    }\r\n}\r\n.nav-tabs{\r\n    .nav-link{\r\n        &.active{\r\n            box-shadow: unset;\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\r\n//\r\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\r\n//\r\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\r\n//\r\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\r\n\r\n// Name of the next breakpoint, or null for the last breakpoint.\r\n//\r\n//    >> breakpoint-next(sm)\r\n//    md\r\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    md\r\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\r\n//    md\r\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\r\n  $n: index($breakpoint-names, $name);\r\n  @if not $n {\r\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\r\n  }\r\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\r\n}\r\n\r\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\r\n//\r\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    576px\r\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\r\n  $min: map-get($breakpoints, $name);\r\n  @return if($min != 0, $min, null);\r\n}\r\n\r\n// Maximum breakpoint width.\r\n// The maximum value is reduced by 0.02px to work around the limitations of\r\n// `min-` and `max-` prefixes and viewports with fractional widths.\r\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\r\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\r\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\r\n//\r\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    767.98px\r\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\r\n  $max: map-get($breakpoints, $name);\r\n  @return if($max and $max > 0, $max - .02, null);\r\n}\r\n\r\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\r\n// Useful for making responsive utilities.\r\n//\r\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    \"\"  (Returns a blank string)\r\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    \"-sm\"\r\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\r\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\r\n}\r\n\r\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\r\n// Makes the @content apply to the given breakpoint and wider.\r\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($name, $breakpoints);\r\n  @if $min {\r\n    @media (min-width: $min) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\r\n// Makes the @content apply to the given breakpoint and narrower.\r\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\r\n  $max: breakpoint-max($name, $breakpoints);\r\n  @if $max {\r\n    @media (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media that spans multiple breakpoint widths.\r\n// Makes the @content apply between the min and max breakpoints\r\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($lower, $breakpoints);\r\n  $max: breakpoint-max($upper, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($lower, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($upper, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Media between the breakpoint's minimum and maximum widths.\r\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\r\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\r\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\r\n  $min:  breakpoint-min($name, $breakpoints);\r\n  $next: breakpoint-next($name, $breakpoints);\r\n  $max:  breakpoint-max($next, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($name, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($next, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n", "\r\n@each $state, $value in $theme-colors {\r\n    $alert-background: shift-color($value, $alert-bg-scale);\r\n    $alert-border: shift-color($value, $alert-border-scale);\r\n    $alert-color: shift-color($value, $alert-color-scale);\r\n    @if (contrast-ratio($alert-background, $alert-color) < $min-contrast-ratio) {\r\n      $alert-color: mix($value, color-contrast($alert-background), abs($alert-color-scale));\r\n    }\r\n    .alert-#{$state} {\r\n      @include alert-variant($alert-background, $alert-border, $alert-color);\r\n      .btn-close {\r\n        filter: $btn-close-white-filter;\r\n      }\r\n    }\r\n}\r\n  \r\n.alert-solid {\r\n    @each $state, $value in $theme-colors {\r\n        &.alert-#{$state} {\r\n            @include alert-variant($value, $value, #fff);\r\n            .btn-close {\r\n                filter: unset;\r\n              }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.alert-left {\r\n    @each $state, $value in $theme-colors {\r\n        &.alert-#{$state} {\r\n            border-color: $value;\r\n        }\r\n    }\r\n}\r\n.alert-top {\r\n    @each $state, $value in $theme-colors {\r\n        &.alert-#{$state} {\r\n            border-color: $value;\r\n        }\r\n    }\r\n}\r\n.alert-right {\r\n    @each $state, $value in $theme-colors {\r\n        &.alert-#{$state} {\r\n            border-color: $value;\r\n        }\r\n    }\r\n}\r\n.alert-bottom {\r\n    @each $state, $value in $theme-colors {\r\n        &.alert-#{$state} {\r\n            border-color: $value;\r\n        }\r\n    }\r\n}\r\n", "// scss-docs-start alert-variant-mixin\r\n@mixin alert-variant($background, $border, $color) {\r\n  --#{$prefix}alert-color: #{$color};\r\n  --#{$prefix}alert-bg: #{$background};\r\n  --#{$prefix}alert-border-color: #{$border};\r\n\r\n  @if $enable-gradients {\r\n    background-image: var(--#{$prefix}gradient);\r\n  }\r\n\r\n  .alert-link {\r\n    color: shade-color($color, 20%);\r\n  }\r\n}\r\n// scss-docs-end alert-variant-mixin\r\n", ".card {\r\n    background: $card-bg;\r\n    .card-header, .card-footer {\r\n        background-color: $card-bg;\r\n        border-bottom-color: $border-color;\r\n    }\r\n}\r\n\r\n.card-footer{\r\n    border-top: 1px solid $border-color;\r\n}\r\n\r\n", ".apexcharts-theme-light{\r\n    .apexcharts-tooltip{\r\n        background: $card-bg;\r\n        border: 1px solid $border-color;\r\n            .apexcharts-tooltip-title{\r\n                background: $card-bg;\r\n                border: 1px solid $border-color;\r\n            }\r\n    }\r\n    .apexcharts-xaxistooltip{\r\n        background: $card-bg;\r\n        border: 1px solid $border-color;\r\n        .apexcharts-xaxistooltip-text{\r\n            color: $body-color;\r\n        }\r\n    }\r\n}", ".icon-box {\r\n    border-color: $border-color;\r\n    color: $secondary;\r\n    .overlay {\r\n        background-color: $body-bg;\r\n    }\r\n    &:hover {\r\n        .overlay{\r\n            .btn{\r\n                color: $white;\r\n            }\r\n        }\r\n    }\r\n}", ".form-control {\r\n    color: $body-color;\r\n    background-color: $card-bg;\r\n    border-color: $border-color;\r\n    &.is-invalid{\r\n        border-color: $danger;\r\n    }\r\n    &.is-valid{\r\n        border-color: $success;\r\n    }\r\n}\r\n.form-check-input{\r\n    color: $body-color;\r\n    background-color: $card-bg;\r\n    border-color: $border-color;\r\n}\r\n.form-select {\r\n    color: $body-color;\r\n    background-color: $card-bg;\r\n    &.is-invalid{\r\n        border-color: $danger;\r\n    }\r\n}\r\n.input-group-text {\r\n    background-color: $card-bg;\r\n    border-color: $border-color;\r\n}\r\n.stepwizard-row {\r\n    a {\r\n        &.btn {\r\n            color: $body-color;\r\n            background-color: $body-bg;\r\n        }\r\n    }\r\n}\r\n#top-tabbar-vertical {\r\n    li {\r\n        a {\r\n            color: $body-color;\r\n            background-color: $body-bg;\r\n        }\r\n    }\r\n}\r\n.custom-form-floating>.form-control:focus~label, \r\n.custom-form-floating>.form-control:not(:placeholder-shown)~label, \r\n.custom-form-floating>.form-select~label{\r\n    background-color: $card-bg;\r\n}", "#top-tab-list {\r\n    li {\r\n        a {\r\n            color: $card-bg;\r\n            background: $body-bg;\r\n            \r\n            .iq-icon {\r\n                background: $card-bg;\r\n                .svg-icon{\r\n                    color: $white;\r\n                }\r\n            }\r\n\r\n        }\r\n        &.active {\r\n            a {\r\n                background: var(--#{$variable-prefix}primary);\r\n                color: $white;  \r\n\r\n                .iq-icon {\r\n                    background: $white;\r\n                    .svg-icon\r\n                    {\r\n                        color: var(--#{$variable-prefix}primary);\r\n                    } \r\n                }\r\n            }\r\n            &.done {\r\n                a {\r\n                    background: $success;\r\n                    i {\r\n                        color: $success; \r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &#confirm {\r\n            &.active {\r\n                a {\r\n                    background: $success;\r\n    \r\n                    i {\r\n                        color: $success; \r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", ".table {\r\n\tthead {\r\n\t\ttr {\r\n\t\t\tth {\r\n                background-color: $body-bg;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n    tbody {\r\n        tr {\r\n            td {\r\n               color: $light;\r\n            }\r\n        }\r\n    }\r\n}", ".dropdown-menu {\r\n    background-color: $body-bg;\r\n    color: $body-color;\r\n    .dropdown-item {\r\n        color: $body-color;\r\n        &:hover {\r\n            color: $primary;\r\n            background-color: transparent;\r\n        }\r\n    }\r\n}\r\n.like-block {\r\n    span {\r\n        color: $body-color;\r\n    }\r\n}\r\n\r\n\r\n.dropdown>.dropdown-menu {\r\n    background-color: $body-bg;\r\n}\r\n.dropdown>.dropdown-menu>a{\r\n    color: $body-color;\r\n}\r\n", ".profile-media {\r\n    &::before {\r\n        border-color: $border-color;\r\n    }\r\n}\r\n.profile-dots-pills {\r\n    background-color: $card-bg;\r\n}\r\n.profile-story-img{\r\n    filter: invert(100%);\r\n}", ".iq-timeline {\r\n\t&:before {\r\n        background-color: $border-color;\r\n\t}\r\n    li {\r\n        .timeline-dots {\r\n            background-color: $body-bg !important;\r\n        }\r\n    }\r\n}\r\n.iq-timeline0 {\r\n\t&:before {\r\n        background-color: $border-color;\r\n\t}\r\n    li {\r\n        .timeline-dots {\r\n            background-color: $body-bg !important;\r\n        }\r\n        .timeline-dots1 {\r\n            background-color: $body-bg;\r\n        }\r\n    }\r\n}", ".credit-card-widget {\r\n\t.primary-gradient-card {\r\n\t\tborder-color:$black !important;\r\n\t}\r\n}\r\n.credit-card-widget{\r\n    .primary-gradient-card{\r\n        background:linear-gradient(117.76deg, rgba(000, 000, 000, 0.5) -7.21%, rgba(000, 000, 000, 0.01) 118.08%);\r\n\t}\r\n}\r\n", "// Circle Progress\r\n.circle-progress-circle {\r\n\tstroke: $secondary;\r\n}", ".page-item {\r\n\t&.active {\r\n\t\t.page-link {\r\n            z-index: 3;\r\n            color: $white;\r\n            background-color: $primary;\r\n            border-color: $secondary;\r\n        }\r\n    }\r\n} \r\n.page-item {\r\n    .page-link {\r\n        background-color: $card-bg;\r\n        border-color: $secondary;\r\n        color: $body-color;\r\n            &.disabled {\r\n                .page-link {\r\n                background-color: $body-bg;\r\n            }\r\n        }\r\n    }\r\n}\r\n.list-group{\r\n    .list-group-item{\r\n        color: $body-color;\r\n        border-color: $border-color;\r\n    }\r\n}\r\n.bg-light {\r\n    background: $dark !important;\r\n}", ".popover{\r\n    background-color: $body-bg;\r\n}\r\n.popover-header{\r\n    background-color: $card-bg;\r\n}\r\n.bs-popover-end>.popover-arrow::after, \r\n.bs-popover-auto[data-popper-placement^=right]>.popover-arrow::after\r\n{\r\n    border-right-color: $body-bg;\r\n}\r\n.bs-popover-top>.popover-arrow::after, \r\n.bs-popover-auto[data-popper-placement^=top]>.popover-arrow::after{\r\n    border-top-color: $body-bg;\r\n}\r\n.bs-popover-bottom>.popover-arrow::after, \r\n.bs-popover-auto[data-popper-placement^=bottom]>.popover-arrow::after{\r\n    border-bottom-color: $body-bg;\r\n}\r\n.bs-popover-start>.popover-arrow::after, \r\n.bs-popover-auto[data-popper-placement^=left]>.popover-arrow::after{\r\n    border-left-color: $body-bg;\r\n}\r\n.toast{\r\n    background-color: #151824;\r\n    .toast-header{\r\n        background-color: #222738;\r\n    }\r\n}", ".card {\r\n\t.card-body {\r\n\t\t.iq-icon-box-2 {\r\n            background: $body-bg;\r\n\t\t}\r\n\t}\r\n}", ".pricing {\r\n\t.table {\r\n\t\tth {\r\n\t\t\tborder-color: $border-color;\r\n\t\t\t&.bg-light {\r\n\t\t\t\tbackground: $body-bg !important;\r\n\t\t\t}\r\n\t\t}\r\n\t\tthead {\r\n\t\t\ttr {\r\n\t\t\t\tborder-color: $border-color;\r\n\t\t\t}\r\n\t\t}\r\n\t\ttr {\r\n\t\t\tborder-color: $border-color;\r\n\t\t}\r\n\t}\r\n}\r\n.prc-box {\r\n\tbackground-color: $blue-700;\r\n}\r\n.type {\r\n\t&::before {\r\n\t\tborder-left-color: $blue-800;\r\n\t}\r\n\t&::after {\r\n\t\tborder-right-color: $blue-800;\r\n\t}\r\n}\r\n.iq-single-card {\r\n\t&.bg-light {\r\n\t\tbackground: $dark !important;\r\n\t}\r\n}\r\n.line-around {\r\n\tborder-color: $border-color;\r\n\t.line-around-1 {\r\n\t\tbackground: $body-bg;\r\n\t}\r\n}", ".login-content {\r\n\t.bg-white {\r\n\t\tbackground: $card-bg !important;\r\n\t}\r\n}", ".apexcharts-svg{\r\n    .apexcharts-yaxis-texts-g{\r\n        text{\r\n            fill:$secondary;\r\n        }\r\n    }\r\n    .apexcharts-xaxis-texts-g{\r\n        text{\r\n            fill:$secondary;\r\n        }\r\n    }\r\n    line{\r\n        stroke:$secondary;\r\n    }\r\n    .apexcharts-track{\r\n        path {\r\n            stroke: $secondary;\r\n        }\r\n    }\r\n}", ".fc-unthemed .fc-toolbar .fc-button{\r\n    text-transform: capitalize;\r\n}\r\n.fc-unthemed th, .fc-unthemed td, .fc-unthemed thead, .fc-unthemed tbody, .fc-unthemed .fc-divider, .fc-unthemed .fc-row, .fc-unthemed .fc-content, .fc-unthemed .fc-popover, .fc-unthemed .fc-list-view, .fc-unthemed .fc-list-heading td{\r\n    border-color: $border-color !important;\r\n}\r\n.fc-other-month.fc-past, .fc-other-month.fc-future {\r\n    background: shade-color($border-color, 60%);\r\n}\r\n.fc-unthemed td.fc-today{\r\n    background: rgba($primary, .1) !important;\r\n}", "&.boxed {\r\n    background: $primary !important;\r\n    .boxed-inner {\r\n        background: $body-bg;\r\n    }\r\n}\r\n&.boxed-fancy {\r\n    background: $body-bg;\r\n    .boxed-inner{\r\n        background: $body-bg; \r\n    }\r\n}"]}