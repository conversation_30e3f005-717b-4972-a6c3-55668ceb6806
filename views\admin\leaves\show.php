<?php
$pageTitle = 'Leave Request Details';
include __DIR__ . '/../../layouts/header.php';
?>

<div class="conatiner-fluid content-inner mt-n5 py-0">
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">Leave Request Details</h4>
                    </div>
                    <div class="header-action">
                        <a href="<?= BASE_URL ?>/admin/leaves/index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                        
                        <?php if ($leaveRequest['status'] === 'pending' && hasPermission('leave.approve')): ?>
                        <button type="button" class="btn btn-success" onclick="approveLeave(<?= $leaveRequest['id'] ?>)">
                            <i class="fas fa-check me-2"></i>Approve
                        </button>
                        <button type="button" class="btn btn-danger" onclick="rejectLeave(<?= $leaveRequest['id'] ?>)">
                            <i class="fas fa-times me-2"></i>Reject
                        </button>
                        <?php endif; ?>
                        
                        <?php if (in_array($leaveRequest['status'], ['pending', 'approved']) && hasPermission('leave.cancel')): ?>
                        <button type="button" class="btn btn-warning" onclick="cancelLeave(<?= $leaveRequest['id'] ?>)">
                            <i class="fas fa-ban me-2"></i>Cancel
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Employee Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td><?= htmlspecialchars($leaveRequest['first_name'] . ' ' . $leaveRequest['last_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Employee Code:</strong></td>
                                            <td><?= htmlspecialchars($leaveRequest['employee_code']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Department:</strong></td>
                                            <td><?= htmlspecialchars($leaveRequest['department'] ?? '-') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Position:</strong></td>
                                            <td><?= htmlspecialchars($leaveRequest['position'] ?? '-') ?></td>
                                        </tr>
                                    </table>
                                </div>
                                
                                <div class="col-md-6">
                                    <h5>Leave Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Leave Type:</strong></td>
                                            <td><?= htmlspecialchars($leaveRequest['leave_type_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Start Date:</strong></td>
                                            <td><?= formatDate($leaveRequest['start_date']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>End Date:</strong></td>
                                            <td><?= formatDate($leaveRequest['end_date']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Days:</strong></td>
                                            <td><span class="badge bg-info fs-6"><?= $leaveRequest['total_days'] ?> days</span></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h5>Reason</h5>
                                    <div class="p-3 bg-light rounded">
                                        <?php if ($leaveRequest['reason']): ?>
                                            <?= nl2br(htmlspecialchars($leaveRequest['reason'])) ?>
                                        <?php else: ?>
                                            <em class="text-muted">No reason provided</em>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($leaveRequest['status'] === 'rejected' && $leaveRequest['rejection_reason']): ?>
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h5>Rejection Reason</h5>
                                    <div class="p-3 bg-danger-subtle rounded">
                                        <?= nl2br(htmlspecialchars($leaveRequest['rejection_reason'])) ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5>Request Status</h5>
                                    <span class="badge bg-<?= $leaveRequest['status'] === 'approved' ? 'success' : ($leaveRequest['status'] === 'rejected' ? 'danger' : ($leaveRequest['status'] === 'cancelled' ? 'secondary' : 'warning')) ?> fs-4 mb-3">
                                        <?= ucfirst($leaveRequest['status']) ?>
                                    </span>
                                    
                                    <div class="mt-4">
                                        <h6>Request Timeline</h6>
                                        <div class="timeline">
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-primary"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Request Submitted</h6>
                                                    <p class="timeline-text"><?= formatDateTime($leaveRequest['created_at']) ?></p>
                                                </div>
                                            </div>
                                            
                                            <?php if ($leaveRequest['approved_at']): ?>
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-<?= $leaveRequest['status'] === 'approved' ? 'success' : 'danger' ?>"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">
                                                        <?= $leaveRequest['status'] === 'approved' ? 'Approved' : 'Rejected' ?>
                                                    </h6>
                                                    <p class="timeline-text"><?= formatDateTime($leaveRequest['approved_at']) ?></p>
                                                    <?php if ($leaveRequest['approved_by_username']): ?>
                                                    <small class="text-muted">by <?= htmlspecialchars($leaveRequest['approved_by_username']) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #fff;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-title {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
}
</style>

<script>
    // Approve leave
    function approveLeave(id) {
        Swal.fire({
            title: "Approve Leave Request",
            input: "textarea",
            inputLabel: "Comments (optional)",
            inputPlaceholder: "Enter approval comments...",
            showCancelButton: true,
            confirmButtonText: "Approve",
            confirmButtonColor: "#28a745",
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading("Approving leave request...");
                
                fetch("../api/leaves/approve.php", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": CSRF_TOKEN
                    },
                    body: JSON.stringify({ 
                        id: id, 
                        comments: result.value,
                        csrf_token: CSRF_TOKEN 
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showAlert("success", data.message);
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showAlert("error", data.message);
                    }
                })
                .catch(error => {
                    hideLoading();
                    showAlert("error", "Failed to approve leave request");
                });
            }
        });
    }

    // Reject leave
    function rejectLeave(id) {
        Swal.fire({
            title: "Reject Leave Request",
            input: "textarea",
            inputLabel: "Rejection Reason (required)",
            inputPlaceholder: "Enter reason for rejection...",
            inputValidator: (value) => {
                if (!value) {
                    return "Rejection reason is required";
                }
            },
            showCancelButton: true,
            confirmButtonText: "Reject",
            confirmButtonColor: "#dc3545",
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading("Rejecting leave request...");
                
                fetch("../api/leaves/reject.php", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": CSRF_TOKEN
                    },
                    body: JSON.stringify({ 
                        id: id, 
                        reason: result.value,
                        csrf_token: CSRF_TOKEN 
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showAlert("success", data.message);
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showAlert("error", data.message);
                    }
                })
                .catch(error => {
                    hideLoading();
                    showAlert("error", "Failed to reject leave request");
                });
            }
        });
    }

    // Cancel leave
    function cancelLeave(id) {
        showConfirm("Are you sure you want to cancel this leave request?", function() {
            showLoading("Cancelling leave request...");
            
            fetch("../api/leaves/cancel.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": CSRF_TOKEN
                },
                body: JSON.stringify({ 
                    id: id,
                    csrf_token: CSRF_TOKEN 
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert("success", data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert("error", data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert("error", "Failed to cancel leave request");
            });
        });
    }
</script>

<?php include __DIR__ . '/../../layouts/footer.php'; ?>
