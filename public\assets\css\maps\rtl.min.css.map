{"version": 3, "sources": ["rtl.scss", "hope-ui-design-system/helper/_reboot.scss", "rtl/utilities/_utilities-reset.scss", "bootstrap/mixins/_breakpoints.scss", "bootstrap/mixins/_utilities.scss", "rtl/reboot/_reboot.scss", "rtl/components/menu-style/_default-main-content.scss", "rtl/components/menu-style/_default-sidebar.scss", "rtl/components/menu-style/_nav-rounded.scss", "rtl/components/menu-style/_navbar.scss", "hope-ui-design-system/_variable.scss", "rtl/components/menu-style/_sidebar-mini.scss", "rtl/components/accordion/_accordion.scss", "rtl/components/button/_fixed-btn.scss", "rtl/components/progressbar/_progressbar.scss", "rtl/components/profile/_profile.scss", "rtl/components/widgets/_progress-widget.scss", "rtl/components/widgets/_profile-widget.scss", "rtl/components/widgets/_credit-card.scss", "rtl/components/button-group/_button-group.scss", "rtl/components/forms/_form-check.scss", "rtl/components/dropdown/_bs-dropdown.scss", "rtl/components/timeline/_simple-timeline.scss", "rtl/pages/auth/_authentication.scss", "rtl/pages/kanban/_kanban.scss", "rtl/pages/pricing/_pricing.scss"], "names": [], "mappings": "AAAA;;;;;;;EAAA,OCCC,YAAA,CAED,GACI,cACA,cACA,8BACA,SACA,WAAA,CCFQ,gBACI,yBAAA,CAEJ,gBACI,wBAAA,CAEJ,gBACI,0BAAA,CAEJ,gBACI,yBAAA,CAVJ,gBACI,yBAAA,CAEJ,gBACI,wBAAA,CAEJ,gBACI,0BAAA,CAEJ,gBACI,yBAAA,CAVJ,gBACI,yBAAA,CAEJ,gBACI,wBAAA,CAEJ,gBACI,0BAAA,CAEJ,gBACI,yBAAA,CAVJ,gBACI,yBAAA,CAEJ,gBACI,wBAAA,CAEJ,gBACI,0BAAA,CAEJ,gBACI,yBAAA,CAVJ,gBACI,yBAAA,CAEJ,gBACI,wBAAA,CAEJ,gBACI,0BAAA,CAEJ,gBACI,yBAAA,CAVJ,gBACI,yBAAA,CAEJ,gBACI,wBAAA,CAEJ,gBACI,0BAAA,CAEJ,gBACI,yBAAA,CC+CZ,yBDzDQ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAAA,CC+CZ,yBDzDQ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAAA,CC+CZ,yBDzDQ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAAA,CC+CZ,0BDzDQ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAVJ,mBACI,yBAAA,CAEJ,mBACI,wBAAA,CAEJ,mBACI,0BAAA,CAEJ,mBACI,yBAAA,CAAA,CC+CZ,0BDzDQ,oBACI,yBAAA,CAEJ,oBACI,wBAAA,CAEJ,oBACI,0BAAA,CAEJ,oBACI,yBAAA,CAVJ,oBACI,yBAAA,CAEJ,oBACI,wBAAA,CAEJ,oBACI,0BAAA,CAEJ,oBACI,yBAAA,CAVJ,oBACI,yBAAA,CAEJ,oBACI,wBAAA,CAEJ,oBACI,0BAAA,CAEJ,oBACI,yBAAA,CAVJ,oBACI,yBAAA,CAEJ,oBACI,wBAAA,CAEJ,oBACI,0BAAA,CAEJ,oBACI,yBAAA,CAVJ,oBACI,yBAAA,CAEJ,oBACI,wBAAA,CAEJ,oBACI,0BAAA,CAEJ,oBACI,yBAAA,CAVJ,oBACI,yBAAA,CAEJ,oBACI,wBAAA,CAEJ,oBACI,0BAAA,CAEJ,oBACI,yBAAA,CAAA,CEkDR,uBAOI,sBAAA,CAPJ,qBAOI,qBAAA,CAPJ,sBAOI,qBAAA,CAPJ,gBAOI,wBAAA,CAPJ,gBAOI,6BAAA,CAPJ,gBAOI,4BAAA,CAPJ,gBAOI,2BAAA,CAPJ,gBAOI,6BAAA,CAPJ,gBAOI,2BAAA,CAPJ,mBAOI,2BAAA,CAPJ,gBAOI,yBAAA,CAPJ,gBAOI,8BAAA,CAPJ,gBAOI,6BAAA,CAPJ,gBAOI,4BAAA,CAPJ,gBAOI,8BAAA,CAPJ,gBAOI,4BAAA,CAPJ,mBAOI,4BAAA,CAPJ,iBAOI,+BAAA,CAPJ,iBAOI,8BAAA,CAPJ,iBAOI,4BAAA,CAPJ,iBAOI,8BAAA,CAPJ,iBAOI,4BAAA,CAPJ,iBAOI,gCAAA,CAPJ,iBAOI,+BAAA,CAPJ,iBAOI,6BAAA,CAPJ,iBAOI,+BAAA,CAPJ,iBAOI,6BAAA,CAPJ,gBAOI,yBAAA,CAPJ,gBAOI,8BAAA,CAPJ,gBAOI,6BAAA,CAPJ,gBAOI,4BAAA,CAPJ,gBAOI,8BAAA,CAPJ,gBAOI,4BAAA,CAPJ,gBAOI,0BAAA,CAPJ,gBAOI,+BAAA,CAPJ,gBAOI,8BAAA,CAPJ,gBAOI,6BAAA,CAPJ,gBAOI,+BAAA,CAPJ,gBAOI,6BAAA,CAPJ,sBAOI,2BAAA,CAPJ,oBAOI,0BAAA,CAPJ,uBAOI,4BAAA,CDVR,yBCGI,0BAOI,sBAAA,CAPJ,wBAOI,qBAAA,CAPJ,yBAOI,qBAAA,CAPJ,mBAOI,wBAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,2BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,2BAAA,CAPJ,sBAOI,2BAAA,CAPJ,mBAOI,yBAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,4BAAA,CAPJ,sBAOI,4BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,gCAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,mBAOI,yBAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,0BAAA,CAPJ,mBAOI,+BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,+BAAA,CAPJ,mBAOI,6BAAA,CAPJ,yBAOI,2BAAA,CAPJ,uBAOI,0BAAA,CAPJ,0BAOI,4BAAA,CAAA,CDVR,yBCGI,0BAOI,sBAAA,CAPJ,wBAOI,qBAAA,CAPJ,yBAOI,qBAAA,CAPJ,mBAOI,wBAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,2BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,2BAAA,CAPJ,sBAOI,2BAAA,CAPJ,mBAOI,yBAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,4BAAA,CAPJ,sBAOI,4BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,gCAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,mBAOI,yBAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,0BAAA,CAPJ,mBAOI,+BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,+BAAA,CAPJ,mBAOI,6BAAA,CAPJ,yBAOI,2BAAA,CAPJ,uBAOI,0BAAA,CAPJ,0BAOI,4BAAA,CAAA,CDVR,yBCGI,0BAOI,sBAAA,CAPJ,wBAOI,qBAAA,CAPJ,yBAOI,qBAAA,CAPJ,mBAOI,wBAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,2BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,2BAAA,CAPJ,sBAOI,2BAAA,CAPJ,mBAOI,yBAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,4BAAA,CAPJ,sBAOI,4BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,gCAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,mBAOI,yBAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,0BAAA,CAPJ,mBAOI,+BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,+BAAA,CAPJ,mBAOI,6BAAA,CAPJ,yBAOI,2BAAA,CAPJ,uBAOI,0BAAA,CAPJ,0BAOI,4BAAA,CAAA,CDVR,0BCGI,0BAOI,sBAAA,CAPJ,wBAOI,qBAAA,CAPJ,yBAOI,qBAAA,CAPJ,mBAOI,wBAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,2BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,2BAAA,CAPJ,sBAOI,2BAAA,CAPJ,mBAOI,yBAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,4BAAA,CAPJ,sBAOI,4BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,gCAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,mBAOI,yBAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,4BAAA,CAPJ,mBAOI,0BAAA,CAPJ,mBAOI,+BAAA,CAPJ,mBAOI,8BAAA,CAPJ,mBAOI,6BAAA,CAPJ,mBAOI,+BAAA,CAPJ,mBAOI,6BAAA,CAPJ,yBAOI,2BAAA,CAPJ,uBAOI,0BAAA,CAPJ,0BAOI,4BAAA,CAAA,CDVR,0BCGI,2BAOI,sBAAA,CAPJ,yBAOI,qBAAA,CAPJ,0BAOI,qBAAA,CAPJ,oBAOI,wBAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,2BAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,2BAAA,CAPJ,uBAOI,2BAAA,CAPJ,oBAOI,yBAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,uBAOI,4BAAA,CAPJ,qBAOI,+BAAA,CAPJ,qBAOI,8BAAA,CAPJ,qBAOI,4BAAA,CAPJ,qBAOI,8BAAA,CAPJ,qBAOI,4BAAA,CAPJ,qBAOI,gCAAA,CAPJ,qBAOI,+BAAA,CAPJ,qBAOI,6BAAA,CAPJ,qBAOI,+BAAA,CAPJ,qBAOI,6BAAA,CAPJ,oBAOI,yBAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,4BAAA,CAPJ,oBAOI,0BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,8BAAA,CAPJ,oBAOI,6BAAA,CAPJ,oBAOI,+BAAA,CAPJ,oBAOI,6BAAA,CAPJ,0BAOI,2BAAA,CAPJ,wBAOI,0BAAA,CAPJ,2BAOI,4BAAA,CAAA,CCzEZ,0BACI,eAAA,CAAA,iCCCI,kCACA,gBAAA,CHyEJ,6BGrEA,0NAII,eACA,iBAAA,CAAA,CCPY,kFACI,iCACA,6BAAA,wBAAA,CAGA,yFACI,gCACA,4BAAA,uBAAA,CAIJ,sGACI,gCACA,4BAAA,uBAAA,CASR,0FACI,kBACA,iBAAA,CASR,mFACI,mBACA,kBAAA,CASA,6LACI,mBAAA,CAOhB,2DACI,OACA,WAAA,CAKV,mCACI,WACA,YACA,iCAAA,6BAAA,wBAAA,CAEJ,iCACI,iBACA,kBAAA,CACF,6CACQ,kBACA,iBAAA,CAGV,iCACE,sBACA,kBAAA,CC9EA,mCACI,kBACA,mBAAA,CASQ,kEACI,qCAAA,AACA,6BAAA,wCAAA,AACA,gCAAA,sCAAA,AACA,8BAAA,yCAAA,gCAAA,CAUJ,+DACI,gDAAA,AACA,wCAAA,mDAAA,AACA,2CAAA,iDAAA,AACA,yCAAA,oDAAA,2CAAA,CC9BpB,sBACI,gBACA,kBAAA,CAIA,wCACI,kBACA,4BAAA,CAIJ,6BACI,cACA,gBAAA,CAGI,2DACI,uDAAA,8CAAA,CAEb,uDACa,uDAAA,8CAAA,CAIZ,6BACI,kBACA,iBAAA,CACN,yCACU,kBACA,iBAAA,CAGR,+BACI,WACA,UACA,SACA,iBACA,iCAAA,6BAAA,wBAAA,CAQI,qFACI,oBACA,iBCk6BoB,CDz5BxB,sFACI,iBAAA,CASA,oGACI,eACA,oBAAA,CElER,8CACI,6CAAA,AACA,wCADA,AACA,qCAAA,+DAAA,AACA,0DADA,AACA,uDAAA,2EAAA,AACA,sEADA,AACA,mEAAA,wBACA,kCACA,iBAAA,CAMI,sFACI,kCACA,iBAAA,CAOR,yEACI,kBAAA,CASQ,iHACI,oBACA,iBAAA,CAYJ,yIACI,mCAAA,CAUZ,kHACI,4CAAA,AACA,wCADA,AACA,oCAAA,SAAA,CAMZ,0DACI,4CAAA,AACA,wCADA,AACA,oCAAA,SAAA,CAQJ,0DACI,gBAAA,CRFZ,6BQUQ,6CACI,mCAAA,+BAAA,0BAAA,CAEI,4EACI,kCAAA,8BAAA,yBAAA,CAOhB,2DACI,eACA,iBAAA,CAAA,CRvBR,6BQ8BI,mCACI,SAAA,CAAA,CAMR,sCACI,kBACA,mBAAA,CAGA,mDACI,kBAAA,CAIJ,mGACI,mBAAA,CAKZ,6KAEI,kBAAA,CAKI,2CACI,iCAAA,AACA,6BADA,AACA,yBAAA,SAAA,CAOgB,iGACI,iCAAA,AACA,6BADA,AACA,yBAAA,SAAA,CAaxB,sDACI,SAAA,CAII,mGACI,SAAA,CAGQ,gIACI,mBACA,iBAAA,CC1K5B,mCACI,kBACA,iBAAA,CCHR,yBACI,OACA,WACA,iCAAA,AACA,yBAAA,oCAAA,2BAAA,CAEJ,2BACI,QACA,UACA,kCAAA,AACA,0BAAA,qCAAA,4BAAA,CAEJ,yBACI,MACA,iCAAA,AACA,yBAAA,kCAAA,yBAAA,CAEJ,4BACI,SACA,oCAAA,AACA,4BAAA,qCAAA,4BAAA,CAEJ,wBACI,OACA,WAAA,CCvBH,uCACC,WACG,UAAA,CCHL,wBACI,WACA,SAAA,CAGH,gCACO,UACA,SAAA,CCNJ,4CACI,oBACA,iBAAA,CCHR,8BACI,WACA,aAAA,CCCI,mDACI,UACA,eAAA,CAEJ,kDACI,eACA,UAAA,CAKT,yFACa,kBACA,kBAAA,CChBhB,qBACI,aAAA,CAGA,mCACI,yCAAA,CAIR,yLAEQ,kCAAA,yBAAA,CAGR,oJAEQ,kBACA,kBACA,kCAAA,yBAAA,CAGR,gLAEQ,kCAAA,yBAAA,CCvBR,sBACI,mBACA,mBAAA,CAEA,wCACI,YACA,kBACA,mBAAA,CAGR,wBACI,cACA,gBAAA,CAEJ,uBACI,mBAAA,CACA,yCACI,mBAAA,CAKA,8FACI,WAAA,CAKR,qDACI,WAAA,CAGR,6BACI,iBACA,kBAAA,CAEJ,qDACI,oBACA,kBAAA,CAGA,sEACI,kBACA,gBAAA,CAIJ,qCACI,SACA,WAAA,CCjDR,yBACI,gBAAA,CAGA,kCACI,kBACA,mBAAA,CAGR,6CACC,WACA,MAAA,CCVG,gCACI,UACA,UAAA,CAII,6CACI,YACA,iBACA,kBAAA,CACA,4DACI,WACA,SAAA,CAEJ,0EACI,WACA,SAAA,CAGR,8CACI,YACA,iBACA,kBAAA,CACA,6DACI,WACA,SAAA,CAEJ,2EACI,WACA,SAAA,CAGR,8CACI,UACA,OAAA,CCnChB,mBACC,QACA,MAAA,CAEA,iCACC,OACA,UAAA,CCFE,qEACgB,oBAAA,CCLpB,0BACC,sCAAA,6BAAA,CAAA", "file": "../rtl.min.css", "sourcesContent": ["/*!\r\n* Version: 1.2.0\r\n* Template: Example Project\r\n* Author: iqonic.design\r\n* Design and Developed by: iqonic.design\r\n* NOTE: This file contains the styling for Template.\r\n*\r\n*/\r\n\r\n// Configuration\r\n@import \"./bootstrap/functions\";\r\n// Variables\r\n@import \"./hope-ui-design-system/variable\";\r\n@import \"./hope-ui-design-system/variables/index\";\r\n@import \"./bootstrap/variables\";\r\n\r\n@import \"./bootstrap/mixins\";\r\n\r\n// Hope Ui Design System Mixin And Helper\r\n@import \"./hope-ui-design-system/helper/functions\";\r\n@import \"./hope-ui-design-system/helper/mixins\";\r\n@import \"./hope-ui-design-system/helper/reboot\";\r\n\r\n@import \"./rtl/index\";\r\n", ":focus{\r\n\toutline: none;\r\n}\r\nhr {\r\n    margin: 1rem 0;\r\n    color: inherit;\r\n    background-color: currentColor;\r\n    border: 0;\r\n    opacity: .25;\r\n}", "@each $breakpoint in map-keys($grid-breakpoints) {\r\n\r\n    // Generate media query if needed\r\n    @include media-breakpoint-up($breakpoint) {\r\n        $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n        @each $name, $value  in $spacers {\r\n            .me#{$infix}-#{$name} {\r\n                margin-right: 0 !important;\r\n            }\r\n            .ms#{$infix}-#{$name} {\r\n                margin-left: 0 !important;\r\n            }\r\n            .pe#{$infix}-#{$name} {\r\n                padding-right: 0 !important;\r\n            }\r\n            .ps#{$infix}-#{$name} {\r\n                padding-left: 0 !important;\r\n            }\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\r\n//\r\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\r\n//\r\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\r\n//\r\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\r\n\r\n// Name of the next breakpoint, or null for the last breakpoint.\r\n//\r\n//    >> breakpoint-next(sm)\r\n//    md\r\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    md\r\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\r\n//    md\r\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\r\n  $n: index($breakpoint-names, $name);\r\n  @if not $n {\r\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\r\n  }\r\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\r\n}\r\n\r\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\r\n//\r\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    576px\r\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\r\n  $min: map-get($breakpoints, $name);\r\n  @return if($min != 0, $min, null);\r\n}\r\n\r\n// Maximum breakpoint width.\r\n// The maximum value is reduced by 0.02px to work around the limitations of\r\n// `min-` and `max-` prefixes and viewports with fractional widths.\r\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\r\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\r\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\r\n//\r\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    767.98px\r\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\r\n  $max: map-get($breakpoints, $name);\r\n  @return if($max and $max > 0, $max - .02, null);\r\n}\r\n\r\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\r\n// Useful for making responsive utilities.\r\n//\r\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    \"\"  (Returns a blank string)\r\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\r\n//    \"-sm\"\r\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\r\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\r\n}\r\n\r\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\r\n// Makes the @content apply to the given breakpoint and wider.\r\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($name, $breakpoints);\r\n  @if $min {\r\n    @media (min-width: $min) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\r\n// Makes the @content apply to the given breakpoint and narrower.\r\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\r\n  $max: breakpoint-max($name, $breakpoints);\r\n  @if $max {\r\n    @media (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else {\r\n    @content;\r\n  }\r\n}\r\n\r\n// Media that spans multiple breakpoint widths.\r\n// Makes the @content apply between the min and max breakpoints\r\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\r\n  $min: breakpoint-min($lower, $breakpoints);\r\n  $max: breakpoint-max($upper, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($lower, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($upper, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n// Media between the breakpoint's minimum and maximum widths.\r\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\r\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\r\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\r\n  $min:  breakpoint-min($name, $breakpoints);\r\n  $next: breakpoint-next($name, $breakpoints);\r\n  $max:  breakpoint-max($next, $breakpoints);\r\n\r\n  @if $min != null and $max != null {\r\n    @media (min-width: $min) and (max-width: $max) {\r\n      @content;\r\n    }\r\n  } @else if $max == null {\r\n    @include media-breakpoint-up($name, $breakpoints) {\r\n      @content;\r\n    }\r\n  } @else if $min == null {\r\n    @include media-breakpoint-down($next, $breakpoints) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n", "// Utility generator\r\n// Used to generate utilities & print utilities\r\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\r\n  $values: map-get($utility, values);\r\n\r\n  // If the values are a list or string, convert it into a map\r\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\r\n    $values: zip($values, $values);\r\n  }\r\n\r\n  @each $key, $value in $values {\r\n    $properties: map-get($utility, property);\r\n\r\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\r\n    @if type-of($properties) == \"string\" {\r\n      $properties: append((), $properties);\r\n    }\r\n\r\n    // Use custom class if present\r\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\r\n    $property-class: if($property-class == null, \"\", $property-class);\r\n\r\n    // Use custom CSS variable name if present, otherwise default to `class`\r\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\r\n\r\n    // State params to generate pseudo-classes\r\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\r\n\r\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\r\n\r\n    // Don't prefix if value key is null (e.g. with shadow class)\r\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\r\n\r\n    @if map-get($utility, rfs) {\r\n      // Inside the media query\r\n      @if $is-rfs-media-query {\r\n        $val: rfs-value($value);\r\n\r\n        // Do not render anything if fluid and non fluid values are the same\r\n        $value: if($val == rfs-fluid-value($value), null, $val);\r\n      }\r\n      @else {\r\n        $value: rfs-fluid-value($value);\r\n      }\r\n    }\r\n\r\n    $is-css-var: map-get($utility, css-var);\r\n    $is-local-vars: map-get($utility, local-vars);\r\n    $is-rtl: map-get($utility, rtl);\r\n\r\n    @if $value != null {\r\n      @if $is-rtl == false {\r\n        /* rtl:begin:remove */\r\n      }\r\n\r\n      @if $is-css-var {\r\n        .#{$property-class + $infix + $property-class-modifier} {\r\n          --#{$prefix}#{$css-variable-name}: #{$value};\r\n        }\r\n\r\n        @each $pseudo in $state {\r\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\r\n            --#{$prefix}#{$css-variable-name}: #{$value};\r\n          }\r\n        }\r\n      } @else {\r\n        .#{$property-class + $infix + $property-class-modifier} {\r\n          @each $property in $properties {\r\n            @if $is-local-vars {\r\n              @each $local-var, $variable in $is-local-vars {\r\n                --#{$prefix}#{$local-var}: #{$variable};\r\n              }\r\n            }\r\n            #{$property}: $value if($enable-important-utilities, !important, null);\r\n          }\r\n        }\r\n\r\n        @each $pseudo in $state {\r\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\r\n            @each $property in $properties {\r\n              @if $is-local-vars {\r\n                @each $local-var, $variable in $is-local-vars {\r\n                  --#{$prefix}#{$local-var}: #{$variable};\r\n                }\r\n              }\r\n              #{$property}: $value if($enable-important-utilities, !important, null);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      @if $is-rtl == false {\r\n        /* rtl:end:remove */\r\n      }\r\n    }\r\n  }\r\n}\r\n", "ol, ul{\r\n    padding-right: 0;\r\n}", ".sidebar {\r\n    &+.main-content {\r\n        margin-right: var(--sidebar-width);\r\n        margin-left: auto;\r\n    }\r\n}\r\n@include media-breakpoint-down(xl) {\r\n    .sidebar-default.sidebar-mini.sidebar-boxed+.main-content,\r\n    .sidebar-default.sidebar-boxed+.main-content,\r\n    .sidebar-default.sidebar-mini+.main-content,\r\n    .sidebar-default+.main-content {\r\n        margin-right: 0;\r\n        margin-left: unset;\r\n    }\r\n}\r\n", ".sidebar-base {\r\n    .sidebar-list {\r\n        .navbar-nav{\r\n            .nav-item{\r\n                .nav-link{\r\n                    .right-icon {\r\n                        -webkit-transform: rotate(180deg);\r\n                        transform: rotate(180deg);\r\n                    }\r\n                    &.active {\r\n                        .right-icon {\r\n                            -webkit-transform: rotate(90deg);\r\n                            transform: rotate(90deg);\r\n                        }\r\n                    }\r\n                    &[aria-expanded=true] {\r\n                        .right-icon {\r\n                            -webkit-transform: rotate(90deg);\r\n                            transform: rotate(90deg);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .navbar-nav{\r\n            .nav-item{\r\n                .nav-link:not(.disabled){\r\n                    span {\r\n                        margin-right: $spacer;\r\n                        margin-left: unset;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &:not(.sidebar-mini){\r\n        .navbar-nav{\r\n            .nav-item{\r\n                &:not(.static-item){\r\n                    padding-left: unset;\r\n                    padding-right: 1rem;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.navs-pill, &.navs-rounded{\r\n        &:not(.sidebar-mini){\r\n            .navbar-nav{\r\n                .nav-item{\r\n                    &:not(.static-item){\r\n                        padding-right: unset;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .data-scrollbar{\r\n        .scrollbar-track-y{\r\n            left: 0;\r\n            right: unset;\r\n        }\r\n    }\r\n}\r\n.sidebar  {\r\n  .sidebar-toggle {\r\n      left: -12px;\r\n      right: unset;\r\n      transform: rotate(180deg);\r\n  }\r\n  .navbar-brand {\r\n      margin-left: 1rem;\r\n      margin-right: unset;\r\n    .logo-title {\r\n            margin-left: unset;\r\n            margin-right: 1rem;\r\n    }\r\n  }\r\n  &.sidebar-glass{\r\n    border-left: 1px solid;\r\n    border-right: unset;\r\n}\r\n}\r\n", ".navs-pill{\r\n    .sidebar-body{\r\n        padding-left: 1rem;\r\n        padding-right: unset;\r\n    }\r\n}\r\n\r\n.sidebar{\r\n    //Sidebar One-side Rounded\r\n    &.navs-rounded{\r\n        .sidebar-body{\r\n            .nav-item{\r\n                .nav-link{\r\n                    border-top-left-radius: $border-radius;\r\n                    border-bottom-left-radius: $border-radius;\r\n                    border-top-right-radius: unset;\r\n                    border-bottom-right-radius: unset;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    //Sidebar One-side Rounded Pill\r\n    &.navs-pill{\r\n        .sidebar-body{\r\n            .nav-item{\r\n                .nav-link{\r\n                    border-top-left-radius: $border-radius-pill !important;\r\n                    border-bottom-left-radius: $border-radius-pill !important;\r\n                    border-top-right-radius: unset !important;\r\n                    border-bottom-right-radius: unset !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n", ".navbar-nav{\r\n    padding-right: 0;\r\n    padding-left: unset;\r\n\r\n}\r\n.navbar-expand-lg{\r\n    .navbar-nav{\r\n        margin-right: auto;\r\n        margin-left: unset !important;\r\n    }\r\n}\r\n.nav  {\r\n    .search-input {\r\n        direction: ltr;\r\n        text-align: right;\r\n\r\n        &.input-group {\r\n            .input-group-text {\r\n                border-radius: 0px $input-border-radius $input-border-radius 0px !important;\r\n            }\r\n\t\t\t.form-control {\r\n                border-radius: $input-border-radius 0px 0px $input-border-radius !important;\r\n\t\t\t}\r\n\t\t}\r\n    }\r\n    .navbar-brand {\r\n        margin-right: 2rem;\r\n        margin-left: unset;\r\n\t\t.logo-title {\r\n            margin-left: unset;\r\n            margin-right: 1rem;\r\n\t\t}\r\n\t}\r\n    .sidebar-toggle{\r\n        right: 20px;\r\n        left: auto;\r\n        top: auto;\r\n        line-height: 15px;\r\n        transform: rotate(180deg);\r\n    }\r\n}\r\n\r\n.tab-bottom-bordered{\r\n    &.iq-custom-tab-border{\r\n        .nav-tabs{\r\n          .nav-link{\r\n            &:nth-child(1){\r\n                padding-right: unset;\r\n                padding-left: $nav-link-padding-x;\r\n            }\r\n          }\r\n        }\r\n    }\r\n}\r\n.sidebar-profile-card {\r\n    .sidebar-profile-action {\r\n        .btn-action{\r\n            &:not(:first-child){\r\n                margin-right: 1rem;\r\n            }\r\n        }\r\n    }\r\n}\r\n.sidebar-mini {\r\n    .sidebar-profile-card {\r\n        .sidebar-profile-action {\r\n            .btn-action {\r\n                &:not(:first-child) {\r\n                    margin-right: 0;\r\n                    margin-bottom: $spacer * .75;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "// Variables\r\n//\r\n// Variables should follow the `$component-state-property-size` formula for\r\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\r\n\r\n// Color system\r\n\r\n// scss-docs-start gray-color-variables\r\n$white:    #ffffff !default;\r\n$gray-100: #f8f9fa !default;\r\n$gray-200: #e9ecef !default;\r\n$gray-300: #dee2e6 !default;\r\n$gray-400: #ced4da !default;\r\n$gray-500: #adb5bd !default;\r\n$gray-600: #6c757d !default;\r\n$gray-700: #495057 !default;\r\n$gray-800: #343a40 !default;\r\n$gray-900: #212529 !default;\r\n$black:    #000 !default;\r\n// scss-docs-end gray-color-variables\r\n\r\n// fusv-disable\r\n// scss-docs-start gray-colors-map\r\n$grays: (\r\n  \"100\": $gray-100,\r\n  \"200\": $gray-200,\r\n  \"300\": $gray-300,\r\n  \"400\": $gray-400,\r\n  \"500\": $gray-500,\r\n  \"600\": $gray-600,\r\n  \"700\": $gray-700,\r\n  \"800\": $gray-800,\r\n  \"900\": $gray-900\r\n) !default;\r\n// scss-docs-end gray-colors-map\r\n// fusv-enable\r\n\r\n// scss-docs-start color-variables\r\n$blue:    #3a57e8  !default;\r\n$indigo:  #6610f2 !default;\r\n$purple:  #6f42c1 !default;\r\n$pink:    #d63384 !default;\r\n$red:     #c03221  !default;\r\n$orange:  #FAA938 !default;\r\n$yellow:  #f16a1b  !default;\r\n$green:   #1aa053  !default;\r\n$teal:    #001F4D !default;\r\n$cyan:    #079aa2  !default;\r\n// scss-docs-end color-variables\r\n\r\n// shadow color\r\n\r\n$shadow-color: #8898AA;\r\n\r\n// scss-docs-start colors-map\r\n$colors: (\r\n  \"blue\":       $blue,\r\n  \"indigo\":     $indigo,\r\n  \"purple\":     $purple,\r\n  \"pink\":       $pink,\r\n  \"red\":        $red,\r\n  \"orange\":     $orange,\r\n  \"yellow\":     $yellow,\r\n  \"green\":      $green,\r\n  \"teal\":       $teal,\r\n  \"cyan\":       $cyan,\r\n  \"white\":      $white,\r\n  \"gray\":       $gray-600,\r\n  \"gray-dark\":  $gray-800\r\n) !default;\r\n// scss-docs-end colors-map\r\n\r\n// scss-docs-start theme-color-variables\r\n$primary:       $blue !default;\r\n$secondary:     $teal !default;\r\n$success:       $green !default;\r\n$info:          $cyan !default;\r\n$warning:       $yellow !default;\r\n$danger:        $red !default;\r\n$light:         $gray-300 !default;\r\n$dark:          $gray-900 !default;\r\n$gray:          $gray-600 !default;\r\n$gray-dark:     $gray-800 !default;\r\n// scss-docs-end theme-color-variables\r\n\r\n// scss-docs-start theme-colors-map\r\n$theme-colors: (\r\n  \"primary\":    $primary,\r\n  \"secondary\":  $secondary,\r\n  \"success\":    $success,\r\n  \"info\":       $info,\r\n  \"warning\":    $warning,\r\n  \"danger\":     $danger,\r\n  \"light\":      $light,\r\n  \"dark\":       $dark,\r\n  \"gray\":       $gray,\r\n  \"gray-dark\":   $gray-dark,\r\n) !default;\r\n// scss-docs-end theme-colors-map\r\n\r\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\r\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\r\n$min-contrast-ratio:   3 !default;\r\n\r\n// Customize the light and dark text colors for use in our color contrast function.\r\n$color-contrast-dark:      $black !default;\r\n$color-contrast-light:     $white !default;\r\n\r\n// fusv-disable\r\n$blue-100: tint-color($blue, 80%) !default;\r\n$blue-200: tint-color($blue, 60%) !default;\r\n$blue-300: tint-color($blue, 40%) !default;\r\n$blue-400: tint-color($blue, 20%) !default;\r\n$blue-500: $blue !default;\r\n$blue-600: shade-color($blue, 20%) !default;\r\n$blue-700: shade-color($blue, 40%) !default;\r\n$blue-800: shade-color($blue, 60%) !default;\r\n$blue-900: shade-color($blue, 80%) !default;\r\n\r\n$indigo-100: tint-color($indigo, 80%) !default;\r\n$indigo-200: tint-color($indigo, 60%) !default;\r\n$indigo-300: tint-color($indigo, 40%) !default;\r\n$indigo-400: tint-color($indigo, 20%) !default;\r\n$indigo-500: $indigo !default;\r\n$indigo-600: shade-color($indigo, 20%) !default;\r\n$indigo-700: shade-color($indigo, 40%) !default;\r\n$indigo-800: shade-color($indigo, 60%) !default;\r\n$indigo-900: shade-color($indigo, 80%) !default;\r\n\r\n$purple-100: tint-color($purple, 80%) !default;\r\n$purple-200: tint-color($purple, 60%) !default;\r\n$purple-300: tint-color($purple, 40%) !default;\r\n$purple-400: tint-color($purple, 20%) !default;\r\n$purple-500: $purple !default;\r\n$purple-600: shade-color($purple, 20%) !default;\r\n$purple-700: shade-color($purple, 40%) !default;\r\n$purple-800: shade-color($purple, 60%) !default;\r\n$purple-900: shade-color($purple, 80%) !default;\r\n\r\n$pink-100: tint-color($pink, 80%) !default;\r\n$pink-200: tint-color($pink, 60%) !default;\r\n$pink-300: tint-color($pink, 40%) !default;\r\n$pink-400: tint-color($pink, 20%) !default;\r\n$pink-500: $pink !default;\r\n$pink-600: shade-color($pink, 20%) !default;\r\n$pink-700: shade-color($pink, 40%) !default;\r\n$pink-800: shade-color($pink, 60%) !default;\r\n$pink-900: shade-color($pink, 80%) !default;\r\n\r\n$red-100: tint-color($red, 80%) !default;\r\n$red-200: tint-color($red, 60%) !default;\r\n$red-300: tint-color($red, 40%) !default;\r\n$red-400: tint-color($red, 20%) !default;\r\n$red-500: $red !default;\r\n$red-600: shade-color($red, 20%) !default;\r\n$red-700: shade-color($red, 40%) !default;\r\n$red-800: shade-color($red, 60%) !default;\r\n$red-900: shade-color($red, 80%) !default;\r\n\r\n$orange-100: tint-color($orange, 80%) !default;\r\n$orange-200: tint-color($orange, 60%) !default;\r\n$orange-300: tint-color($orange, 40%) !default;\r\n$orange-400: tint-color($orange, 20%) !default;\r\n$orange-500: $orange !default;\r\n$orange-600: shade-color($orange, 20%) !default;\r\n$orange-700: shade-color($orange, 40%) !default;\r\n$orange-800: shade-color($orange, 60%) !default;\r\n$orange-900: shade-color($orange, 80%) !default;\r\n\r\n$yellow-100: tint-color($yellow, 80%) !default;\r\n$yellow-200: tint-color($yellow, 60%) !default;\r\n$yellow-300: tint-color($yellow, 40%) !default;\r\n$yellow-400: tint-color($yellow, 20%) !default;\r\n$yellow-500: $yellow !default;\r\n$yellow-600: shade-color($yellow, 20%) !default;\r\n$yellow-700: shade-color($yellow, 40%) !default;\r\n$yellow-800: shade-color($yellow, 60%) !default;\r\n$yellow-900: shade-color($yellow, 80%) !default;\r\n\r\n$green-100: tint-color($green, 80%) !default;\r\n$green-200: tint-color($green, 60%) !default;\r\n$green-300: tint-color($green, 40%) !default;\r\n$green-400: tint-color($green, 20%) !default;\r\n$green-500: $green !default;\r\n$green-600: shade-color($green, 20%) !default;\r\n$green-700: shade-color($green, 40%) !default;\r\n$green-800: shade-color($green, 60%) !default;\r\n$green-900: shade-color($green, 80%) !default;\r\n\r\n$teal-100: tint-color($teal, 80%) !default;\r\n$teal-200: tint-color($teal, 60%) !default;\r\n$teal-300: tint-color($teal, 40%) !default;\r\n$teal-400: tint-color($teal, 20%) !default;\r\n$teal-500: $teal !default;\r\n$teal-600: shade-color($teal, 20%) !default;\r\n$teal-700: shade-color($teal, 40%) !default;\r\n$teal-800: shade-color($teal, 60%) !default;\r\n$teal-900: shade-color($teal, 80%) !default;\r\n\r\n$cyan-100: tint-color($cyan, 80%) !default;\r\n$cyan-200: tint-color($cyan, 60%) !default;\r\n$cyan-300: tint-color($cyan, 40%) !default;\r\n$cyan-400: tint-color($cyan, 20%) !default;\r\n$cyan-500: $cyan !default;\r\n$cyan-600: shade-color($cyan, 20%) !default;\r\n$cyan-700: shade-color($cyan, 40%) !default;\r\n$cyan-800: shade-color($cyan, 60%) !default;\r\n$cyan-900: shade-color($cyan, 80%) !default;\r\n// fusv-enable\r\n\r\n// Characters which are escaped by the escape-svg function\r\n$escaped-characters: (\r\n  (\"<\", \"%3c\"),\r\n  (\">\", \"%3e\"),\r\n  (\"#\", \"%23\"),\r\n  (\"(\", \"%28\"),\r\n  (\")\", \"%29\"),\r\n) !default;\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n\r\n$enable-caret:                true !default;\r\n$enable-rounded:              true !default;\r\n$enable-shadows:              true;\r\n$enable-gradients:            false !default;\r\n$enable-transitions:          true !default;\r\n$enable-reduced-motion:       true !default;\r\n$enable-smooth-scroll:        true !default;\r\n$enable-grid-classes:         true !default;\r\n$enable-button-pointers:      true !default;\r\n$enable-rfs:                  true !default;\r\n$enable-validation-icons:     true !default;\r\n$enable-negative-margins:     true !default;\r\n$enable-deprecation-messages: true !default;\r\n$enable-important-utilities:  true !default;\r\n\r\n// Prefix for :root CSS variables\r\n\r\n$variable-prefix:             bs- !default;\r\n\r\n// Gradient\r\n//\r\n// The gradient which is added to components if `$enable-gradients` is `true`\r\n// This gradient is also added to elements with `.bg-gradient`\r\n// scss-docs-start variable-gradient\r\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\r\n// scss-docs-end variable-gradient\r\n\r\n// Spacing\r\n//\r\n// Control the default styling of most Bootstrap elements by modifying these\r\n// variables. Mostly focused on spacing.\r\n// You can add more entries to the $spacers map, should you need more variation.\r\n\r\n// scss-docs-start spacer-variables-maps\r\n$spacer: 1rem !default;\r\n$spacers: (\r\n  0: 0,\r\n  1: $spacer * .25,\r\n  2: $spacer * .5,\r\n  3: $spacer,\r\n  4: $spacer * 1.5,\r\n  5: $spacer * 3,\r\n) !default;\r\n\r\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\r\n// scss-docs-end spacer-variables-maps\r\n\r\n// Position\r\n//\r\n// Define the edge positioning anchors of the position utilities.\r\n\r\n// scss-docs-start position-map\r\n$position-values: (\r\n  0: 0,\r\n  50: 50%,\r\n  100: 100%\r\n) !default;\r\n// scss-docs-end position-map\r\n\r\n// Body\r\n//\r\n// Settings for the `<body>` element.\r\n\r\n$body-bg:                   #F5F6FA !default;\r\n$body-color:                #8A92A6 !default;\r\n$body-text-align:           null !default;\r\n\r\n\r\n// Links\r\n//\r\n// Style anchor elements.\r\n\r\n$link-color:                              $primary !default;\r\n$link-decoration:                         none !default;\r\n$link-shade-percentage:                   20% !default;\r\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\r\n$link-hover-decoration:                   null !default;\r\n\r\n$stretched-link-pseudo-element:           after !default;\r\n$stretched-link-z-index:                  1 !default;\r\n\r\n// Paragraphs\r\n//\r\n// Style p element.\r\n\r\n$paragraph-margin-bottom:   1rem !default;\r\n\r\n\r\n// Grid breakpoints\r\n//\r\n// Define the minimum dimensions at which your layout will change,\r\n// adapting to different screen sizes, for use in media queries.\r\n\r\n// scss-docs-start grid-breakpoints\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px,\r\n  xxl: 1400px\r\n) !default;\r\n// scss-docs-end grid-breakpoints\r\n\r\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\r\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\r\n\r\n\r\n// Grid containers\r\n//\r\n// Define the maximum width of `.container` for different screen sizes.\r\n\r\n// scss-docs-start container-max-widths\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1140px,\r\n  xxl: 1320px\r\n) !default;\r\n// scss-docs-end container-max-widths\r\n\r\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\r\n\r\n\r\n// Grid columns\r\n//\r\n// Set the number of columns and specify the width of the gutters.\r\n\r\n$grid-columns:                12 !default;\r\n$grid-gutter-width:           2rem !default;\r\n$grid-row-columns:            6 !default;\r\n\r\n$gutters: $spacers !default;\r\n\r\n// Container padding\r\n\r\n$container-padding-x: $grid-gutter-width * .5 !default;\r\n\r\n\r\n// Components\r\n//\r\n// Define common padding and border radius sizes and more.\r\n\r\n// scss-docs-start border-variables\r\n$border-width:                1px !default;\r\n$border-widths: (\r\n  1: 1px,\r\n  2: 2px,\r\n  3: 3px,\r\n  4: 4px,\r\n  5: 5px\r\n) !default;\r\n\r\n$border-color:                #eee !default;\r\n// scss-docs-end border-variables\r\n\r\n// scss-docs-start border-radius-variables\r\n$border-radius-xs:            0.125rem !default;\r\n$border-radius:               0.5rem !default;\r\n$border-radius-sm:            0.25rem !default;\r\n$border-radius-lg:            1rem !default;\r\n$border-radius-pill:          50rem !default;\r\n// scss-docs-end border-radius-variables\r\n\r\n// scss-docs-start box-shadow-variables\r\n$box-shadow:                  0 .5rem 1rem rgba(darken($primary, 25%), .05) !default;\r\n$box-shadow-sm:               0 .125rem .25rem rgba(darken($primary, 25%), .1) !default;\r\n$box-shadow-lg:               0 10px 30px 0 rgba(darken($primary, 25%), .05)!default;\r\n$box-shadow-inset:            inset 0 4px 8px rgba($black, .16) !default;\r\n\r\n//Color Shadow\r\n$box-color-shadow:            0 .125rem .25rem !default;\r\n$box-color-shadow-hover:      0 .125rem .5rem !default;\r\n$box-color-shadow-tint:         .30!default;\r\n$box-color-shadow-shade:        .35!default;\r\n\r\n// scss-docs-end box-shadow-variables\r\n\r\n$component-active-color:      $white !default;\r\n$component-active-bg:         $primary !default;\r\n$component-active-shadow:     0 .125rem .25rem rgba($primary,.10)!default;\r\n$component-hover-shadow:     0 .125rem .25rem rgba($primary,.15)!default;\r\n$component-success-shadow:     0 .125rem .25rem rgba($success,.10)!default;\r\n$component-error-shadow:     0 .125rem .25rem rgba($danger,.10)!default;\r\n\r\n// scss-docs-start caret-variables\r\n$caret-width:                 .3em !default;\r\n$caret-vertical-align:        $caret-width * .85 !default;\r\n$caret-spacing:               $caret-width * .85 !default;\r\n// scss-docs-end caret-variables\r\n\r\n$transition-base:             all .2s ease-in-out !default;\r\n$transition-fade:             opacity .15s linear !default;\r\n// scss-docs-start collapse-transition\r\n$transition-collapse:         height .35s ease !default;\r\n// scss-docs-end collapse-transition\r\n\r\n// stylelint-disable function-disallowed-list\r\n// scss-docs-start aspect-ratios\r\n$aspect-ratios: (\r\n  \"1x1\": 100%,\r\n  \"4x3\": calc(3 / 4 * 100%),\r\n  \"16x9\": calc(9 / 16 * 100%),\r\n  \"21x9\": calc(9 / 21 * 100%)\r\n) !default;\r\n// scss-docs-end aspect-ratios\r\n// stylelint-enable function-disallowed-list\r\n\r\n// Typography\r\n//\r\n// Font, line-height, and color for body text, headings, and more.\r\n\r\n// scss-docs-start font-variables\r\n// stylelint-disable value-keyword-case\r\n$font-family-sans-serif:      'Inter', sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\r\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\r\n// stylelint-enable value-keyword-case\r\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif) !default;\r\n$font-family-code:            var(--#{$variable-prefix}font-monospace) !default;\r\n\r\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\r\n// $font-size-base affects the font size of the body text\r\n$font-size-root:              null !default;\r\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\r\n$font-size-xs:                $font-size-base * .800 !default;\r\n$font-size-sm:                $font-size-base * .875 !default;\r\n$font-size-lg:                $font-size-base * 1.25 !default;\r\n\r\n$font-weight-lighter:         lighter !default;\r\n$font-weight-light:           300 !default;\r\n$font-weight-normal:          400 !default;\r\n$font-weight-bold:            700 !default;\r\n$font-weight-bolder:          bolder !default;\r\n\r\n$font-weight-base:            $font-weight-normal !default;\r\n\r\n$line-height-base:            1.5 !default;\r\n$line-height-sm:              1.25 !default;\r\n$line-height-lg:              2 !default;\r\n\r\n$h1-font-size:                $font-size-base * 2.5 !default;\r\n$h2-font-size:                $font-size-base * 2 !default;\r\n$h3-font-size:                $font-size-base * 1.75 !default;\r\n$h4-font-size:                $font-size-base * 1.5 !default;\r\n$h5-font-size:                $font-size-base * 1.25 !default;\r\n$h6-font-size:                $font-size-base !default;\r\n// scss-docs-end font-variables\r\n\r\n// scss-docs-start font-sizes\r\n$font-sizes: (\r\n  1: $h1-font-size,\r\n  2: $h2-font-size,\r\n  3: $h3-font-size,\r\n  4: $h4-font-size,\r\n  5: $h5-font-size,\r\n  6: $h6-font-size\r\n) !default;\r\n// scss-docs-end font-sizes\r\n\r\n// scss-docs-start headings-variables\r\n$headings-margin-bottom:      0 !default;\r\n$headings-font-family:        null !default;\r\n$headings-font-style:         null !default;\r\n$headings-font-weight:        500 !default;\r\n$headings-line-height:        1.2 !default;\r\n$headings-color:              #232D42 !default;\r\n// scss-docs-end headings-variables\r\n\r\n// scss-docs-start display-headings\r\n$display-font-sizes: (\r\n  1: 5rem,\r\n  2: 4.5rem,\r\n  3: 4rem,\r\n  4: 3.5rem,\r\n  5: 3rem,\r\n  6: 2.5rem\r\n) !default;\r\n\r\n$display-font-weight: 300 !default;\r\n$display-line-height: $headings-line-height !default;\r\n// scss-docs-end display-headings\r\n\r\n// scss-docs-start type-variables\r\n$lead-font-size:              $font-size-base * 1.25 !default;\r\n$lead-font-weight:            300 !default;\r\n\r\n$small-font-size:             .875em !default;\r\n\r\n$sub-sup-font-size:           .75em !default;\r\n\r\n$text-muted:                  $gray-600 !default;\r\n\r\n$initialism-font-size:        $small-font-size !default;\r\n\r\n$blockquote-margin-y:         $spacer !default;\r\n$blockquote-font-size:        $font-size-base * 1.25 !default;\r\n$blockquote-footer-color:     $gray-600 !default;\r\n$blockquote-footer-font-size: $small-font-size !default;\r\n\r\n$hr-margin-y:                 $spacer !default;\r\n$hr-color:                    inherit !default;\r\n$hr-height:                   $border-width !default;\r\n$hr-opacity:                  .25 !default;\r\n\r\n$legend-margin-bottom:        .5rem !default;\r\n$legend-font-size:            1.5rem !default;\r\n$legend-font-weight:          null !default;\r\n\r\n$mark-padding:                .2em !default;\r\n\r\n$dt-font-weight:              $font-weight-bold !default;\r\n\r\n$nested-kbd-font-weight:      $font-weight-bold !default;\r\n\r\n$list-inline-padding:         .5rem !default;\r\n\r\n$mark-bg:                     #fcf8e3 !default;\r\n// scss-docs-end type-variables\r\n\r\n\r\n// Tables\r\n//\r\n// Customizes the `.table` component with basic values, each used across all table variations.\r\n\r\n// scss-docs-start table-variables\r\n$table-th-padding-y:        .75rem !default;\r\n$table-th-padding-x:        1.5rem !default;\r\n$table-cell-padding-y:        1rem !default;\r\n$table-cell-padding-x:        1.5rem !default;\r\n$table-cell-padding-y-sm:     .25rem !default;\r\n$table-cell-padding-x-sm:     .25rem !default;\r\n\r\n$table-cell-vertical-align:   center !default;\r\n\r\n$table-color:                 $body-color !default;\r\n$table-td-color:              $headings-color !default;\r\n$table-bg:                    transparent !default;\r\n$table-radius:                null !default;\r\n$table-accent-bg:             transparent !default;\r\n\r\n$table-th-font-weight:        500 !default;\r\n\r\n$table-striped-color:         $table-color !default;\r\n$table-striped-bg-factor:     .03 !default;\r\n$table-striped-bg:            rgba($body-color, $table-striped-bg-factor) !default;\r\n\r\n$table-active-color:          $table-color !default;\r\n$table-active-bg-factor:      .1 !default;\r\n$table-active-bg:             rgba($body-color, $table-active-bg-factor) !default;\r\n\r\n$table-hover-color:           $table-color !default;\r\n$table-hover-bg-factor:       .075 !default;\r\n$table-hover-bg:              rgba($body-color, $table-hover-bg-factor) !default;\r\n\r\n$table-border-factor:         .1 !default;\r\n$table-border-width:          $border-width !default;\r\n$table-border-color:          $border-color !default;\r\n\r\n$table-striped-order:         even !default;\r\n\r\n$table-group-separator-color: currentColor !default;\r\n\r\n$table-caption-color:         $text-muted !default;\r\n\r\n$table-bg-scale:              -80% !default;\r\n// scss-docs-end table-variables\r\n\r\n// scss-docs-start table-loop\r\n$table-variants: (\r\n  \"primary\":    shift-color($primary, $table-bg-scale),\r\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\r\n  \"success\":    shift-color($success, $table-bg-scale),\r\n  \"info\":       shift-color($info, $table-bg-scale),\r\n  \"warning\":    shift-color($warning, $table-bg-scale),\r\n  \"danger\":     shift-color($danger, $table-bg-scale),\r\n  \"light\":      $light,\r\n  \"dark\":       $dark,\r\n  \"gray\":       shift-color($gray, $table-bg-scale),\r\n) !default;\r\n// scss-docs-end table-loop\r\n\r\n\r\n// Buttons + Forms\r\n//\r\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\r\n\r\n// scss-docs-start input-btn-variables\r\n$input-btn-padding-y:         .5rem !default;\r\n$input-btn-padding-x:         1.5rem !default;\r\n$input-btn-font-family:       null !default;\r\n$input-btn-font-size:         $font-size-base !default;\r\n$input-btn-line-height:       $line-height-base !default;\r\n\r\n$input-btn-focus-width:         0rem !default;\r\n$input-btn-focus-color-opacity: .15 !default;\r\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\r\n$input-btn-focus-blur:          .25rem !default;\r\n$input-btn-focus-box-shadow:    0 .125rem $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\r\n\r\n$input-btn-padding-y-xs:      0.125rem !default;\r\n$input-btn-padding-x-xs:      0.5rem !default;\r\n$input-btn-font-size-xs:      $font-size-xs !default;\r\n\r\n$input-btn-padding-y-sm:      .25rem !default;\r\n$input-btn-padding-x-sm:      1rem !default;\r\n$input-btn-font-size-sm:      $font-size-sm !default;\r\n\r\n$input-btn-padding-y-lg:      .5rem !default;\r\n$input-btn-padding-x-lg:      1.5rem !default;\r\n$input-btn-font-size-lg:      $font-size-lg !default;\r\n\r\n$input-btn-border-width:      $border-width !default;\r\n// scss-docs-end input-btn-variables\r\n\r\n\r\n// Buttons\r\n//\r\n// For each of Bootstrap's buttons, define text, background, and border color.\r\n\r\n// scss-docs-start btn-variables\r\n$btn-padding-y:               $input-btn-padding-y !default;\r\n$btn-padding-x:               $input-btn-padding-x !default;\r\n$btn-font-family:             $input-btn-font-family !default;\r\n$btn-font-size:               $input-btn-font-size !default;\r\n$btn-line-height:             $input-btn-line-height !default;\r\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\r\n\r\n$btn-padding-y-xs:            $input-btn-padding-y-xs !default;\r\n$btn-padding-x-xs:            $input-btn-padding-x-xs !default;\r\n$btn-font-size-xs:            $input-btn-font-size-xs !default;\r\n\r\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\r\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\r\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\r\n\r\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\r\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\r\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\r\n\r\n$btn-border-width:            $input-btn-border-width !default;\r\n\r\n$btn-font-weight:             $font-weight-normal !default;\r\n$btn-box-shadow:              0 0px 0px 0 rgba($black,0) !default;\r\n$btn-box-color-shadow:        $box-color-shadow !default;\r\n$btn-box-color-shadow-hover:  $box-color-shadow-hover !default;\r\n$btn-box-shadow-tint:         $box-color-shadow-tint!default;\r\n$btn-box-shadow-shade:        $box-color-shadow-shade!default;\r\n$btn-focus-width:             $input-btn-focus-width !default;\r\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\r\n$btn-disabled-opacity:        .65 !default;\r\n$btn-active-box-shadow:       0 0px 0px rgba($black, .0) !default;\r\n\r\n$btn-link-color:              $link-color !default;\r\n$btn-link-hover-color:        $link-hover-color !default;\r\n$btn-link-disabled-color:     $gray-600 !default;\r\n\r\n// Allows for customizing button radius independently from global border radius\r\n$btn-border-radius-xs: $border-radius-xs!default;\r\n$btn-border-radius:           $border-radius-sm !default;\r\n$btn-border-radius-sm:        $border-radius-sm !default;\r\n$btn-border-radius-lg:        $border-radius-sm !default;\r\n\r\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$btn-hover-bg-shade-amount:       15% !default;\r\n$btn-hover-bg-tint-amount:        15% !default;\r\n$btn-hover-border-shade-amount:   20% !default;\r\n$btn-hover-border-tint-amount:    10% !default;\r\n$btn-active-bg-shade-amount:      20% !default;\r\n$btn-active-bg-tint-amount:       20% !default;\r\n$btn-active-border-shade-amount:  25% !default;\r\n$btn-active-border-tint-amount:   10% !default;\r\n// scss-docs-end btn-variables\r\n\r\n\r\n// Forms\r\n\r\n// scss-docs-start form-text-variables\r\n$form-text-margin-top:                  .25rem !default;\r\n$form-text-font-size:                   $small-font-size !default;\r\n$form-text-font-style:                  null !default;\r\n$form-text-font-weight:                 null !default;\r\n$form-text-color:                       $text-muted !default;\r\n// scss-docs-end form-text-variables\r\n\r\n// scss-docs-start form-label-variables\r\n$form-label-margin-bottom:              .5rem !default;\r\n$form-label-font-size:                  null !default;\r\n$form-label-font-style:                 null !default;\r\n$form-label-font-weight:                null !default;\r\n$form-label-color:                      null !default;\r\n// scss-docs-end form-label-variables\r\n\r\n// scss-docs-start form-input-variables\r\n$input-padding-y:                       .5rem;\r\n$input-padding-x:                       1rem;\r\n$input-font-family:                     $input-btn-font-family !default;\r\n$input-font-size:                       $input-btn-font-size !default;\r\n$input-font-weight:                     $font-weight-base !default;\r\n$input-line-height:                     $input-btn-line-height !default;\r\n\r\n$input-padding-y-xs:                    $input-btn-padding-y-xs !default;\r\n$input-padding-x-xs:                    $input-btn-padding-x-xs !default;\r\n$input-font-size-xs:                    $input-btn-font-size-xs !default;\r\n\r\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\r\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\r\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\r\n\r\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\r\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\r\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\r\n\r\n$input-bg:                              $white !default;\r\n$input-disabled-bg:                     $gray-200 !default;\r\n$input-disabled-border-color:           null !default;\r\n\r\n$input-color:                           $body-color !default;\r\n$input-border-color:                    $border-color !default;\r\n$input-border-width:                    $input-btn-border-width !default;\r\n$input-box-shadow:                      0 0 0 0 !default;\r\n\r\n$input-border-radius:                   .25rem !default;\r\n$input-border-radius-sm:                $input-border-radius !default;\r\n$input-border-radius-lg:                $input-border-radius !default;\r\n\r\n$input-focus-bg:                        $input-bg !default;\r\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\r\n$input-focus-color:                     $input-color !default;\r\n$input-focus-width:                     $input-btn-focus-width !default;\r\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\r\n\r\n$input-placeholder-color:               $gray-600 !default;\r\n$input-plaintext-color:                 $body-color !default;\r\n\r\n$input-height-border:                   $input-border-width * 2 !default;\r\n\r\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\r\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\r\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\r\n\r\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\r\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\r\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\r\n\r\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n// scss-docs-end form-input-variables\r\n\r\n// scss-docs-start form-check-variables\r\n$form-check-input-width:                  1em !default;\r\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\r\n$form-check-padding-start:                $form-check-input-width + .5em !default;\r\n$form-check-margin-bottom:                .125rem !default;\r\n$form-check-label-color:                  null !default;\r\n$form-check-label-cursor:                 null !default;\r\n$form-check-transition:                   color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$form-check-input-active-filter:          brightness(90%) !default;\r\n\r\n$form-check-input-bg:                     $input-bg !default;\r\n$form-check-input-border:                 1px solid rgba($black, .25) !default;\r\n$form-check-input-border-radius:          .25em !default;\r\n$form-check-radio-border-radius:          50% !default;\r\n$form-check-input-focus-border:           $input-focus-border-color !default;\r\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow !default;\r\n\r\n$form-check-input-checked-color:          $component-active-color !default;\r\n$form-check-input-checked-bg-color:       $component-active-bg !default;\r\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\r\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\r\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\r\n\r\n$form-check-input-indeterminate-color:          $component-active-color !default;\r\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\r\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\r\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\r\n\r\n$form-check-input-disabled-opacity:        .5 !default;\r\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\r\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\r\n\r\n$form-check-inline-margin-end:    1rem !default;\r\n// scss-docs-end form-check-variables\r\n\r\n// scss-docs-start form-switch-variables\r\n// $form-switch-color:               rgba(0, 0, 0, .25) !default;\r\n// $form-switch-width:               2em !default;\r\n// $form-switch-size:                .5rem !default;\r\n// $form-switch-padding-start:       $form-switch-width + .5em !default;\r\n// $form-switch-bg-image:            url(\"data:image/svg+xml,<svg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='1' y='1' width='18' height='18' rx='9' fill='white' stroke='#{$white}' stroke-width='2'/></svg>\") !default;\r\n// $form-switch-border-radius:       $form-switch-width !default;\r\n// $form-switch-transition:          background-position .15s ease-in-out !default;\r\n\r\n// $form-switch-focus-color:         $input-focus-border-color !default;\r\n// $form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='1' y='1' width='18' height='18' rx='9' fill='white' stroke='#{$white}' stroke-width='2'/></svg>\") !default;\r\n\r\n// $form-switch-checked-color:       $component-active-bg !default;\r\n// $form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='1' y='1' width='18' height='18' rx='9' fill='white' stroke='#{$form-check-input-checked-color}' stroke-width='2'/></svg>\") !default;\r\n// $form-switch-checked-bg-position: right center !default;\r\n$form-switch-color:               rgba($black, .25) !default;\r\n$form-switch-width:               2em !default;\r\n$form-switch-padding-start:       $form-switch-width + .5em !default;\r\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\r\n$form-switch-border-radius:       $form-switch-width !default;\r\n$form-switch-transition:          background-position .15s ease-in-out !default;\r\n\r\n$form-switch-focus-color:         $input-focus-border-color !default;\r\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\r\n\r\n$form-switch-checked-color:       $component-active-color !default;\r\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\r\n$form-switch-checked-bg-position: right center !default;\r\n// scss-docs-end form-switch-variables\r\n\r\n// scss-docs-start input-group-variables\r\n$input-group-addon-padding-y:           $input-padding-y !default;\r\n$input-group-addon-padding-x:           $input-padding-x !default;\r\n$input-group-addon-font-weight:         $input-font-weight !default;\r\n$input-group-addon-color:               $input-color !default;\r\n$input-group-addon-bg:                  $input-bg !default;\r\n$input-group-addon-border-color:        $input-border-color !default;\r\n// scss-docs-end input-group-variables\r\n\r\n// scss-docs-start form-select-variables\r\n$form-select-padding-y:             $input-padding-y !default;\r\n$form-select-padding-x:             $input-padding-x !default;\r\n$form-select-font-family:           $input-font-family !default;\r\n$form-select-font-size:             $input-font-size !default;\r\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\r\n$form-select-font-weight:           $input-font-weight !default;\r\n$form-select-line-height:           $input-line-height !default;\r\n$form-select-color:                 $input-color !default;\r\n$form-select-bg:                    $input-bg !default;\r\n$form-select-disabled-color:        null !default;\r\n$form-select-disabled-bg:           $gray-200 !default;\r\n$form-select-disabled-border-color: $input-disabled-border-color !default;\r\n$form-select-bg-position:           right $form-select-padding-x center !default;\r\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\r\n$form-select-indicator-color:       $gray-800 !default;\r\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\r\n\r\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\r\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\r\n\r\n$form-select-border-width:        $input-border-width !default;\r\n$form-select-border-color:        $input-border-color !default;\r\n$form-select-border-radius:       $border-radius !default;\r\n$form-select-box-shadow:          $box-shadow-inset !default;\r\n\r\n$form-select-focus-border-color:  $input-focus-border-color !default;\r\n$form-select-focus-width:         $input-focus-width !default;\r\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\r\n\r\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\r\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\r\n$form-select-font-size-sm:        $input-font-size-sm !default;\r\n\r\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\r\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\r\n$form-select-font-size-lg:        $input-font-size-lg !default;\r\n\r\n$form-select-transition:          $input-transition !default;\r\n// scss-docs-end form-select-variables\r\n\r\n// scss-docs-start form-range-variables\r\n$form-range-track-width:          100% !default;\r\n$form-range-track-height:         .25rem !default;\r\n$form-range-track-cursor:         pointer !default;\r\n$form-range-track-bg:             tint-color($primary, 80%) !default;\r\n$form-range-track-border-radius:  1rem !default;\r\n$form-range-track-box-shadow:     $box-color-shadow rgba($primary, .1) !default;\r\n\r\n$form-range-thumb-width:                   1rem !default;\r\n$form-range-thumb-height:                  $form-range-thumb-width !default;\r\n$form-range-thumb-bg:                      $white !default;\r\n$form-range-thumb-border:                  2px solid $primary !default;\r\n$form-range-thumb-border-radius:           1rem !default;\r\n$form-range-thumb-box-shadow:              $box-color-shadow-hover rgba($primary, .5) !default;\r\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\r\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\r\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 80%) !default;\r\n$form-range-thumb-disabled-bg:             $gray-500 !default;\r\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n// scss-docs-end form-range-variables\r\n\r\n// scss-docs-start form-file-variables\r\n$form-file-button-color:          $input-color !default;\r\n$form-file-button-bg:             $input-group-addon-bg !default;\r\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%) !default;\r\n// scss-docs-end form-file-variables\r\n\r\n// scss-docs-start form-floating-variables\r\n$form-floating-height:            add(3.5rem, $input-height-border) !default;\r\n$form-floating-line-height:       1.25 !default;\r\n$form-floating-padding-x:         $input-padding-x !default;\r\n$form-floating-padding-y:         1rem !default;\r\n$form-floating-input-padding-t:   1.625rem !default;\r\n$form-floating-input-padding-b:   .625rem !default;\r\n$form-floating-label-opacity:     .65 !default;\r\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem) !default;\r\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out !default;\r\n// scss-docs-end form-floating-variables\r\n\r\n// Form validation\r\n\r\n// scss-docs-start form-feedback-variables\r\n$form-feedback-margin-top:          $form-text-margin-top !default;\r\n$form-feedback-font-size:           $form-text-font-size !default;\r\n$form-feedback-font-style:          $form-text-font-style !default;\r\n$form-feedback-valid-color:         $success !default;\r\n$form-feedback-invalid-color:       $danger !default;\r\n\r\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\r\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\r\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\r\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\r\n// scss-docs-end form-feedback-variables\r\n\r\n// scss-docs-start form-validation-states\r\n$form-validation-states: (\r\n  \"valid\": (\r\n    \"color\": $form-feedback-valid-color,\r\n    \"icon\": $form-feedback-icon-valid\r\n  ),\r\n  \"invalid\": (\r\n    \"color\": $form-feedback-invalid-color,\r\n    \"icon\": $form-feedback-icon-invalid\r\n  )\r\n) !default;\r\n// scss-docs-end form-validation-states\r\n\r\n// Z-index master list\r\n//\r\n// Warning: Avoid customizing these values. They're used for a bird's eye view\r\n// of components dependent on the z-axis and are designed to all work together.\r\n\r\n// scss-docs-start zindex-stack\r\n$zindex-general:                    900 !default;\r\n$zindex-dropdown:                   1000 !default;\r\n$zindex-sticky:                     1020 !default;\r\n$zindex-fixed:                      1030 !default;\r\n$zindex-modal-backdrop:             1040 !default;\r\n$zindex-offcanvas:                  1050 !default;\r\n$zindex-modal:                      1060 !default;\r\n$zindex-popover:                    1070 !default;\r\n$zindex-tooltip:                    1080 !default;\r\n// scss-docs-end zindex-stack\r\n\r\n\r\n// Navs\r\n\r\n// scss-docs-start nav-variables\r\n$nav-link-padding-y:                $spacer * .5 !default;\r\n$nav-link-padding-x:                $spacer * 1 !default;\r\n$nav-link-font-size:                null !default;\r\n$nav-link-font-weight:              null !default;\r\n$nav-link-color:                    $link-color !default;\r\n$nav-link-hover-color:              $link-hover-color !default;\r\n$nav-link-transition:               all 300ms ease-in-out!default;\r\n$nav-link-disabled-color:           $gray-500 !default;\r\n\r\n$nav-tabs-border-color:             $gray-300 !default;\r\n$nav-tabs-border-width:             0 !default;\r\n$nav-tabs-border-radius:            $border-radius-sm !default;\r\n$nav-tabs-shadow:                   $box-shadow-sm !default;\r\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\r\n$nav-tabs-link-active-color:        $white !default;\r\n$nav-tabs-link-active-bg:           $primary !default;\r\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\r\n\r\n$nav-pills-border-radius:           $border-radius-pill;\r\n$nav-pills-shadow:                  null !default;\r\n$nav-pills-link-active-color:       $component-active-color !default;\r\n$nav-pills-link-active-bg:          $component-active-bg !default;\r\n// scss-docs-end nav-variables\r\n\r\n\r\n// Navbar\r\n\r\n// scss-docs-start navbar-variables\r\n$navbar-padding-y:                  $spacer * .5 !default;\r\n$navbar-padding-x:                  null !default;\r\n\r\n$navbar-nav-link-padding-x:         .5rem !default;\r\n\r\n$navbar-brand-font-size:            $font-size-lg !default;\r\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\r\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\r\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\r\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\r\n$navbar-brand-margin-end:           1rem !default;\r\n\r\n$navbar-toggler-padding-y:          .25rem !default;\r\n$navbar-toggler-padding-x:          .75rem !default;\r\n$navbar-toggler-font-size:          $font-size-lg !default;\r\n$navbar-toggler-border-radius:      $btn-border-radius !default;\r\n$navbar-toggler-focus-width:        $btn-focus-width !default;\r\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\r\n// scss-docs-end navbar-variables\r\n\r\n// scss-docs-start navbar-theme-variables\r\n$navbar-dark-color:                 rgba($white, .55) !default;\r\n$navbar-dark-hover-color:           rgba($white, .75) !default;\r\n$navbar-dark-active-color:          $white !default;\r\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\r\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\r\n\r\n$navbar-light-color:                rgba($black, .55) !default;\r\n$navbar-light-hover-color:          rgba($black, .7) !default;\r\n$navbar-light-active-color:         rgba($black, .9) !default;\r\n$navbar-light-disabled-color:       rgba($black, .3) !default;\r\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\r\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\r\n\r\n$navbar-light-brand-color:                $navbar-light-active-color !default;\r\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\r\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\r\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\r\n// scss-docs-end navbar-theme-variables\r\n\r\n\r\n// Dropdowns\r\n//\r\n// Dropdown menu container and contents.\r\n\r\n// scss-docs-start dropdown-variables\r\n$dropdown-min-width:                10rem !default;\r\n$dropdown-padding-x:                0 !default;\r\n$dropdown-padding-y:                .5rem !default;\r\n$dropdown-spacer:                   .125rem !default;\r\n$dropdown-font-size:                $font-size-base !default;\r\n$dropdown-color:                    $body-color !default;\r\n$dropdown-bg:                       $white !default;\r\n$dropdown-border-color:             rgba($black, .0) !default;\r\n$dropdown-border-radius:            $border-radius-sm !default;\r\n$dropdown-border-width:             $border-width !default;\r\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\r\n$dropdown-divider-bg:               $dropdown-border-color !default;\r\n$dropdown-divider-margin-y:         $spacer * .5 !default;\r\n$dropdown-box-shadow:               $box-shadow-lg !default;\r\n\r\n$dropdown-link-color:               $gray-600 !default;\r\n$dropdown-link-hover-color:         shade-color($primary, 10%) !default;\r\n$dropdown-link-hover-bg:            transparent !default;\r\n\r\n$dropdown-link-active-color:        $component-active-color !default;\r\n$dropdown-link-active-bg:           $component-active-bg !default;\r\n\r\n$dropdown-link-disabled-color:      $gray-500 !default;\r\n\r\n$dropdown-item-padding-y:           $spacer * .25 !default;\r\n$dropdown-item-padding-x:           $spacer !default;\r\n\r\n$dropdown-header-color:             $gray-600 !default;\r\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\r\n// scss-docs-end dropdown-variables\r\n\r\n// scss-docs-start dropdown-dark-variables\r\n$dropdown-dark-color:               $gray-300 !default;\r\n$dropdown-dark-bg:                  $gray-800 !default;\r\n$dropdown-dark-border-color:        $dropdown-border-color !default;\r\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\r\n$dropdown-dark-box-shadow:          null !default;\r\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\r\n$dropdown-dark-link-hover-color:    $white !default;\r\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\r\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\r\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\r\n$dropdown-dark-link-disabled-color: $gray-500 !default;\r\n$dropdown-dark-header-color:        $gray-500 !default;\r\n// scss-docs-end dropdown-dark-variables\r\n\r\n\r\n// Pagination\r\n\r\n// scss-docs-start pagination-variables\r\n$pagination-padding-y:              .25rem !default;\r\n$pagination-padding-x:              1rem !default;\r\n$pagination-padding-y-sm:           .125rem !default;\r\n$pagination-padding-x-sm:           .75rem !default;\r\n$pagination-padding-y-lg:           .5rem !default;\r\n$pagination-padding-x-lg:           1.5rem !default;\r\n\r\n$pagination-color:                  $link-color !default;\r\n$pagination-bg:                     $white !default;\r\n$pagination-border-width:           $border-width !default;\r\n$pagination-border-radius:          $border-radius !default;\r\n$pagination-margin-start:           -$pagination-border-width !default;\r\n$pagination-border-color:           $gray-300 !default;\r\n\r\n$pagination-focus-color:            $link-hover-color !default;\r\n$pagination-focus-bg:               $gray-200 !default;\r\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\r\n$pagination-focus-outline:          0 !default;\r\n\r\n$pagination-hover-color:            $link-hover-color !default;\r\n$pagination-hover-bg:               $gray-200 !default;\r\n$pagination-hover-border-color:     $gray-300 !default;\r\n\r\n$pagination-active-color:           $component-active-color !default;\r\n$pagination-active-bg:              $component-active-bg !default;\r\n$pagination-active-border-color:    $pagination-active-bg !default;\r\n\r\n$pagination-disabled-color:         $gray-600 !default;\r\n$pagination-disabled-bg:            $white !default;\r\n$pagination-disabled-border-color:  $gray-300 !default;\r\n\r\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\r\n\r\n$pagination-border-radius-sm:       $border-radius-sm !default;\r\n$pagination-border-radius-lg:       $border-radius !default;\r\n// scss-docs-end pagination-variables\r\n\r\n\r\n// Cards\r\n\r\n// scss-docs-start card-variables\r\n$card-spacer-y:                     $spacer * 1.5 !default;\r\n$card-spacer-x:                     $spacer * 1.5 !default;\r\n$card-title-spacer-y:               $spacer * .5 !default;\r\n$card-border-width:                 0 !default;\r\n$card-border-radius:                $border-radius;\r\n$card-border-color:                 rgba($black, .125) !default;\r\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\r\n$card-cap-padding-y:                $card-spacer-y !default;\r\n$card-cap-padding-x:                $card-spacer-x !default;\r\n$card-cap-bg:                       $white !default;\r\n$card-cap-color:                    null !default;\r\n$card-height:                       null !default;\r\n$card-color:                        null !default;\r\n$card-bg:                           $white !default;\r\n$card-img-overlay-padding:          $spacer !default;\r\n$card-group-margin:                 $grid-gutter-width * .5 !default;\r\n\r\n$card-box-shadow:                   $box-shadow-lg !default;\r\n// scss-docs-end card-variables\r\n\r\n// Accordion\r\n\r\n// scss-docs-start accordion-variables\r\n$accordion-padding-y:                     1rem !default;\r\n$accordion-padding-x:                     1.5rem !default;\r\n$accordion-color:                         $body-color !default;\r\n$accordion-bg:                            $body-bg !default;\r\n$accordion-border-width:                  $border-width !default;\r\n$accordion-border-color:                  rgba($black, .125) !default;\r\n$accordion-border-radius:                 $border-radius !default;\r\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\r\n\r\n$accordion-body-padding-y:                $accordion-padding-y !default;\r\n$accordion-body-padding-x:                $accordion-padding-x !default;\r\n\r\n$accordion-button-padding-y:              $accordion-padding-y !default;\r\n$accordion-button-padding-x:              $accordion-padding-x !default;\r\n$accordion-button-color:                  $accordion-color !default;\r\n$accordion-button-bg:                     $accordion-bg !default;\r\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\r\n$accordion-button-active-bg:              tint-color($component-active-bg, 90%) !default;\r\n$accordion-button-active-color:           shade-color($primary, 10%) !default;\r\n\r\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\r\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\r\n\r\n$accordion-icon-width:                    1.25rem !default;\r\n$accordion-icon-color:                    $accordion-color !default;\r\n$accordion-icon-active-color:             $accordion-button-active-color !default;\r\n$accordion-icon-transition:               transform .2s ease-in-out !default;\r\n$accordion-icon-transform:                rotate(-180deg) !default;\r\n\r\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n// scss-docs-end accordion-variables\r\n\r\n// Tooltips\r\n\r\n// scss-docs-start tooltip-variables\r\n$tooltip-font-size:                 $font-size-sm !default;\r\n$tooltip-max-width:                 200px !default;\r\n$tooltip-color:                     $white !default;\r\n$tooltip-bg:                        $black !default;\r\n$tooltip-border-radius:             $border-radius-sm !default;\r\n$tooltip-opacity:                   .9 !default;\r\n$tooltip-padding-y:                 $spacer * .25 !default;\r\n$tooltip-padding-x:                 $spacer * .5 !default;\r\n$tooltip-margin:                    0 !default;\r\n\r\n$tooltip-arrow-width:               .8rem !default;\r\n$tooltip-arrow-height:              .4rem !default;\r\n$tooltip-arrow-color:               $tooltip-bg !default;\r\n// scss-docs-end tooltip-variables\r\n\r\n// Form tooltips must come after regular tooltips\r\n// scss-docs-start tooltip-feedback-variables\r\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\r\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\r\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\r\n$form-feedback-tooltip-line-height:   null !default;\r\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\r\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\r\n// scss-docs-end tooltip-feedback-variables\r\n\r\n\r\n// Popovers\r\n\r\n// scss-docs-start popover-variables\r\n$popover-font-size:                 $font-size-sm !default;\r\n$popover-bg:                        $white !default;\r\n$popover-max-width:                 276px !default;\r\n$popover-border-width:              $border-width !default;\r\n$popover-border-color:              rgba($black, .1) !default;\r\n$popover-border-radius:             $border-radius-sm !default;\r\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\r\n$popover-box-shadow:                $box-shadow !default;\r\n\r\n$popover-header-bg:                 shade-color($popover-bg, 6%) !default;\r\n$popover-header-color:              $headings-color !default;\r\n$popover-header-padding-y:          .5rem !default;\r\n$popover-header-padding-x:          $spacer !default;\r\n\r\n$popover-body-color:                $body-color !default;\r\n$popover-body-padding-y:            $spacer !default;\r\n$popover-body-padding-x:            $spacer !default;\r\n\r\n$popover-arrow-width:               1rem !default;\r\n$popover-arrow-height:              .5rem !default;\r\n$popover-arrow-color:               $popover-bg !default;\r\n\r\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\r\n// scss-docs-end popover-variables\r\n\r\n\r\n// Toasts\r\n\r\n// scss-docs-start toast-variables\r\n$toast-max-width:                   350px !default;\r\n$toast-padding-x:                   1rem !default;\r\n$toast-padding-y:                   .5rem !default;\r\n$toast-font-size:                   .875rem !default;\r\n$toast-color:                       null !default;\r\n$toast-background-color:            rgba($white, .85) !default;\r\n$toast-border-width:                1px !default;\r\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\r\n$toast-border-radius:               $border-radius-sm !default;\r\n$toast-box-shadow:                  $box-shadow !default;\r\n$toast-spacing:                     $container-padding-x !default;\r\n\r\n$toast-header-color:                $gray-600 !default;\r\n$toast-header-background-color:     rgba($white, .85) !default;\r\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\r\n// scss-docs-end toast-variables\r\n\r\n\r\n// Badges\r\n\r\n// scss-docs-start badge-variables\r\n$badge-font-size:                   .75em !default;\r\n$badge-font-weight:                 $font-weight-bold !default;\r\n$badge-color:                       $white !default;\r\n$badge-padding-y:                   .125rem !default;\r\n$badge-padding-x:                   .5rem !default;\r\n$badge-border-radius:               $border-radius-sm !default;\r\n// scss-docs-end badge-variables\r\n\r\n\r\n// Modals\r\n\r\n// scss-docs-start modal-variables\r\n$modal-inner-padding:               $spacer !default;\r\n\r\n$modal-footer-margin-between:       .5rem !default;\r\n\r\n$modal-dialog-margin:               .5rem !default;\r\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\r\n\r\n$modal-title-line-height:           $line-height-base !default;\r\n\r\n$modal-content-color:               null !default;\r\n$modal-content-bg:                  $white !default;\r\n$modal-content-border-color:        rgba($black, .1) !default;\r\n$modal-content-border-width:        $border-width !default;\r\n$modal-content-border-radius:       $border-radius !default;\r\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\r\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\r\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\r\n\r\n$modal-backdrop-bg:                 $black !default;\r\n$modal-backdrop-opacity:            .5 !default;\r\n$modal-header-border-color:         $border-color !default;\r\n$modal-footer-border-color:         $modal-header-border-color !default;\r\n$modal-header-border-width:         $modal-content-border-width !default;\r\n$modal-footer-border-width:         $modal-header-border-width !default;\r\n$modal-header-padding-y:            $modal-inner-padding !default;\r\n$modal-header-padding-x:            $modal-inner-padding !default;\r\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n\r\n$modal-sm:                          300px !default;\r\n$modal-md:                          500px !default;\r\n$modal-lg:                          800px !default;\r\n$modal-xl:                          1140px !default;\r\n\r\n$modal-fade-transform:              translate(0, -50px) !default;\r\n$modal-show-transform:              none !default;\r\n$modal-transition:                  transform .3s ease-out !default;\r\n$modal-scale-transform:             scale(1.02) !default;\r\n// scss-docs-end modal-variables\r\n\r\n\r\n// Alerts\r\n//\r\n// Define alert colors, border radius, and padding.\r\n\r\n// scss-docs-start alert-variables\r\n$alert-padding-y:               $spacer !default;\r\n$alert-padding-x:               $spacer !default;\r\n$alert-margin-bottom:           1rem !default;\r\n$alert-border-radius:           .25rem;\r\n$alert-link-font-weight:        $font-weight-bold !default;\r\n$alert-border-width:            $border-width * 2 !default;\r\n$alert-bg-scale:                -80% !default;\r\n$alert-border-scale:            0% !default;\r\n$alert-color-scale:             30% !default;\r\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\r\n// scss-docs-end alert-variables\r\n\r\n\r\n// Progress bars\r\n\r\n// scss-docs-start progress-variables\r\n$progress-height:                   1rem !default;\r\n$progress-font-size:                $font-size-base * .75 !default;\r\n$progress-bg:                       $gray-200 !default;\r\n$progress-border-radius:            $border-radius !default;\r\n$progress-box-shadow:               $box-shadow-inset !default;\r\n$progress-bar-color:                $white !default;\r\n$progress-bar-bg:                   $primary !default;\r\n$progress-bar-animation-timing:     1s linear infinite !default;\r\n$progress-bar-transition:           width .6s ease !default;\r\n// scss-docs-end progress-variables\r\n\r\n\r\n// List group\r\n\r\n// scss-docs-start list-group-variables\r\n$list-group-color:                  $gray-900 !default;\r\n$list-group-bg:                     $white !default;\r\n$list-group-border-color:           rgba($black, .125) !default;\r\n$list-group-border-width:           $border-width !default;\r\n$list-group-border-radius:          $border-radius-sm !default;\r\n\r\n$list-group-item-padding-y:         $spacer * .5 !default;\r\n$list-group-item-padding-x:         $spacer !default;\r\n$list-group-item-bg-scale:          -80% !default;\r\n$list-group-item-color-scale:       40% !default;\r\n\r\n$list-group-hover-bg:               $gray-100 !default;\r\n$list-group-active-color:           $component-active-color !default;\r\n$list-group-active-bg:              $component-active-bg !default;\r\n$list-group-active-border-color:    $list-group-active-bg !default;\r\n\r\n$list-group-disabled-color:         $gray-600 !default;\r\n$list-group-disabled-bg:            $list-group-bg !default;\r\n\r\n$list-group-action-color:           $gray-700 !default;\r\n$list-group-action-hover-color:     $list-group-action-color !default;\r\n\r\n$list-group-action-active-color:    $body-color !default;\r\n$list-group-action-active-bg:       $gray-200 !default;\r\n// scss-docs-end list-group-variables\r\n\r\n\r\n// Image thumbnails\r\n\r\n// scss-docs-start thumbnail-variables\r\n$thumbnail-padding:                 .25rem !default;\r\n$thumbnail-bg:                      $body-bg !default;\r\n$thumbnail-border-width:            $border-width !default;\r\n$thumbnail-border-color:            $gray-300 !default;\r\n$thumbnail-border-radius:           $border-radius !default;\r\n$thumbnail-box-shadow:              $box-shadow-sm !default;\r\n// scss-docs-end thumbnail-variables\r\n\r\n\r\n// Figures\r\n\r\n// scss-docs-start figure-variables\r\n$figure-caption-font-size:          $small-font-size !default;\r\n$figure-caption-color:              $gray-600 !default;\r\n// scss-docs-end figure-variables\r\n\r\n\r\n// Breadcrumbs\r\n\r\n// scss-docs-start breadcrumb-variables\r\n$breadcrumb-font-size:              null !default;\r\n$breadcrumb-padding-y:              0 !default;\r\n$breadcrumb-padding-x:              0 !default;\r\n$breadcrumb-item-padding-x:         .5rem !default;\r\n$breadcrumb-margin-bottom:          1rem !default;\r\n$breadcrumb-bg:                     null !default;\r\n$breadcrumb-divider-color:          $gray-600 !default;\r\n$breadcrumb-active-color:           $gray-600 !default;\r\n$breadcrumb-divider:                quote(\"/\") !default;\r\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\r\n$breadcrumb-border-radius:          null !default;\r\n// scss-docs-end breadcrumb-variables\r\n\r\n// Carousel\r\n\r\n// scss-docs-start carousel-variables\r\n$carousel-control-color:             $white !default;\r\n$carousel-control-width:             15% !default;\r\n$carousel-control-opacity:           .5 !default;\r\n$carousel-control-hover-opacity:     .9 !default;\r\n$carousel-control-transition:        opacity .15s ease !default;\r\n\r\n$carousel-indicator-width:           20px !default;\r\n$carousel-indicator-height:          20px !default;\r\n$carousel-indicator-hit-area-height: 0px !default;\r\n$carousel-indicator-spacer:          3px !default;\r\n$carousel-indicator-opacity:         .5 !default;\r\n$carousel-indicator-active-bg:       $white !default;\r\n$carousel-indicator-active-opacity:  1 !default;\r\n$carousel-indicator-transition:      opacity .6s ease !default;\r\n\r\n$carousel-caption-width:             70% !default;\r\n$carousel-caption-color:             $white !default;\r\n$carousel-caption-padding-y:         1.25rem !default;\r\n$carousel-caption-spacer:            1.25rem !default;\r\n\r\n$carousel-control-icon-width:        2rem !default;\r\n\r\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\r\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\r\n\r\n$carousel-transition-duration:       .6s !default;\r\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\r\n\r\n$carousel-dark-indicator-active-bg:  $black !default;\r\n$carousel-dark-caption-color:        $black !default;\r\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\r\n// scss-docs-end carousel-variables\r\n\r\n\r\n// Spinners\r\n\r\n// scss-docs-start spinner-variables\r\n$spinner-width:           2rem !default;\r\n$spinner-height:          $spinner-width !default;\r\n$spinner-vertical-align:  -.125em !default;\r\n$spinner-border-width:    .25em !default;\r\n$spinner-animation-speed: .75s !default;\r\n\r\n$spinner-width-sm:        1rem !default;\r\n$spinner-height-sm:       $spinner-width-sm !default;\r\n$spinner-border-width-sm: .2em !default;\r\n// scss-docs-end spinner-variables\r\n\r\n\r\n// Close\r\n\r\n// scss-docs-start close-variables\r\n$btn-close-width:            1em !default;\r\n$btn-close-height:           $btn-close-width !default;\r\n$btn-close-padding-x:        .25em !default;\r\n$btn-close-padding-y:        $btn-close-padding-x !default;\r\n$btn-close-color:            $black !default;\r\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\r\n$btn-close-focus-shadow:     $input-btn-focus-box-shadow !default;\r\n$btn-close-opacity:          1 !default;\r\n$btn-close-radius:           $border-radius-sm !default;\r\n$btn-close-hover-opacity:    1 !default;\r\n$btn-close-focus-opacity:    1 !default;\r\n$btn-close-disabled-opacity: .25 !default;\r\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\r\n// scss-docs-end close-variables\r\n\r\n\r\n// Offcanvas\r\n\r\n// scss-docs-start offcanvas-variables\r\n$offcanvas-padding-y:               $modal-inner-padding !default;\r\n$offcanvas-padding-x:               $modal-inner-padding !default;\r\n$offcanvas-horizontal-width:        400px !default;\r\n$offcanvas-vertical-height:         30vh !default;\r\n$offcanvas-transition-duration:     .3s !default;\r\n$offcanvas-border-color:            $modal-content-border-color !default;\r\n$offcanvas-border-width:            $modal-content-border-width !default;\r\n$offcanvas-title-line-height:       $modal-title-line-height !default;\r\n$offcanvas-bg-color:                $modal-content-bg !default;\r\n$offcanvas-color:                   $modal-content-color !default;\r\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\r\n// scss-docs-end offcanvas-variables\r\n\r\n// Code\r\n\r\n$code-font-size:                    $small-font-size !default;\r\n$code-color:                        $pink !default;\r\n\r\n$kbd-padding-y:                     .2rem !default;\r\n$kbd-padding-x:                     .4rem !default;\r\n$kbd-font-size:                     $code-font-size !default;\r\n$kbd-color:                         $white !default;\r\n$kbd-bg:                            $gray-900 !default;\r\n\r\n$pre-color:                         null !default;", ".sidebar{\r\n    &.sidebar-mini {\r\n        + {\r\n            .main-content {\r\n                transition: var(--sidebar-transition);\r\n                transition-duration: var(--sidebar-transition-duration);\r\n                transition-timing-function: var(--sidebar-transition-function-ease);\r\n                --sidebar-width: #{$navbar-vertical-mini-width};\r\n                margin-right: var(--sidebar-width);\r\n                margin-left: unset;\r\n            }\r\n        }\r\n        &.sidebar-hover{\r\n            &.sidebar-transparent{\r\n                &:hover{\r\n                    +.main-content{\r\n                        margin-right: var(--sidebar-width);\r\n                        margin-left: unset;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &.sidebar-base{\r\n            .nav-item{\r\n                &:not(.static-item){\r\n                    padding-left: unset;\r\n                }\r\n            }\r\n        }\r\n        .sidebar-list{\r\n            .navbar-nav{\r\n                .nav-item{\r\n                    .nav-link{\r\n                        &:not(.disabled){\r\n                            i.sidenav-mini-icon{\r\n                                margin-right: 0.25rem;\r\n                                margin-left: unset;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &.navs-full-width{\r\n            &.sidebar-base{\r\n                &:not(.sidebar-hover:hover){\r\n                    .navbar-nav{\r\n                        .nav-item:not(.static-item){\r\n                            .nav-link{\r\n                                padding: 0.625rem 1.5rem 0.625rem 1rem;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &.sidebar-base:not(.sidebar-hover:hover){\r\n            .nav-item{\r\n                .nav-link:not(.static-item){\r\n                    span{\r\n                        transform: translateX(100%) scale(0);\r\n                        opacity: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .navbar-brand {\r\n            .logo-title {\r\n                transform: translateX(100%) scale(0);\r\n                opacity: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n.sidebar-hover{\r\n    &:hover{\r\n        .sidebar-list{\r\n            .static-item{\r\n                text-align: right;\r\n            }\r\n        }\r\n    }\r\n}\r\n@include media-breakpoint-down(xl) {\r\n    .sidebar {\r\n        &.sidebar-base {\r\n            &.sidebar-mini {\r\n                transform: translateX(100%);\r\n                .sidebar-header {\r\n                    a.navbar-brand {\r\n                        transform: translate(100%);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .sidebar {\r\n        &.sidebar-base.sidebar-mini+.main-content{\r\n            margin-right: 0;\r\n            margin-left: unset;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(xl) {\r\n    .sidebar {\r\n        .sidebar-toggle {\r\n            left: 18px;\r\n        }\r\n    }\r\n}\r\n\r\n.sidebar-base{\r\n    .sidebar-body{\r\n        padding-left: 1rem;\r\n        padding-right: unset;\r\n    }\r\n    &.sidebar-mini{\r\n        .sidebar-body{\r\n            padding-right: 1rem;\r\n        }\r\n    }\r\n    &.navs-pill, &.navs-rounded{\r\n        .sidebar-body{\r\n            padding-right: unset;\r\n        }\r\n    }\r\n}\r\n\r\n.sidebar-hover:hover.navs-rounded-all .navbar-nav .nav-item:not(.static-item),\r\n.sidebar-hover:hover.navs-pill-all .navbar-nav .nav-item:not(.static-item){\r\n    padding-left: unset;\r\n}\r\n\r\n.sidebar-hover {\r\n    &:hover {\r\n        .logo-title {\r\n            transform: translateX(0%);\r\n            opacity: 1;\r\n        }\r\n        .sidebar-list{\r\n            .navbar-nav{\r\n                .nav-item{\r\n                    .nav-link{\r\n                        &:not(.disabled){\r\n                            span{\r\n                                transform: translateX(0%);\r\n                                opacity: 1;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// navs-full-width\r\n.sidebar-base {\r\n    &.navs-full-width{\r\n        .sidebar-body {\r\n            padding: 0;\r\n        }\r\n        &:not(.sidebar-mini) {\r\n            .navbar-nav {\r\n                .nav-item:not(.static-item) {\r\n                    padding: 0;\r\n                    .sub-nav{\r\n                        .nav-item{\r\n                            .nav-link {\r\n                                padding-right: 2rem;\r\n                                padding-left: 1rem;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n", ".accordion-button{\r\n    &::after{\r\n        margin-left: unset;\r\n        margin-right: auto;\r\n    }\r\n}", ".btn-fixed-end {\r\n    left: 0;\r\n    right: auto;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n}\r\n.btn-fixed-start {\r\n    right: 0;\r\n    left: auto;\r\n    border-top-right-radius: 0;\r\n    border-bottom-right-radius: 0;\r\n}\r\n.btn-fixed-top {\r\n    top: 0;\r\n    border-top-left-radius: 0;\r\n    border-top-right-radius: 0;\r\n}\r\n.btn-fixed-bottom {\r\n    bottom: 0;\r\n    border-bottom-left-radius: 0;\r\n    border-bottom-right-radius: 0;\r\n}\r\n.btn-download{\r\n    left: 0;\r\n    right: unset;\r\n}", ".card-slide {\r\n\t.card-slie-arrow {\r\n\t\tleft: unset;\r\n    \tright: 42px;\r\n\t}\r\n}", ".upload-icone {\r\n    right: 69px;\r\n    left: auto;\r\n}\r\n.profile-media {\r\n\t&:before {\r\n        left: auto;\r\n        right: 9px;\r\n\t}\r\n}", ".progress-widget{\r\n    .progress-detail{\r\n        margin-right: 1.5rem;\r\n        margin-left: unset;\r\n    }\r\n}", ".comment-attagement {\r\n    right: auto;\r\n    left: 1.875rem;\r\n}", "\r\n.credit-card-widget{\r\n    .card-header {\r\n        &::before {\r\n            left: auto;\r\n            right: -3.125rem;\r\n        }\r\n        &::after {\r\n            left: -3.125rem;\r\n            right: auto;\r\n        }\r\n    }\r\n    .primary-gradient-card {\r\n\t\t.master-card-content {\r\n\t\t\t.master-card-2 {\r\n                margin-left: unset;\r\n                margin-right: -2rem;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", ".btn-group{\r\n    direction: ltr;\r\n}\r\n.modal-header{\r\n    .btn-close {\r\n        margin: -.625rem auto -.625rem -.625rem;\r\n    }\r\n} \r\n\r\n.input-group:not(.has-validation)>:not(:last-child):not(.dropdown-toggle):not(.dropdown-menu), .input-group:not(.has-validation)>.dropdown-toggle:nth-last-child(n+3) \r\n    {\r\n        border-radius: 0 5px 5px 0;\r\n    }\r\n\r\n.input-group>:not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) \r\n    {\r\n        margin-right: -1px;\r\n        margin-left: unset;\r\n        border-radius: 5px 0 0 5px;\r\n}\r\n\r\n.input-group.has-validation>:nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu), .input-group.has-validation>.dropdown-toggle:nth-last-child(n+4) \r\n    {\r\n        border-radius: 0 5px 5px 0;\r\n    }", ".form-check {\r\n    padding-left: unset;\r\n    padding-right: 1.5em;\r\n\r\n    .form-check-input {\r\n        float: right;\r\n        margin-left: unset;\r\n        margin-right: -1.5em;\r\n    }\r\n}\r\n.form-control {\r\n    direction: ltr;\r\n    text-align: right;\r\n}\r\n.form-switch {\r\n    padding-right: 2.5em;\r\n    .form-check-input {\r\n        margin-right: -2.5em;\r\n    }\r\n}\r\n.select2-container--default{\r\n    .select2-selection--multiple{\r\n        .select2-selection__choice {\r\n            float: right;\r\n        }\r\n    } \r\n} \r\n.select2-container{\r\n    .select2-search--inline {\r\n        float: right;\r\n    }\r\n} \r\n.form-check-inline {\r\n    margin-left: 1rem;\r\n    margin-right: unset;\r\n}\r\n.form-switch.form-switch.form-check-inline {\r\n    padding-right: 2.5em;\r\n    padding-left: unset;\r\n}\r\n.form-check.form-switch.form-check-inline {\r\n    .form-check-input{\r\n        margin-right: 0.5em;\r\n        margin-left: 0.5em;\r\n    }\r\n}\r\n.iq-comingsoon-form {\r\n    button {\r\n        left: 0px;\r\n        right: unset;\r\n    }\r\n} ", ".dropdown-item {\r\n    text-align: right;\r\n}\r\n.dropdown-toggle {\r\n    &::after {\r\n        margin-left: unset;\r\n        margin-right: .255em;\r\n    }\r\n}\r\n.dropdown-menu-end[data-bs-popper] {\r\n\tright: auto;\r\n\tleft: 0;\r\n}\r\n", ".iq-timeline0 {    \r\n    &::before {\r\n        left: auto;\r\n        right: 20px;\r\n    }\r\n    ul {\r\n        li {\r\n            &:nth-child(odd) {\r\n                float: right;\r\n                text-align: right;\r\n                padding: 0 60px 0 0;\r\n                .timeline-dots {\r\n                    right: 12px;\r\n                    left: auto;\r\n                }\r\n                .timeline-dots.timeline-dot1 {\r\n                    right: 12px;\r\n                    left: auto;\r\n                } \r\n            }\r\n            &:nth-child(even) {\r\n                float: right;\r\n                text-align: right;\r\n                padding: 0 60px 0 0;\r\n                .timeline-dots {\r\n                    right: 12px;\r\n                    left: auto;\r\n                }\r\n                .timeline-dots.timeline-dot1 {\r\n                    right: 12px;\r\n                    left: auto;\r\n                }\r\n            }\r\n            .timeline-dots1 {\r\n                left: auto;\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}", ".sign-bg {\r\n\tright: 0;\r\n\tleft: 0;\r\n\r\n\t&.sign-bg-right {\r\n\t\tleft: 0;\r\n\t\tright: auto;\r\n\t}\r\n}", ".card {\r\n\t.card-body {\r\n\t\t.iq-media-group-1 {\r\n\t\t\t.iq-media-1 {\r\n\t\t\t\t&:first-child {\r\n                    margin-left: -1.25rem;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", ".iq-single-card {\r\n\tborder-radius: .5rem 0 0 .5rem;\r\n}"]}