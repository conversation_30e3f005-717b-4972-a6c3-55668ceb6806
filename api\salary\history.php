<?php
require_once '../../config/config.php';

header('Content-Type: application/json');

$auth = new Auth();
if (!$auth->isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ']);
    exit;
}

requirePermission('salary.view');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'วิธีการเรียกใช้ไม่ได้รับอนุญาต']);
    exit;
}

try {
    $employeeId = $_GET['employee_id'] ?? null;
    if (!$employeeId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ต้องระบุรหัสพนักงาน']);
        exit;
    }
    
    $salaryModel = new Salary();
    $history = $salaryModel->getSalaryHistory($employeeId);
    
    echo json_encode([
        'success' => true,
        'data' => $history
    ]);
    
} catch (Exception $e) {
    error_log("Salary history API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์']);
}
?>
