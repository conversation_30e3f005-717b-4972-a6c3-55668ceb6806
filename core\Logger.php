<?php
/**
 * Logger Class
 * Human Resources Center System
 */

class Logger {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Log an event
     */
    public function log($type, $userId = null, $action = null, $description = null, $data = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "INSERT INTO system_logs (log_type, user_id, ip_address, user_agent, action, description, data) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                $type,
                $userId,
                getUserIP(),
                getUserAgent(),
                $action,
                $description,
                $data ? json_encode($data) : null
            ]);
            
            // Also write to file
            $this->writeToFile($type, $userId, $action, $description, $data);
            
        } catch (Exception $e) {
            error_log("Logger error: " . $e->getMessage());
        }
    }
    
    /**
     * Log access event
     */
    public function logAccess($userId, $action, $description = null, $data = null) {
        $this->log(LOG_ACCESS, $userId, $action, $description, $data);
    }
    
    /**
     * Log activity event
     */
    public function logActivity($userId, $action, $description = null, $data = null) {
        $this->log(LOG_ACTIVITY, $userId, $action, $description, $data);
    }
    
    /**
     * Log error event
     */
    public function logError($userId, $action, $description = null, $data = null) {
        $this->log(LOG_ERROR, $userId, $action, $description, $data);
    }
    
    /**
     * Log security event
     */
    public function logSecurity($userId, $action, $description = null, $data = null) {
        $this->log(LOG_SECURITY, $userId, $action, $description, $data);
    }
    
    /**
     * Write log to file
     */
    private function writeToFile($type, $userId, $action, $description, $data) {
        try {
            $logDir = LOG_PATH . $type . '/';
            $logFile = $logDir . date('Y-m-d') . '.log';

            // Check if we can write to the log directory
            if (!is_dir($logDir)) {
                if (!@mkdir($logDir, 0755, true)) {
                    // If we can't create the directory, just skip file logging
                    return;
                }
            }

            // Check if directory is writable
            if (!is_writable($logDir)) {
                // If directory is not writable, skip file logging
                return;
            }

            $logEntry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'type' => $type,
                'user_id' => $userId,
                'ip_address' => getUserIP(),
                'user_agent' => getUserAgent(),
                'action' => $action,
                'description' => $description,
                'data' => $data
            ];

            $logLine = json_encode($logEntry) . PHP_EOL;

            // Try to write to file, suppress errors
            @file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);

        } catch (Exception $e) {
            // If file logging fails, just continue without it
            // Don't throw errors as this would break the main application
            error_log("File logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * Get logs from database
     */
    public function getLogs($type = null, $userId = null, $limit = 100, $offset = 0) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT * FROM system_logs WHERE 1=1";
            $params = [];
            
            if ($type) {
                $sql .= " AND log_type = ?";
                $params[] = $type;
            }
            
            if ($userId) {
                $sql .= " AND user_id = ?";
                $params[] = $userId;
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get logs error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count logs
     */
    public function countLogs($type = null, $userId = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT COUNT(*) FROM system_logs WHERE 1=1";
            $params = [];
            
            if ($type) {
                $sql .= " AND log_type = ?";
                $params[] = $type;
            }
            
            if ($userId) {
                $sql .= " AND user_id = ?";
                $params[] = $userId;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log("Count logs error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Archive old logs
     */
    public function archiveLogs($daysOld = 30) {
        try {
            $pdo = $this->db->getConnection();
            
            // Get logs older than specified days
            $sql = "SELECT * FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$daysOld]);
            $oldLogs = $stmt->fetchAll();
            
            if (!empty($oldLogs)) {
                // Create archive file
                $archiveDir = LOG_PATH . 'archive/';
                if (!is_dir($archiveDir)) {
                    @mkdir($archiveDir, 0755, true);
                }

                // Only create archive file if directory is writable
                if (is_writable($archiveDir)) {
                    $archiveFile = $archiveDir . 'logs_' . date('Y-m-d_H-i-s') . '.json';
                    @file_put_contents($archiveFile, json_encode($oldLogs, JSON_PRETTY_PRINT));
                }
                
                // Delete old logs from database
                $sql = "DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$daysOld]);
                
                $this->logActivity(null, 'archive_logs', "Archived {$stmt->rowCount()} old log entries");
                
                return $stmt->rowCount();
            }
            
            return 0;
            
        } catch (Exception $e) {
            error_log("Archive logs error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clean up old log files
     */
    public function cleanupLogFiles($daysOld = 30) {
        $logTypes = [LOG_ACCESS, LOG_ACTIVITY, LOG_ERROR, LOG_SECURITY];
        $cleanedFiles = 0;
        
        foreach ($logTypes as $type) {
            $logDir = LOG_PATH . $type . '/';
            
            if (is_dir($logDir)) {
                $files = glob($logDir . '*.log');
                
                foreach ($files as $file) {
                    $fileTime = filemtime($file);
                    $cutoffTime = time() - ($daysOld * 24 * 60 * 60);
                    
                    if ($fileTime < $cutoffTime) {
                        // Archive the file before deleting
                        $archiveDir = LOG_PATH . 'archive/' . $type . '/';
                        if (!is_dir($archiveDir)) {
                            @mkdir($archiveDir, 0755, true);
                        }

                        // Only archive if directory is writable
                        if (is_writable($archiveDir)) {
                            $archiveFile = $archiveDir . basename($file);
                            if (@copy($file, $archiveFile)) {
                                @unlink($file);
                                $cleanedFiles++;
                            }
                        } else {
                            // If can't archive, just delete the file
                            @unlink($file);
                            $cleanedFiles++;
                        }
                    }
                }
            }
        }
        
        if ($cleanedFiles > 0) {
            $this->logActivity(null, 'cleanup_log_files', "Cleaned up {$cleanedFiles} old log files");
        }
        
        return $cleanedFiles;
    }
    
    /**
     * Get log statistics
     */
    public function getLogStats($days = 7) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT 
                        log_type,
                        COUNT(*) as count,
                        DATE(created_at) as log_date
                    FROM system_logs 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                    GROUP BY log_type, DATE(created_at)
                    ORDER BY log_date DESC, log_type";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$days]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get log stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Search logs
     */
    public function searchLogs($query, $type = null, $dateFrom = null, $dateTo = null, $limit = 100) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT * FROM system_logs WHERE 
                    (action LIKE ? OR description LIKE ? OR data LIKE ?)";
            $params = ["%{$query}%", "%{$query}%", "%{$query}%"];
            
            if ($type) {
                $sql .= " AND log_type = ?";
                $params[] = $type;
            }
            
            if ($dateFrom) {
                $sql .= " AND created_at >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $sql .= " AND created_at <= ?";
                $params[] = $dateTo;
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Search logs error: " . $e->getMessage());
            return [];
        }
    }
}
