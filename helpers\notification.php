<?php
/**
 * Notification Helper Functions
 * Human Resources Center System
 */

/**
 * Send LINE notification
 */
function sendLineNotification($userId, $message) {
    try {
        $accessToken = LINE_CHANNEL_ACCESS_TOKEN;
        
        $data = [
            'to' => $userId,
            'messages' => [
                [
                    'type' => 'text',
                    'text' => $message
                ]
            ]
        ];
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $accessToken
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.line.me/v2/bot/message/push');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return ['success' => true, 'message' => 'LINE notification sent successfully'];
        } else {
            error_log("LINE notification failed: " . $response);
            return ['success' => false, 'message' => 'Failed to send LINE notification'];
        }
        
    } catch (Exception $e) {
        error_log("LINE notification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'LINE notification error: ' . $e->getMessage()];
    }
}

/**
 * Send email notification
 */
function sendEmailNotification($to, $subject, $message, $isHtml = false) {
    try {
        require_once __DIR__ . '/../vendor/autoload.php'; // PHPMailer
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        $mail->CharSet = 'UTF-8';
        
        // Recipients
        $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML($isHtml);
        $mail->Subject = $subject;
        $mail->Body = $message;
        
        $mail->send();
        return ['success' => true, 'message' => 'Email sent successfully'];
        
    } catch (Exception $e) {
        error_log("Email error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to send email: ' . $e->getMessage()];
    }
}

/**
 * Send leave request notification
 */
function sendLeaveRequestNotification($leaveRequestId) {
    try {
        $leaveModel = new LeaveRequest();
        $employeeModel = new Employee();
        
        $leaveRequest = $leaveModel->getById($leaveRequestId);
        if (!$leaveRequest) {
            return ['success' => false, 'message' => 'Leave request not found'];
        }
        
        $employee = $employeeModel->getById($leaveRequest['employee_id']);
        if (!$employee) {
            return ['success' => false, 'message' => 'Employee not found'];
        }
        
        // Prepare notification message
        $message = "🏖️ Leave Request Notification\n\n";
        $message .= "Employee: {$employee['first_name']} {$employee['last_name']}\n";
        $message .= "Employee Code: {$employee['employee_code']}\n";
        $message .= "Leave Type: {$leaveRequest['leave_type_name']}\n";
        $message .= "Period: " . formatDate($leaveRequest['start_date']) . " - " . formatDate($leaveRequest['end_date']) . "\n";
        $message .= "Total Days: {$leaveRequest['total_days']} days\n";
        $message .= "Reason: {$leaveRequest['reason']}\n\n";
        $message .= "Please review and approve/reject this request.";
        
        // Get HR managers to notify
        $userModel = new User();
        $hrManagers = $userModel->getUsersByRole(ROLE_HR);
        
        $results = [];
        
        foreach ($hrManagers as $manager) {
            // Send LINE notification if LINE ID is available
            if (!empty($manager['line_id'])) {
                $lineResult = sendLineNotification($manager['line_id'], $message);
                $results[] = $lineResult;
            }
            
            // Send email notification
            $emailSubject = "Leave Request - {$employee['first_name']} {$employee['last_name']}";
            $emailMessage = nl2br($message);
            $emailResult = sendEmailNotification($manager['email'], $emailSubject, $emailMessage, true);
            $results[] = $emailResult;
        }
        
        // Log notification
        $logger = new Logger();
        $logger->logActivity(
            getCurrentUserId(),
            'leave_notification_sent',
            "Leave request notification sent for {$employee['first_name']} {$employee['last_name']}",
            ['leave_request_id' => $leaveRequestId, 'results' => $results]
        );
        
        return ['success' => true, 'message' => 'Notifications sent', 'results' => $results];
        
    } catch (Exception $e) {
        error_log("Leave notification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to send notifications: ' . $e->getMessage()];
    }
}

/**
 * Send leave approval notification
 */
function sendLeaveApprovalNotification($leaveRequestId, $status, $comments = null) {
    try {
        $leaveModel = new LeaveRequest();
        $employeeModel = new Employee();
        
        $leaveRequest = $leaveModel->getById($leaveRequestId);
        if (!$leaveRequest) {
            return ['success' => false, 'message' => 'Leave request not found'];
        }
        
        $employee = $employeeModel->getById($leaveRequest['employee_id']);
        if (!$employee) {
            return ['success' => false, 'message' => 'Employee not found'];
        }
        
        // Prepare notification message
        $statusIcon = $status === 'approved' ? '✅' : '❌';
        $statusText = ucfirst($status);
        
        $message = "{$statusIcon} Leave Request {$statusText}\n\n";
        $message .= "Your leave request has been {$status}.\n\n";
        $message .= "Leave Type: {$leaveRequest['leave_type_name']}\n";
        $message .= "Period: " . formatDate($leaveRequest['start_date']) . " - " . formatDate($leaveRequest['end_date']) . "\n";
        $message .= "Total Days: {$leaveRequest['total_days']} days\n";
        
        if ($comments) {
            $message .= "Comments: {$comments}\n";
        }
        
        $message .= "\nThank you.";
        
        $results = [];
        
        // Send LINE notification if LINE ID is available
        if (!empty($employee['line_id'])) {
            $lineResult = sendLineNotification($employee['line_id'], $message);
            $results[] = $lineResult;
        }
        
        // Send email notification
        $emailSubject = "Leave Request {$statusText} - {$leaveRequest['leave_type_name']}";
        $emailMessage = nl2br($message);
        
        if (!empty($employee['email'])) {
            $emailResult = sendEmailNotification($employee['email'], $emailSubject, $emailMessage, true);
            $results[] = $emailResult;
        }
        
        // Log notification
        $logger = new Logger();
        $logger->logActivity(
            getCurrentUserId(),
            'leave_approval_notification_sent',
            "Leave {$status} notification sent to {$employee['first_name']} {$employee['last_name']}",
            ['leave_request_id' => $leaveRequestId, 'status' => $status, 'results' => $results]
        );
        
        return ['success' => true, 'message' => 'Notifications sent', 'results' => $results];
        
    } catch (Exception $e) {
        error_log("Leave approval notification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to send notifications: ' . $e->getMessage()];
    }
}

/**
 * Send payslip notification
 */
function sendPayslipNotification($employeeId, $payrollId) {
    try {
        $employeeModel = new Employee();
        $payrollModel = new Payroll();
        
        $employee = $employeeModel->getById($employeeId);
        if (!$employee) {
            return ['success' => false, 'message' => 'Employee not found'];
        }
        
        $payroll = $payrollModel->getById($payrollId);
        if (!$payroll) {
            return ['success' => false, 'message' => 'Payroll not found'];
        }
        
        // Prepare notification message
        $message = "💰 Payslip Available\n\n";
        $message .= "Your payslip for " . formatDate($payroll['pay_period_start']) . " - " . formatDate($payroll['pay_period_end']) . " is now available.\n\n";
        $message .= "Net Pay: " . formatCurrency($payroll['net_pay']) . "\n";
        $message .= "Pay Date: " . formatDate($payroll['pay_date']) . "\n\n";
        $message .= "Please check your HR system for detailed payslip.";
        
        $results = [];
        
        // Send LINE notification if LINE ID is available
        if (!empty($employee['line_id'])) {
            $lineResult = sendLineNotification($employee['line_id'], $message);
            $results[] = $lineResult;
        }
        
        // Send email notification
        $emailSubject = "Payslip Available - " . formatDate($payroll['pay_period_start']) . " to " . formatDate($payroll['pay_period_end']);
        $emailMessage = nl2br($message);
        
        if (!empty($employee['email'])) {
            $emailResult = sendEmailNotification($employee['email'], $emailSubject, $emailMessage, true);
            $results[] = $emailResult;
        }
        
        // Log notification
        $logger = new Logger();
        $logger->logActivity(
            getCurrentUserId(),
            'payslip_notification_sent',
            "Payslip notification sent to {$employee['first_name']} {$employee['last_name']}",
            ['employee_id' => $employeeId, 'payroll_id' => $payrollId, 'results' => $results]
        );
        
        return ['success' => true, 'message' => 'Notifications sent', 'results' => $results];
        
    } catch (Exception $e) {
        error_log("Payslip notification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to send notifications: ' . $e->getMessage()];
    }
}

/**
 * Send birthday notification
 */
function sendBirthdayNotification($employeeId) {
    try {
        $employeeModel = new Employee();
        $employee = $employeeModel->getById($employeeId);
        
        if (!$employee) {
            return ['success' => false, 'message' => 'Employee not found'];
        }
        
        // Prepare notification message
        $message = "🎉 Happy Birthday!\n\n";
        $message .= "Dear {$employee['first_name']},\n\n";
        $message .= "Wishing you a very happy birthday! 🎂\n";
        $message .= "May this new year of your life bring you happiness, success, and prosperity.\n\n";
        $message .= "Best wishes from the HR Team";
        
        $results = [];
        
        // Send LINE notification if LINE ID is available
        if (!empty($employee['line_id'])) {
            $lineResult = sendLineNotification($employee['line_id'], $message);
            $results[] = $lineResult;
        }
        
        // Send email notification
        $emailSubject = "Happy Birthday, {$employee['first_name']}! 🎉";
        $emailMessage = nl2br($message);
        
        if (!empty($employee['email'])) {
            $emailResult = sendEmailNotification($employee['email'], $emailSubject, $emailMessage, true);
            $results[] = $emailResult;
        }
        
        // Log notification
        $logger = new Logger();
        $logger->logActivity(
            null,
            'birthday_notification_sent',
            "Birthday notification sent to {$employee['first_name']} {$employee['last_name']}",
            ['employee_id' => $employeeId, 'results' => $results]
        );
        
        return ['success' => true, 'message' => 'Birthday notifications sent', 'results' => $results];
        
    } catch (Exception $e) {
        error_log("Birthday notification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to send birthday notifications: ' . $e->getMessage()];
    }
}
