<?php
/**
 * Leave Controller
 * Human Resources Center System
 */

class LeaveController {
    private $leaveModel;
    private $employeeModel;
    private $logger;
    
    public function __construct() {
        $this->leaveModel = new LeaveRequest();
        $this->employeeModel = new Employee();
        $this->logger = new Logger();
        
        // Require authentication
        requireLogin();
    }
    
    /**
     * List leave requests
     */
    public function index() {
        requirePermission('leave.view');
        
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? null;
        $status = $_GET['status'] ?? null;
        $employeeId = $_GET['employee_id'] ?? null;
        $dateFrom = $_GET['date_from'] ?? null;
        $dateTo = $_GET['date_to'] ?? null;
        
        $limit = RECORDS_PER_PAGE;
        $offset = ($page - 1) * $limit;
        
        $leaveRequests = $this->leaveModel->getAll($limit, $offset, $employeeId, $status, $dateFrom, $dateTo);
        $totalRecords = $this->leaveModel->count($employeeId, $status, $dateFrom, $dateTo);
        $pagination = paginate($totalRecords, $page, $limit);
        
        // Get employees for filter
        $employees = $this->employeeModel->getAll();
        
        // Get leave types
        $leaveTypes = $this->leaveModel->getLeaveTypes();
        
        include __DIR__ . '/../views/admin/leaves/index.php';
    }
    
    /**
     * Show leave request details
     */
    public function show() {
        requirePermission('leave.view');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            setFlashMessage('error', 'Leave request ID is required');
            redirect(BASE_URL . '/admin/leaves/index.php');
        }
        
        $leaveRequest = $this->leaveModel->getById($id);
        if (!$leaveRequest) {
            setFlashMessage('error', 'Leave request not found');
            redirect(BASE_URL . '/admin/leaves/index.php');
        }
        
        include __DIR__ . '/../views/admin/leaves/show.php';
    }
    
    /**
     * Approve leave request
     */
    public function approve() {
        requirePermission('leave.approve');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $id = $_POST['id'] ?? null;
        $comments = $_POST['comments'] ?? null;
        
        if (!$id) {
            errorResponse('Leave request ID is required', 400);
        }
        
        $result = $this->leaveModel->approve($id, getCurrentUserId(), $comments);
        
        if ($result['success']) {
            // Send notification
            require_once __DIR__ . '/../helpers/notification.php';
            sendLeaveApprovalNotification($id, 'approved', $comments);
            
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Reject leave request
     */
    public function reject() {
        requirePermission('leave.approve');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $id = $_POST['id'] ?? null;
        $reason = $_POST['reason'] ?? null;
        
        if (!$id) {
            errorResponse('Leave request ID is required', 400);
        }
        
        if (empty($reason)) {
            errorResponse('Rejection reason is required', 400);
        }
        
        $result = $this->leaveModel->reject($id, getCurrentUserId(), $reason);
        
        if ($result['success']) {
            // Send notification
            require_once __DIR__ . '/../helpers/notification.php';
            sendLeaveApprovalNotification($id, 'rejected', $reason);
            
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Cancel leave request
     */
    public function cancel() {
        requirePermission('leave.cancel');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $id = $_POST['id'] ?? null;
        
        if (!$id) {
            errorResponse('Leave request ID is required', 400);
        }
        
        $result = $this->leaveModel->cancel($id);
        
        if ($result['success']) {
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Create leave request (for admin)
     */
    public function create() {
        requirePermission('leave.create');
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->store();
        }
        
        $employees = $this->employeeModel->getAll();
        $leaveTypes = $this->leaveModel->getLeaveTypes();
        
        include __DIR__ . '/../views/admin/leaves/create.php';
    }
    
    /**
     * Store new leave request
     */
    public function store() {
        requirePermission('leave.create');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        // Validate input
        $validator = new Validator($_POST);
        $validator->required('employee_id', 'Employee is required')
                 ->required('leave_type_id', 'Leave type is required')
                 ->required('start_date', 'Start date is required')
                 ->required('end_date', 'End date is required')
                 ->date('start_date', 'Y-m-d', 'Invalid start date format')
                 ->date('end_date', 'Y-m-d', 'Invalid end date format');
        
        if ($validator->fails()) {
            if (isset($_POST['ajax'])) {
                errorResponse('Validation failed', 400);
            }
            
            setFlashMessage('error', 'Please correct the errors below');
            $errors = $validator->getErrors();
            $employees = $this->employeeModel->getAll();
            $leaveTypes = $this->leaveModel->getLeaveTypes();
            include __DIR__ . '/../views/admin/leaves/create.php';
            return;
        }
        
        // Validate dates
        if (strtotime($_POST['start_date']) > strtotime($_POST['end_date'])) {
            if (isset($_POST['ajax'])) {
                errorResponse('Start date cannot be after end date', 400);
            }
            setFlashMessage('error', 'Start date cannot be after end date');
            $employees = $this->employeeModel->getAll();
            $leaveTypes = $this->leaveModel->getLeaveTypes();
            include __DIR__ . '/../views/admin/leaves/create.php';
            return;
        }
        
        // Prepare data
        $data = [
            'employee_id' => $_POST['employee_id'],
            'leave_type_id' => $_POST['leave_type_id'],
            'start_date' => $_POST['start_date'],
            'end_date' => $_POST['end_date'],
            'reason' => $_POST['reason'] ?? null
        ];
        
        $result = $this->leaveModel->create($data);
        
        if (isset($_POST['ajax'])) {
            if ($result['success']) {
                // Send notification
                require_once __DIR__ . '/../helpers/notification.php';
                sendLeaveRequestNotification($result['id']);
                
                successResponse($result['message'], ['id' => $result['id']]);
            } else {
                errorResponse($result['message'], 400);
            }
        }
        
        if ($result['success']) {
            // Send notification
            require_once __DIR__ . '/../helpers/notification.php';
            sendLeaveRequestNotification($result['id']);
            
            setFlashMessage('success', $result['message']);
            redirect(BASE_URL . '/admin/leaves/index.php');
        } else {
            setFlashMessage('error', $result['message']);
            $employees = $this->employeeModel->getAll();
            $leaveTypes = $this->leaveModel->getLeaveTypes();
            include __DIR__ . '/../views/admin/leaves/create.php';
        }
    }
    
    /**
     * Get leave request data for AJAX
     */
    public function getData() {
        requirePermission('leave.view');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            errorResponse('Leave request ID is required', 400);
        }
        
        $leaveRequest = $this->leaveModel->getById($id);
        if (!$leaveRequest) {
            errorResponse('Leave request not found', 404);
        }
        
        successResponse('Leave request data retrieved', $leaveRequest);
    }
    
    /**
     * Get leave statistics
     */
    public function getStats() {
        requirePermission('leave.view');
        
        try {
            $pdo = $this->leaveModel->getConnection();
            
            // Get leave statistics
            $stats = [
                'pending' => $this->leaveModel->count(null, 'pending'),
                'approved' => $this->leaveModel->count(null, 'approved'),
                'rejected' => $this->leaveModel->count(null, 'rejected'),
                'cancelled' => $this->leaveModel->count(null, 'cancelled')
            ];
            
            successResponse('Leave statistics retrieved', $stats);
            
        } catch (Exception $e) {
            error_log("Get leave stats error: " . $e->getMessage());
            errorResponse('Failed to get leave statistics', 500);
        }
    }
}
