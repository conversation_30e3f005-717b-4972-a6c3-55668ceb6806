<?php
require_once __DIR__ . '/../../../config/config.php';

$auth = new Auth();

// Require authentication
if (!$auth->isAuthenticated()) {
    redirect(BASE_URL . '/login.php');
}

// Initialize controller and get data
$benefitController = new BenefitController();

// Get page data
$page = $_GET['page'] ?? 1;
$search = $_GET['search'] ?? null;
$type = $_GET['type'] ?? null;

$limit = RECORDS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get benefits
$benefitModel = new Benefit();
$benefits = $benefitModel->getAll($limit, $offset, $search, $type);
$totalRecords = $benefitModel->count($search, $type);
$pagination = paginate($totalRecords, $page, $limit);

// Get benefit types for filter
$benefitTypes = $benefitModel->getBenefitTypes();

// Get statistics
$stats = [
    'total_benefits' => $benefitModel->count(),
    'active_benefits' => $benefitModel->getActiveBenefitsCount(),
    'total_value' => $benefitModel->getTotalBenefitValue(),
    'monthly_cost' => $benefitModel->getMonthlyBenefitCost()
];

$currentUser = $auth->getCurrentUser();
$pageTitle = 'จัดการสวัสดิการ';
include __DIR__ . '/../../layouts/header.php';
?>

<div class="conatiner-fluid content-inner mt-n5 py-0">
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">สวัสดิการทั้งหมด</span>
                            <h4 class="mb-0"><?= number_format($stats['total_benefits']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-primary-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">สวัสดิการที่ใช้งาน</span>
                            <h4 class="mb-0"><?= number_format($stats['active_benefits']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-success-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11.3194 14.3209C11.1261 14.3219 10.9328 14.2523 10.7838 14.1091L8.86695 12.2656C8.57097 11.9793 8.56795 11.5145 8.86091 11.2262C9.15387 10.9369 9.63207 10.934 9.92906 11.2193L11.3083 12.5451L14.6758 9.22479C14.9698 8.93552 15.448 8.93258 15.744 9.21793C16.041 9.50426 16.044 9.97004 15.751 10.2574L11.8519 14.1022C11.7049 14.2474 11.5127 14.3199 11.3194 14.3209Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">มูลค่ารวม</span>
                            <h4 class="mb-0"><?= formatCurrency($stats['total_value']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-info-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M12.0865 22C11.9627 22 11.8388 21.9716 11.7271 21.9137L8.12599 20.0496C7.10415 19.5201 6.30481 18.9259 5.68063 18.2336C4.31449 16.7195 3.5544 14.776 3.54232 12.7599L3.50004 6.12426C3.495 5.35842 3.98931 4.67103 4.72826 4.41215L11.3405 2.10679C11.7331 1.96656 12.1711 1.9646 12.5707 2.09992L19.2081 4.32684C19.9511 4.57493 20.4535 5.25742 20.4575 6.02228L20.4998 12.6628C20.5129 14.676 19.779 16.6274 18.434 18.1581C17.8168 18.8602 17.0245 19.4632 16.0128 20.0025L12.4439 21.9088C12.3331 21.9686 12.2103 21.999 12.0865 22Z" fill="currentColor"></path>
                                <path d="M11.3194 14.3209C11.1261 14.3219 10.9328 14.2523 10.7838 14.1091L8.86695 12.2656C8.57097 11.9793 8.56795 11.5145 8.86091 11.2262C9.15387 10.9369 9.63207 10.934 9.92906 11.2193L11.3083 12.5451L14.6758 9.22479C14.9698 8.93552 15.448 8.93258 15.744 9.21793C16.041 9.50426 16.044 9.97004 15.751 10.2574L11.8519 14.1022C11.7049 14.2474 11.5127 14.3199 11.3194 14.3209Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">ค่าใช้จ่ายรายเดือน</span>
                            <h4 class="mb-0"><?= formatCurrency($stats['monthly_cost']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-warning-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.7688 8.71387H16.2312C18.5886 8.71387 20.5 10.5831 20.5 12.8885V17.8254C20.5 20.1308 18.5886 22 16.2312 22H7.7688C5.41136 22 3.5 20.1308 3.5 17.8254V12.8885C3.5 10.5831 5.41136 8.71387 7.7688 8.71387Z" fill="currentColor"></path>
                                <path opacity="0.4" d="M17.523 7.39595V8.86667C17.1673 8.7673 16.7913 8.71761 16.4052 8.71761H15.7447V7.39595C15.7447 5.37868 14.0681 3.73903 12.0053 3.73903C9.94257 3.73903 8.26594 5.36874 8.25578 7.37608V8.71761H7.60545C7.20916 8.71761 6.83319 8.7673 6.47754 8.87661V7.39595C6.4877 4.41476 8.95692 2 11.985 2C15.0537 2 17.523 4.41476 17.523 7.39595Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">จัดการสวัสดิการ</h4>
                    </div>
                    <div class="header-action">
                        <?php if (hasPermission('benefits.create')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#benefitModal">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path opacity="0.4" d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2Z" fill="currentColor"></path>
                            </svg>
                            เพิ่มสวัสดิการ
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search benefits..." 
                                       value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select class="form-select" id="typeFilter">
                                    <option value="">All Types</option>
                                    <?php foreach ($benefitTypes as $type): ?>
                                    <option value="<?= $type ?>" <?= ($_GET['type'] ?? '') === $type ? 'selected' : '' ?>>
                                        <?= ucfirst($type) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="1" <?= ($_GET['status'] ?? '') === '1' ? 'selected' : '' ?>>Active</option>
                                    <option value="0" <?= ($_GET['status'] ?? '') === '0' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-primary w-100" onclick="applyFilters()">
                                <svg width="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                    <circle cx="11" cy="11" r="8" stroke="currentcolor" stroke-width="1.5"></circle>
                                    <path d="21 21l-4.35-4.35" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                Filter
                            </button>
                        </div>
                    </div>

                    <!-- Benefits Table -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="benefitsTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Description</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($benefits as $benefit): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($benefit['name']) ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $benefit['benefit_type'] === 'health' ? 'info' : ($benefit['benefit_type'] === 'insurance' ? 'warning' : 'primary') ?>">
                                            <?= ucfirst($benefit['benefit_type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($benefit['description']): ?>
                                        <span data-bs-toggle="tooltip" title="<?= htmlspecialchars($benefit['description']) ?>">
                                            <?= htmlspecialchars(substr($benefit['description'], 0, 50)) ?><?= strlen($benefit['description']) > 50 ? '...' : '' ?>
                                        </span>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($benefit['amount'] > 0): ?>
                                        <?= formatCurrency($benefit['amount']) ?>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $benefit['is_active'] ? 'success' : 'secondary' ?>">
                                            <?= $benefit['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td><?= formatDateTime($benefit['created_at'], 'd/m/Y') ?></td>
                                    <td>
                                        <div class="flex align-items-center list-user-action">
                                            <a class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="View" href="show.php?id=<?= $benefit['id'] ?>">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill="currentColor" d="M9.76 14.37L12 12.13L14.24 14.37L15.65 12.96L13.41 10.72L15.65 8.48L14.24 7.07L12 9.31L9.76 7.07L8.35 8.48L10.59 10.72L8.35 12.96L9.76 14.37Z"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            
                                            <?php if (hasPermission('benefits.edit')): ?>
                                            <a class="btn btn-sm btn-icon btn-warning" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Edit" href="#" onclick="editBenefit(<?= $benefit['id'] ?>)">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M11.4925 2.78906H7.75349C4.67849 2.78906 2.75049 4.96606 2.75049 8.04806V16.3621C2.75049 19.4441 4.66949 21.6211 7.75349 21.6211H16.5775C19.6625 21.6211 21.5815 19.4441 21.5815 16.3621V12.3341" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.82812 10.921L16.3011 3.44799C17.2321 2.51799 18.7411 2.51799 19.6721 3.44799L20.8891 4.66499C21.8201 5.59599 21.8201 7.10599 20.8891 8.03599L13.3801 15.545C12.9731 15.952 12.4211 16.181 11.8451 16.181H8.09912L8.19312 12.401C8.20712 11.845 8.43412 11.315 8.82812 10.921Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path d="M15.1655 4.60254L19.7315 9.16854" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (hasPermission('benefits.delete')): ?>
                                            <a class="btn btn-sm btn-icon btn-danger" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Delete" href="#" onclick="deleteBenefit(<?= $benefit['id'] ?>, '<?= htmlspecialchars($benefit['name']) ?>')">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor">
                                                        <polyline points="3,6 5,6 21,6"></polyline>
                                                        <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                                                        <line x1="10" y1="11" x2="10" y2="17"></line>
                                                        <line x1="14" y1="11" x2="14" y2="17"></line>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['previous_page'] ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&type=<?= urlencode($_GET['type'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>">Previous</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                            <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&type=<?= urlencode($_GET['type'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>"><?= $i ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['next_page'] ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&type=<?= urlencode($_GET['type'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>">Next</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Benefit Modal -->
<div class="modal fade" id="benefitModal" tabindex="-1" aria-labelledby="benefitModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="benefitModalLabel">เพิ่มสวัสดิการ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="benefitForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                    <input type="hidden" name="benefit_id" id="benefit_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="name" class="form-label">Benefit Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="type" class="form-label">Benefit Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="health">Health</option>
                                    <option value="insurance">Insurance</option>
                                    <option value="allowance">Allowance</option>
                                    <option value="bonus">Bonus</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="amount" class="form-label">Amount</label>
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Status</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="Enter benefit description"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Benefit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additionalScripts = '
<script>
    // Apply filters
    function applyFilters() {
        const search = document.getElementById("searchInput").value;
        const type = document.getElementById("typeFilter").value;
        const status = document.getElementById("statusFilter").value;
        
        const params = new URLSearchParams();
        if (search) params.append("search", search);
        if (type) params.append("type", type);
        if (status) params.append("status", status);
        
        window.location.href = "?" + params.toString();
    }

    // Edit benefit
    function editBenefit(id) {
        showLoading("กำลังโหลดข้อมูลสวัสดิการ...");
        
        fetch(`${BASE_URL}/api/benefits.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                const benefit = data.data;
                document.getElementById("benefit_id").value = benefit.id;
                document.getElementById("name").value = benefit.name;
                document.getElementById("type").value = benefit.benefit_type;
                document.getElementById("amount").value = benefit.amount;
                document.getElementById("description").value = benefit.description || "";
                document.getElementById("is_active").checked = benefit.is_active == 1;
                
                document.getElementById("benefitModalLabel").textContent = "แก้ไขสวัสดิการ";
                bootstrap.Modal.getInstance(document.getElementById("benefitModal")) || new bootstrap.Modal(document.getElementById("benefitModal")).show();
            } else {
                showAlert("error", data.message);
            }
        })
        .catch(error => {
            hideLoading();
            showAlert("error", "ไม่สามารถโหลดข้อมูลสวัสดิการได้");
        });
    }

    // Delete benefit
    function deleteBenefit(id, name) {
        showConfirm(`คุณแน่ใจหรือไม่ที่จะลบสวัสดิการ "${name}"?`, function() {
            showLoading("กำลังลบสวัสดิการ...");
            
            fetch(`${BASE_URL}/api/benefits.php`, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": CSRF_TOKEN
                },
                body: JSON.stringify({ 
                    id: id,
                    csrf_token: CSRF_TOKEN 
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert("success", data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert("error", data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert("error", "ไม่สามารถลบสวัสดิการได้");
            });
        });
    }

    // Handle form submission
    document.getElementById("benefitForm").addEventListener("submit", function(e) {
        e.preventDefault();
        
        if (!validateForm("#benefitForm")) {
            showAlert("error", "กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วนและถูกต้อง");
            return;
        }
        
        const isEdit = document.getElementById("benefit_id").value !== "";
        showLoading(isEdit ? "กำลังอัปเดตสวัสดิการ..." : "กำลังสร้างสวัสดิการ...");
        
        const formData = new FormData(this);
        formData.append("ajax", "1");
        
        const url = `${BASE_URL}/api/benefits.php`;
        const method = isEdit ? "PUT" : "POST";
        
        fetch(url, {
            method: method,
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert("success", data.message);
                bootstrap.Modal.getInstance(document.getElementById("benefitModal")).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert("error", data.message);
            }
        })
        .catch(error => {
            hideLoading();
            showAlert("error", isEdit ? "ไม่สามารถอัปเดตสวัสดิการได้" : "ไม่สามารถสร้างสวัสดิการได้");
        });
    });

    // Reset form when modal is hidden
    document.getElementById("benefitModal").addEventListener("hidden.bs.modal", function() {
        document.getElementById("benefitForm").reset();
        document.getElementById("benefit_id").value = "";
        document.getElementById("benefitModalLabel").textContent = "เพิ่มสวัสดิการ";
    });

    // Search on Enter key
    document.getElementById("searchInput").addEventListener("keypress", function(e) {
        if (e.key === "Enter") {
            applyFilters();
        }
    });
</script>
';

include __DIR__ . '/../../layouts/footer.php';
?>
