<?php
/**
 * Leave Request Model
 * Human Resources Center System
 */

class LeaveRequest {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new Database();
        $this->logger = new Logger();
    }
    
    /**
     * Get all leave requests
     */
    public function getAll($limit = null, $offset = 0, $employeeId = null, $status = null, $dateFrom = null, $dateTo = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT lr.*, 
                           e.first_name, e.last_name, e.employee_code,
                           lt.name as leave_type_name,
                           u.username as approved_by_username
                    FROM leave_requests lr
                    JOIN employees e ON lr.employee_id = e.id
                    JOIN leave_types lt ON lr.leave_type_id = lt.id
                    LEFT JOIN users u ON lr.approved_by = u.id
                    WHERE 1=1";
            $params = [];
            
            if ($employeeId) {
                $sql .= " AND lr.employee_id = ?";
                $params[] = $employeeId;
            }
            
            if ($status) {
                $sql .= " AND lr.status = ?";
                $params[] = $status;
            }
            
            if ($dateFrom) {
                $sql .= " AND lr.start_date >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $sql .= " AND lr.end_date <= ?";
                $params[] = $dateTo;
            }
            
            $sql .= " ORDER BY lr.created_at DESC";
            
            if ($limit) {
                $sql .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get leave requests error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'get_leave_requests_error', $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count leave requests
     */
    public function count($employeeId = null, $status = null, $dateFrom = null, $dateTo = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT COUNT(*) FROM leave_requests WHERE 1=1";
            $params = [];
            
            if ($employeeId) {
                $sql .= " AND employee_id = ?";
                $params[] = $employeeId;
            }
            
            if ($status) {
                $sql .= " AND status = ?";
                $params[] = $status;
            }
            
            if ($dateFrom) {
                $sql .= " AND start_date >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $sql .= " AND end_date <= ?";
                $params[] = $dateTo;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log("Count leave requests error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get leave request by ID
     */
    public function getById($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT lr.*, 
                           e.first_name, e.last_name, e.employee_code,
                           lt.name as leave_type_name,
                           u.username as approved_by_username
                    FROM leave_requests lr
                    JOIN employees e ON lr.employee_id = e.id
                    JOIN leave_types lt ON lr.leave_type_id = lt.id
                    LEFT JOIN users u ON lr.approved_by = u.id
                    WHERE lr.id = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get leave request by ID error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'get_leave_request_error', $e->getMessage(), ['leave_request_id' => $id]);
            return null;
        }
    }
    
    /**
     * Create new leave request
     */
    public function create($data) {
        try {
            $pdo = $this->db->getConnection();
            $pdo->beginTransaction();
            
            // Calculate total days
            $totalDays = $this->calculateLeaveDays($data['start_date'], $data['end_date']);
            
            // Check if employee has enough leave balance
            $leaveBalance = $this->getLeaveBalance($data['employee_id'], $data['leave_type_id']);
            if ($leaveBalance !== null && $totalDays > $leaveBalance) {
                $pdo->rollback();
                return ['success' => false, 'message' => 'Insufficient leave balance'];
            }
            
            // Check for overlapping leave requests
            if ($this->hasOverlappingLeave($data['employee_id'], $data['start_date'], $data['end_date'])) {
                $pdo->rollback();
                return ['success' => false, 'message' => 'Overlapping leave request exists'];
            }
            
            $sql = "INSERT INTO leave_requests (
                        employee_id, leave_type_id, start_date, end_date, 
                        total_days, reason, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['employee_id'],
                $data['leave_type_id'],
                $data['start_date'],
                $data['end_date'],
                $totalDays,
                $data['reason'] ?? null,
                'pending'
            ]);
            
            if ($result) {
                $leaveRequestId = $pdo->lastInsertId();
                $pdo->commit();
                
                // Get employee and leave type info for logging
                $employee = (new Employee())->getById($data['employee_id']);
                $leaveType = $this->getLeaveTypeById($data['leave_type_id']);
                
                $this->logger->logActivity(
                    getCurrentUserId(), 
                    'leave_request_created', 
                    "Leave request created for {$employee['first_name']} {$employee['last_name']} - {$leaveType['name']}", 
                    ['leave_request_id' => $leaveRequestId]
                );
                
                return ['success' => true, 'id' => $leaveRequestId, 'message' => 'Leave request created successfully'];
            }
            
            $pdo->rollback();
            return ['success' => false, 'message' => 'Failed to create leave request'];
            
        } catch (Exception $e) {
            $pdo->rollback();
            error_log("Create leave request error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'create_leave_request_error', $e->getMessage(), $data);
            return ['success' => false, 'message' => 'Failed to create leave request: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update leave request
     */
    public function update($id, $data) {
        try {
            $pdo = $this->db->getConnection();
            
            // Get current leave request
            $currentRequest = $this->getById($id);
            if (!$currentRequest) {
                return ['success' => false, 'message' => 'Leave request not found'];
            }
            
            // Only allow updates for pending requests
            if ($currentRequest['status'] !== 'pending') {
                return ['success' => false, 'message' => 'Cannot update non-pending leave request'];
            }
            
            // Calculate total days if dates are being updated
            if (isset($data['start_date']) || isset($data['end_date'])) {
                $startDate = $data['start_date'] ?? $currentRequest['start_date'];
                $endDate = $data['end_date'] ?? $currentRequest['end_date'];
                $data['total_days'] = $this->calculateLeaveDays($startDate, $endDate);
                
                // Check for overlapping leave requests (excluding current request)
                if ($this->hasOverlappingLeave($currentRequest['employee_id'], $startDate, $endDate, $id)) {
                    return ['success' => false, 'message' => 'Overlapping leave request exists'];
                }
            }
            
            $sql = "UPDATE leave_requests SET 
                        leave_type_id = COALESCE(?, leave_type_id),
                        start_date = COALESCE(?, start_date),
                        end_date = COALESCE(?, end_date),
                        total_days = COALESCE(?, total_days),
                        reason = COALESCE(?, reason),
                        updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['leave_type_id'] ?? null,
                $data['start_date'] ?? null,
                $data['end_date'] ?? null,
                $data['total_days'] ?? null,
                $data['reason'] ?? null,
                $id
            ]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(), 
                    'leave_request_updated', 
                    "Leave request updated for {$currentRequest['first_name']} {$currentRequest['last_name']}", 
                    ['leave_request_id' => $id]
                );
                
                return ['success' => true, 'message' => 'Leave request updated successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to update leave request'];
            
        } catch (Exception $e) {
            error_log("Update leave request error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'update_leave_request_error', $e->getMessage(), ['leave_request_id' => $id]);
            return ['success' => false, 'message' => 'Failed to update leave request: ' . $e->getMessage()];
        }
    }
    
    /**
     * Approve leave request
     */
    public function approve($id, $approvedBy, $comments = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE leave_requests SET 
                        status = 'approved',
                        approved_by = ?,
                        approved_at = NOW(),
                        updated_at = NOW()
                    WHERE id = ? AND status = 'pending'";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$approvedBy, $id]);
            
            if ($result && $stmt->rowCount() > 0) {
                $leaveRequest = $this->getById($id);
                
                $this->logger->logActivity(
                    $approvedBy, 
                    'leave_request_approved', 
                    "Leave request approved for {$leaveRequest['first_name']} {$leaveRequest['last_name']}", 
                    ['leave_request_id' => $id, 'comments' => $comments]
                );
                
                return ['success' => true, 'message' => 'Leave request approved successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to approve leave request or request not found'];
            
        } catch (Exception $e) {
            error_log("Approve leave request error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'approve_leave_request_error', $e->getMessage(), ['leave_request_id' => $id]);
            return ['success' => false, 'message' => 'Failed to approve leave request: ' . $e->getMessage()];
        }
    }
    
    /**
     * Reject leave request
     */
    public function reject($id, $rejectedBy, $reason = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE leave_requests SET 
                        status = 'rejected',
                        approved_by = ?,
                        approved_at = NOW(),
                        rejection_reason = ?,
                        updated_at = NOW()
                    WHERE id = ? AND status = 'pending'";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$rejectedBy, $reason, $id]);
            
            if ($result && $stmt->rowCount() > 0) {
                $leaveRequest = $this->getById($id);
                
                $this->logger->logActivity(
                    $rejectedBy, 
                    'leave_request_rejected', 
                    "Leave request rejected for {$leaveRequest['first_name']} {$leaveRequest['last_name']}", 
                    ['leave_request_id' => $id, 'reason' => $reason]
                );
                
                return ['success' => true, 'message' => 'Leave request rejected successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to reject leave request or request not found'];
            
        } catch (Exception $e) {
            error_log("Reject leave request error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'reject_leave_request_error', $e->getMessage(), ['leave_request_id' => $id]);
            return ['success' => false, 'message' => 'Failed to reject leave request: ' . $e->getMessage()];
        }
    }
    
    /**
     * Cancel leave request
     */
    public function cancel($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE leave_requests SET 
                        status = 'cancelled',
                        updated_at = NOW()
                    WHERE id = ? AND status IN ('pending', 'approved')";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result && $stmt->rowCount() > 0) {
                $leaveRequest = $this->getById($id);
                
                $this->logger->logActivity(
                    getCurrentUserId(), 
                    'leave_request_cancelled', 
                    "Leave request cancelled for {$leaveRequest['first_name']} {$leaveRequest['last_name']}", 
                    ['leave_request_id' => $id]
                );
                
                return ['success' => true, 'message' => 'Leave request cancelled successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to cancel leave request or request not found'];
            
        } catch (Exception $e) {
            error_log("Cancel leave request error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'cancel_leave_request_error', $e->getMessage(), ['leave_request_id' => $id]);
            return ['success' => false, 'message' => 'Failed to cancel leave request: ' . $e->getMessage()];
        }
    }
    
    /**
     * Calculate leave days between two dates
     */
    private function calculateLeaveDays($startDate, $endDate) {
        return calculateWorkingDays($startDate, $endDate);
    }
    
    /**
     * Check for overlapping leave requests
     */
    private function hasOverlappingLeave($employeeId, $startDate, $endDate, $excludeId = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT COUNT(*) FROM leave_requests 
                    WHERE employee_id = ? 
                    AND status IN ('pending', 'approved')
                    AND (
                        (start_date <= ? AND end_date >= ?) OR
                        (start_date <= ? AND end_date >= ?) OR
                        (start_date >= ? AND end_date <= ?)
                    )";
            $params = [$employeeId, $startDate, $startDate, $endDate, $endDate, $startDate, $endDate];
            
            if ($excludeId) {
                $sql .= " AND id != ?";
                $params[] = $excludeId;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn() > 0;
            
        } catch (Exception $e) {
            error_log("Check overlapping leave error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get leave balance for employee and leave type
     */
    private function getLeaveBalance($employeeId, $leaveTypeId) {
        try {
            $pdo = $this->db->getConnection();
            
            // Get leave type info
            $leaveType = $this->getLeaveTypeById($leaveTypeId);
            if (!$leaveType || $leaveType['max_days_per_year'] <= 0) {
                return null; // Unlimited leave
            }
            
            // Calculate used leave days for current year
            $sql = "SELECT COALESCE(SUM(total_days), 0) FROM leave_requests 
                    WHERE employee_id = ? 
                    AND leave_type_id = ? 
                    AND status = 'approved'
                    AND YEAR(start_date) = YEAR(NOW())";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$employeeId, $leaveTypeId]);
            $usedDays = $stmt->fetchColumn();
            
            return $leaveType['max_days_per_year'] - $usedDays;
            
        } catch (Exception $e) {
            error_log("Get leave balance error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get leave type by ID
     */
    private function getLeaveTypeById($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT * FROM leave_types WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get leave type error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get all leave types
     */
    public function getLeaveTypes() {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT * FROM leave_types WHERE is_active = 1 ORDER BY name";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get leave types error: " . $e->getMessage());
            return [];
        }
    }
}
