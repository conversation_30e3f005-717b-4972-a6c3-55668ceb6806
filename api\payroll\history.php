<?php
// Suppress PHP warnings to prevent JSON corruption
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

require_once '../../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_clean();
    http_response_code(200);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    ob_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Debug: Log the request
    error_log("Payroll history API called - Method: " . $_SERVER['REQUEST_METHOD']);
    
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    
    if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
        ob_clean();
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ']);
        exit;
    }
    
    $accessToken = substr($authHeader, 7);
    
    // Verify LINE access token
    $lineProfile = verifyLineAccessToken($accessToken);
    if (!$lineProfile) {
        ob_clean();
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'โทเค็น LINE ไม่ถูกต้อง']);
        exit;
    }
    
    // Get employee by LINE ID
    $employeeModel = new Employee();
    $employee = $employeeModel->getByLineId($lineProfile['userId']);
    
    if (!$employee) {
        ob_clean();
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลพนักงาน กรุณาลงทะเบียนก่อนใช้งาน'
        ]);
        exit;
    }
    
    // Get query parameters
    $limit = $_GET['limit'] ?? 12;
    
    // Get payroll history
    $payrollModel = new Payroll();
    $payrolls = $payrollModel->getAll($limit, 0, $employee['id']);

    ob_clean();
    echo json_encode([
        'success' => true,
        'data' => $payrolls,
        'employee' => [
            'id' => $employee['id'],
            'name' => $employee['first_name'] . ' ' . $employee['last_name'],
            'employee_code' => $employee['employee_code']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Payroll history API error: " . $e->getMessage());
    
    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์'
    ]);
}
?>
