<?php
require_once 'Database.php';

class Leave {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * Get all leave types
     */
    public function getLeaveTypes() {
        try {
            // For now, return static leave types (can be moved to database later)
            $leaveTypes = [
                [
                    'id' => 1,
                    'type_code' => 'ANNUAL',
                    'type_name' => 'ลาพักผ่อน',
                    'type_name_en' => 'Annual Leave',
                    'max_days_per_year' => 6,
                    'is_paid' => true,
                    'color' => '#28a745'
                ],
                [
                    'id' => 2,
                    'type_code' => 'SICK',
                    'type_name' => 'ลาป่วย',
                    'type_name_en' => 'Sick Leave',
                    'max_days_per_year' => 30,
                    'is_paid' => true,
                    'color' => '#dc3545'
                ],
                [
                    'id' => 3,
                    'type_code' => 'PERSONAL',
                    'type_name' => 'ลากิจ',
                    'type_name_en' => 'Personal Leave',
                    'max_days_per_year' => 3,
                    'is_paid' => false,
                    'color' => '#ffc107'
                ],
                [
                    'id' => 4,
                    'type_code' => 'EMERGENCY',
                    'type_name' => 'ลาฉุกเฉิน',
                    'type_name_en' => 'Emergency Leave',
                    'max_days_per_year' => 0,
                    'is_paid' => false,
                    'color' => '#fd7e14'
                ]
            ];

            return [
                'success' => true,
                'data' => $leaveTypes
            ];
        } catch (Exception $e) {
            error_log("Get leave types error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'ไม่สามารถดึงข้อมูลประเภทการลาได้'
            ];
        }
    }
    
    /**
     * Get all leave types
     */
    public function getLeaveTypes() {
        try {
            $query = "SELECT * FROM leave_types WHERE is_active = TRUE ORDER BY type_name";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            
            return [
                'success' => true,
                'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
            ];
        } catch (Exception $e) {
            error_log("Get leave types error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'ไม่สามารถดึงข้อมูลประเภทการลาได้'
            ];
        }
    }
    
    /**
     * Get employee leave balance
     */
    public function getEmployeeLeaveBalance($employeeId, $year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }
            
            $query = "
                SELECT 
                    lb.*,
                    lt.type_code,
                    lt.type_name,
                    lt.type_name_en,
                    lt.is_paid
                FROM leave_balances lb
                JOIN leave_types lt ON lb.leave_type_id = lt.id
                WHERE lb.employee_id = ? AND lb.year = ?
                ORDER BY lt.type_name
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$employeeId, $year]);
            
            return [
                'success' => true,
                'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
            ];
        } catch (Exception $e) {
            error_log("Get employee leave balance error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'ไม่สามารถดึงข้อมูลยอดวันลาได้'
            ];
        }
    }
    
    /**
     * Submit leave request
     */
    public function submitLeaveRequest($data) {
        try {
            // Validate required fields
            $required = ['employee_id', 'leave_type_id', 'start_date', 'end_date', 'reason'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return [
                        'success' => false,
                        'message' => "ข้อมูล {$field} จำเป็นต้องระบุ"
                    ];
                }
            }
            
            // Calculate total days
            $startDate = new DateTime($data['start_date']);
            $endDate = new DateTime($data['end_date']);
            $totalDays = $startDate->diff($endDate)->days + 1;
            
            // Check if end date is after start date
            if ($endDate < $startDate) {
                return [
                    'success' => false,
                    'message' => 'วันที่สิ้นสุดต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น'
                ];
            }
            
            // Check leave balance
            $balance = $this->getEmployeeLeaveBalance($data['employee_id']);
            if (!$balance['success']) {
                return $balance;
            }
            
            $leaveTypeBalance = null;
            foreach ($balance['data'] as $item) {
                if ($item['leave_type_id'] == $data['leave_type_id']) {
                    $leaveTypeBalance = $item;
                    break;
                }
            }
            
            if ($leaveTypeBalance && $leaveTypeBalance['remaining_days'] < $totalDays) {
                return [
                    'success' => false,
                    'message' => "วันลาคงเหลือไม่เพียงพอ (เหลือ {$leaveTypeBalance['remaining_days']} วัน)"
                ];
            }
            
            // Insert leave request
            $query = "
                INSERT INTO leave_requests 
                (employee_id, leave_type_id, request_date, start_date, end_date, total_days, reason, document_path)
                VALUES (?, ?, CURDATE(), ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([
                $data['employee_id'],
                $data['leave_type_id'],
                $data['start_date'],
                $data['end_date'],
                $totalDays,
                $data['reason'],
                $data['document_path'] ?? null
            ]);
            
            if ($result) {
                $requestId = $this->db->lastInsertId();
                
                return [
                    'success' => true,
                    'message' => 'ส่งคำขอลาเรียบร้อยแล้ว',
                    'data' => [
                        'request_id' => $requestId,
                        'total_days' => $totalDays
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'ไม่สามารถส่งคำขอลาได้'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Submit leave request error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการส่งคำขอลา'
            ];
        }
    }
    
    /**
     * Get employee leave requests
     */
    public function getEmployeeLeaveRequests($employeeId, $limit = 20, $offset = 0) {
        try {
            $query = "
                SELECT 
                    lr.*,
                    lt.type_name,
                    lt.type_code,
                    lt.is_paid,
                    approver.first_name as approver_first_name,
                    approver.last_name as approver_last_name
                FROM leave_requests lr
                JOIN leave_types lt ON lr.leave_type_id = lt.id
                LEFT JOIN employees approver ON lr.approved_by = approver.id
                WHERE lr.employee_id = ?
                ORDER BY lr.created_at DESC
                LIMIT ? OFFSET ?
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$employeeId, $limit, $offset]);
            
            return [
                'success' => true,
                'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
            ];
        } catch (Exception $e) {
            error_log("Get employee leave requests error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'ไม่สามารถดึงข้อมูลประวัติการลาได้'
            ];
        }
    }
    
    /**
     * Cancel leave request
     */
    public function cancelLeaveRequest($requestId, $employeeId) {
        try {
            // Check if request exists and belongs to employee
            $query = "SELECT * FROM leave_requests WHERE id = ? AND employee_id = ? AND status = 'pending'";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$requestId, $employeeId]);
            
            if ($stmt->rowCount() === 0) {
                return [
                    'success' => false,
                    'message' => 'ไม่พบคำขอลาที่สามารถยกเลิกได้'
                ];
            }
            
            // Update status to cancelled
            $query = "UPDATE leave_requests SET status = 'cancelled', updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([$requestId]);
            
            if ($result) {
                return [
                    'success' => true,
                    'message' => 'ยกเลิกคำขอลาเรียบร้อยแล้ว'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'ไม่สามารถยกเลิกคำขอลาได้'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Cancel leave request error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการยกเลิกคำขอลา'
            ];
        }
    }
    
    /**
     * Get leave request by ID
     */
    public function getLeaveRequestById($requestId, $employeeId = null) {
        try {
            $query = "
                SELECT 
                    lr.*,
                    lt.type_name,
                    lt.type_code,
                    lt.is_paid,
                    lt.requires_document,
                    approver.first_name as approver_first_name,
                    approver.last_name as approver_last_name
                FROM leave_requests lr
                JOIN leave_types lt ON lr.leave_type_id = lt.id
                LEFT JOIN employees approver ON lr.approved_by = approver.id
                WHERE lr.id = ?
            ";
            
            $params = [$requestId];
            
            if ($employeeId) {
                $query .= " AND lr.employee_id = ?";
                $params[] = $employeeId;
            }
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                return [
                    'success' => true,
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'ไม่พบข้อมูลคำขอลา'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Get leave request by ID error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'ไม่สามารถดึงข้อมูลคำขอลาได้'
            ];
        }
    }
    
    /**
     * Get leave statistics for employee
     */
    public function getLeaveStatistics($employeeId, $year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }
            
            $query = "
                SELECT 
                    lt.type_name,
                    lb.total_days,
                    lb.used_days,
                    lb.remaining_days,
                    COALESCE(pending.pending_days, 0) as pending_days
                FROM leave_balances lb
                JOIN leave_types lt ON lb.leave_type_id = lt.id
                LEFT JOIN (
                    SELECT 
                        leave_type_id,
                        SUM(total_days) as pending_days
                    FROM leave_requests 
                    WHERE employee_id = ? 
                    AND status = 'pending'
                    AND YEAR(start_date) = ?
                    GROUP BY leave_type_id
                ) pending ON lb.leave_type_id = pending.leave_type_id
                WHERE lb.employee_id = ? AND lb.year = ?
                ORDER BY lt.type_name
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([$employeeId, $year, $employeeId, $year]);
            
            return [
                'success' => true,
                'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
            ];
        } catch (Exception $e) {
            error_log("Get leave statistics error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'ไม่สามารถดึงข้อมูลสถิติการลาได้'
            ];
        }
    }
}
