<?php
/**
 * Setup Script for Human Resources Center System
 * This script will create the database and insert initial data
 */

require_once 'config/config.php';

// Check if setup has already been run
if (file_exists(__DIR__ . '/.setup_complete')) {
    die('Setup has already been completed. Delete .setup_complete file to run setup again.');
}

$errors = [];
$success = [];

try {
    // Create database connection
    $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE " . DB_NAME);
    
    $success[] = "Database created successfully";
    
    // Read and execute schema
    $schema = file_get_contents(__DIR__ . '/database/schema.sql');
    if ($schema === false) {
        throw new Exception("Could not read schema.sql file");
    }
    
    // Split schema into individual statements
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^(--|\/\*|\s*$)/', $statement)) {
            $pdo->exec($statement);
        }
    }
    
    $success[] = "Database schema created successfully";
    
    // Create default admin user
    $adminPassword = hashPassword('admin123');
    $adminSql = "INSERT INTO users (username, email, password_hash, role_id, is_active) 
                 VALUES ('admin', '<EMAIL>', ?, 1, 1)";
    $stmt = $pdo->prepare($adminSql);
    $stmt->execute([$adminPassword]);
    
    $success[] = "Default admin user created (username: admin, password: admin123)";
    
    // Create sample HR user
    $hrPassword = hashPassword('hr123');
    $hrSql = "INSERT INTO users (username, email, password_hash, role_id, is_active) 
              VALUES ('hr_manager', '<EMAIL>', ?, 2, 1)";
    $stmt = $pdo->prepare($hrSql);
    $stmt->execute([$hrPassword]);
    
    $success[] = "Sample HR user created (username: hr_manager, password: hr123)";
    
    // Create sample employee user
    $empPassword = hashPassword('emp123');
    $empSql = "INSERT INTO users (username, email, password_hash, role_id, is_active) 
               VALUES ('employee1', '<EMAIL>', ?, 3, 1)";
    $stmt = $pdo->prepare($empSql);
    $stmt->execute([$empPassword]);
    $empUserId = $pdo->lastInsertId();
    
    // Create sample employee record
    $employeeSql = "INSERT INTO employees (
                        employee_code, user_id, first_name, last_name, 
                        position, department, hire_date, employment_status
                    ) VALUES (?, ?, 'John', 'Doe', 'Software Developer', 'IT', CURDATE(), 'active')";
    $stmt = $pdo->prepare($employeeSql);
    $stmt->execute(['EMP001', $empUserId]);
    
    $success[] = "Sample employee created (username: employee1, password: emp123)";
    
    // Create necessary directories
    $directories = [
        'uploads/profiles',
        'uploads/documents',
        'logs/access',
        'logs/activity',
        'logs/error',
        'logs/security',
        'logs/archive'
    ];
    
    foreach ($directories as $dir) {
        $fullPath = __DIR__ . '/' . $dir;
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0755, true);
        }
    }
    
    $success[] = "Required directories created";
    
    // Create .htaccess files for security
    $htaccessContent = "deny from all";
    file_put_contents(__DIR__ . '/logs/.htaccess', $htaccessContent);
    file_put_contents(__DIR__ . '/config/.htaccess', $htaccessContent);
    
    $success[] = "Security files created";
    
    // Mark setup as complete
    file_put_contents(__DIR__ . '/.setup_complete', date('Y-m-d H:i:s'));
    
    $success[] = "Setup completed successfully!";
    
} catch (Exception $e) {
    $errors[] = "Setup failed: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR Center Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">HR Center Setup</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h5>Setup Errors:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?= htmlspecialchars($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success">
                                <h5>Setup Results:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($success as $msg): ?>
                                        <li><?= htmlspecialchars($msg) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (empty($errors) && !empty($success)): ?>
                            <div class="mt-4">
                                <h5>Next Steps:</h5>
                                <ol>
                                    <li>Update the configuration in <code>config/config.php</code> with your actual settings</li>
                                    <li>Configure your LINE Bot and LIFF app settings</li>
                                    <li>Set up email SMTP settings</li>
                                    <li>Change default passwords for security</li>
                                    <li>Access the system at <a href="login.php">login.php</a></li>
                                </ol>
                                
                                <div class="alert alert-warning mt-3">
                                    <strong>Security Note:</strong> Please change all default passwords and update configuration settings before using in production.
                                </div>
                                
                                <div class="mt-3">
                                    <a href="login.php" class="btn btn-primary">Go to Login</a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="mt-4">
                                <h5>Troubleshooting:</h5>
                                <ul>
                                    <li>Make sure your database credentials are correct in <code>config/config.php</code></li>
                                    <li>Ensure your web server has write permissions to the project directory</li>
                                    <li>Check that MySQL/MariaDB is running</li>
                                    <li>Verify PHP has PDO MySQL extension enabled</li>
                                </ul>
                                
                                <div class="mt-3">
                                    <a href="setup.php" class="btn btn-warning">Retry Setup</a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>System Requirements</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Server Requirements:</h6>
                                <ul>
                                    <li>PHP 7.4 or higher</li>
                                    <li>MySQL 5.7 or MariaDB 10.2</li>
                                    <li>Apache/Nginx web server</li>
                                    <li>SSL certificate (for production)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>PHP Extensions:</h6>
                                <ul>
                                    <li>PDO MySQL</li>
                                    <li>OpenSSL</li>
                                    <li>cURL</li>
                                    <li>JSON</li>
                                    <li>Fileinfo</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
