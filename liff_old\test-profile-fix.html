<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile Fix - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .success-card {
            border-left: 4px solid #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .debug-log {
            background: #000;
            color: #0f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            font-size: 12px;
        }
        
        .error-log { color: #f00; }
        .success-log { color: #0f0; }
        .info-log { color: #ff0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">🔧 Profile API Fix Test</h1>
            <p class="text-white-50">ทดสอบการแก้ไข Profile API</p>
        </div>
        
        <!-- Fix Summary -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">✅ การแก้ไขที่ทำ</h5>
                <div class="alert alert-success">
                    <h6>🔧 PHP Warning Fixes:</h6>
                    <ul>
                        <li>✅ เพิ่ม <code>error_reporting(0)</code> - ปิด warnings ทั้งหมด</li>
                        <li>✅ เพิ่ม <code>ob_start()</code> - เริ่ม output buffering</li>
                        <li>✅ เพิ่ม <code>ob_clean()</code> - ล้าง buffer ก่อน JSON output</li>
                        <li>✅ แก้ไข profile_picture null coalescing</li>
                    </ul>
                    
                    <h6>📁 Files Updated:</h6>
                    <ul class="mb-0">
                        <li><code>api/employee/profile.php</code></li>
                        <li><code>api/employee/register.php</code></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 Profile API Tests</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>LINE User Info:</h6>
                        <div id="userInfo" class="alert alert-info">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm"></div>
                                <span class="ms-2">Loading...</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>การทดสอบ:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success btn-lg" onclick="testProfileAPI()">
                                👤 ทดสอบ Profile API
                            </button>
                            <button class="btn btn-primary btn-lg" onclick="testMainAppFlow()">
                                🏠 ทดสอบ Main App Flow
                            </button>
                            <button class="btn btn-info btn-lg" onclick="openMainApp()">
                                📱 เปิด Main App
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Logs -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📋 Test Results</h5>
                <div id="debugLogs" class="debug-log">
                    [Ready] Profile API fix test console...
                </div>
                <div class="mt-2">
                    <button class="btn btn-secondary btn-sm" onclick="clearLogs()">
                        🗑️ Clear Logs
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Expected Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🎯 ผลลัพธ์ที่คาดหวัง</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-success">
                            <h6>✅ Profile API Success:</h6>
                            <pre class="mb-0" style="font-size: 12px;">
{
  "success": true,
  "data": {
    "employee_id": 9,
    "first_name": "Jane",
    "last_name": "Smith",
    "position": "HR Manager",
    "line_id": "U76f..."
  }
}
                            </pre>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6>📱 Main App Result:</h6>
                            <ul class="mb-0">
                                <li>✅ โหลดข้อมูลพนักงานได้</li>
                                <li>✅ แสดงหน้า Profile</li>
                                <li>✅ ไม่มี "Unexpected token" error</li>
                                <li>✅ ใช้งาน Features ได้</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        let currentUser = null;
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsDiv = document.getElementById('debugLogs');
            const colorClass = type === 'error' ? 'error-log' : type === 'success' ? 'success-log' : 'info-log';
            logsDiv.innerHTML += `<div class="${colorClass}">${logEntry}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('debugLogs').innerHTML = '[Ready] Profile API fix test console cleared...';
        }
        
        // Initialize LIFF
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                log('Initializing LIFF for profile fix test...', 'info');
                await liff.init({ liffId: LIFF_ID });
                
                if (liff.isLoggedIn()) {
                    currentUser = await liff.getProfile();
                    log(`LIFF initialized successfully. User: ${currentUser.displayName}`, 'success');
                    
                    // Display user info
                    const userInfoHtml = `
                        <div class="text-center">
                            <img src="${currentUser.pictureUrl}" alt="Profile" style="width: 60px; height: 60px; border-radius: 50%; margin-bottom: 10px;">
                            <h6>${currentUser.displayName}</h6>
                            <small>ID: ${currentUser.userId}</small>
                        </div>
                    `;
                    document.getElementById('userInfo').innerHTML = userInfoHtml;
                    
                } else {
                    log('User not logged in', 'error');
                }
            } catch (error) {
                log(`LIFF initialization failed: ${error.message}`, 'error');
            }
        });
        
        async function testProfileAPI() {
            log('🧪 Testing Profile API with fixes...', 'info');
            
            try {
                if (!currentUser) {
                    log('❌ No LINE user profile available', 'error');
                    return;
                }
                
                const accessToken = await liff.getAccessToken();
                log(`Access Token: Available`, 'success');
                
                log('📡 Calling Profile API...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/employee/profile.php`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`Response Status: ${response.status}`, response.ok ? 'success' : 'error');
                log(`Content-Type: ${response.headers.get('content-type')}`, 'info');
                
                const responseText = await response.text();
                log(`Response Length: ${responseText.length} characters`, 'info');
                
                // Check if response starts with HTML
                if (responseText.trim().startsWith('<') || responseText.includes('Warning:')) {
                    log(`❌ Response contains HTML/PHP warnings:`, 'error');
                    log(`First 200 chars: ${responseText.substring(0, 200)}`, 'error');
                    return;
                }
                
                try {
                    const data = JSON.parse(responseText);
                    log(`✅ JSON parsed successfully!`, 'success');
                    log(`Response: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.success) {
                        log(`✅ Profile loaded: ${data.data.first_name} ${data.data.last_name}`, 'success');
                        log(`Position: ${data.data.position}`, 'success');
                        log(`Department: ${data.data.department}`, 'success');
                        
                        await Swal.fire({
                            icon: 'success',
                            title: 'Profile API ทำงานได้แล้ว!',
                            text: `โหลดข้อมูล ${data.data.first_name} ${data.data.last_name} สำเร็จ`,
                            confirmButtonText: 'เยี่ยม!'
                        });
                        
                    } else {
                        log(`⚠️ Profile API returned error: ${data.message}`, 'error');
                    }
                } catch (jsonError) {
                    log(`❌ JSON parsing failed: ${jsonError.message}`, 'error');
                    log(`Raw response: ${responseText}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Profile API test error: ${error.message}`, 'error');
            }
        }
        
        async function testMainAppFlow() {
            log('🔄 Testing main app flow...', 'info');
            
            // First test profile API
            await testProfileAPI();
            
            // Wait a bit
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            log('📱 Testing main app integration...', 'info');
            
            // Simulate main app behavior
            try {
                const accessToken = await liff.getAccessToken();
                
                const response = await fetch(`${API_BASE_URL}/employee/profile.php`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Main app flow test successful!`, 'success');
                    log(`Employee data ready for main app`, 'success');
                    
                    await Swal.fire({
                        icon: 'success',
                        title: 'Main App พร้อมใช้งาน!',
                        text: 'Profile API ทำงานได้สมบูรณ์ Main App จะโหลดข้อมูลได้แล้ว',
                        confirmButtonText: 'เยี่ยมมาก!'
                    });
                    
                } else {
                    log(`⚠️ Main app flow issue: ${data.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Main app flow test error: ${error.message}`, 'error');
            }
        }
        
        function openMainApp() {
            log('🏠 Opening main app...', 'info');
            window.open('index.html', '_blank');
        }
    </script>
</body>
</html>
