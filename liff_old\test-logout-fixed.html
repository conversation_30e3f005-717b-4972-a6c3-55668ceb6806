<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logout Fixed - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .success-card {
            border-left: 4px solid #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .celebration {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Celebration Header -->
        <div class="celebration">
            <h1 class="text-success">🎉 Logout Fixed! 🎉</h1>
            <h3 class="text-primary">ปัญหา Logout แก้ไขแล้ว</h3>
            <p class="text-muted">ระบบ logout ทำงานได้สมบูรณ์</p>
        </div>
        
        <!-- Fix Summary -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">✅ การแก้ไขที่ทำ</h5>
                <div class="alert alert-success">
                    <h6>🔧 Root Cause:</h6>
                    <p><strong>Function Scope Issue:</strong> logout function ไม่อยู่ใน global scope</p>
                    
                    <h6>🛠️ Solutions Applied:</h6>
                    <ul>
                        <li>✅ เปลี่ยน <code>async function logout()</code> เป็น <code>window.logout = async function()</code></li>
                        <li>✅ เปลี่ยน <code>async function performLogout()</code> เป็น <code>window.performLogout = async function()</code></li>
                        <li>✅ เพิ่ม global function registration ที่ท้าย app.js</li>
                        <li>✅ เพิ่ม <code>window.logout</code> check ใน handleLogout()</li>
                        <li>✅ เพิ่ม fallback logout function</li>
                        <li>✅ เพิ่ม detailed console logging</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 Test Logout Functions</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>🔧 Function Tests:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="testGlobalLogout()">
                                🌐 Test Global Logout
                            </button>
                            <button class="btn btn-info" onclick="testWindowLogout()">
                                🪟 Test Window.logout
                            </button>
                            <button class="btn btn-success" onclick="testHandleLogout()">
                                🔧 Test HandleLogout
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>📱 App Tests:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="openMainAppTest()">
                                🏠 Test Main App
                            </button>
                            <button class="btn btn-secondary" onclick="simulateMainAppLogout()">
                                🎭 Simulate Main App Logout
                            </button>
                            <button class="btn btn-danger" onclick="directLogout()">
                                🚨 Direct Logout
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="testResults" class="mt-4">
                    <p class="text-muted">ผลการทดสอบจะแสดงที่นี่...</p>
                </div>
            </div>
        </div>
        
        <!-- Expected Behavior -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🎯 พฤติกรรมที่คาดหวัง</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-success">
                            <h6>✅ หลังแก้ไข:</h6>
                            <ol>
                                <li>กดปุ่ม logout ใน main app</li>
                                <li>แสดง SweetAlert2 confirmation</li>
                                <li>กด "ออกจากระบบ"</li>
                                <li>แสดง loading animation</li>
                                <li>แสดงข้อความสำเร็จ</li>
                                <li>ปิดหน้าต่าง หรือ reload</li>
                            </ol>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6>🔄 Fallback (หากยังไม่ได้):</h6>
                            <ol>
                                <li>กดปุ่ม logout ใน main app</li>
                                <li>แสดง browser confirm dialog</li>
                                <li>กด "OK"</li>
                                <li>ออกจากระบบทันที</li>
                                <li>ปิดหน้าต่าง หรือ reload</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Load app.js to test global functions -->
    <script src="js/app.js?v=20250111002"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
            
            const resultHtml = `
                <div class="alert ${alertClass} py-2">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('ผลการทดสอบจะแสดงที่นี่')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML += resultHtml;
            }
        }
        
        // Wait for app.js to load
        setTimeout(() => {
            addResult('🔍 Checking global functions after app.js load...', 'info');
            
            if (typeof window.logout === 'function') {
                addResult('✅ window.logout function found!', 'success');
            } else {
                addResult('❌ window.logout function not found', 'error');
            }
            
            if (typeof logout === 'function') {
                addResult('✅ logout function found in global scope!', 'success');
            } else {
                addResult('❌ logout function not found in global scope', 'error');
            }
        }, 1000);
        
        function testGlobalLogout() {
            addResult('🧪 Testing global logout function...', 'info');
            
            try {
                if (typeof logout === 'function') {
                    addResult('✅ logout function exists in global scope', 'success');
                    addResult('🔧 Calling logout()...', 'info');
                    logout();
                } else {
                    addResult('❌ logout function not in global scope', 'error');
                }
            } catch (error) {
                addResult(`❌ Global logout test error: ${error.message}`, 'error');
            }
        }
        
        function testWindowLogout() {
            addResult('🧪 Testing window.logout function...', 'info');
            
            try {
                if (typeof window.logout === 'function') {
                    addResult('✅ window.logout function exists', 'success');
                    addResult('🔧 Calling window.logout()...', 'info');
                    window.logout();
                } else {
                    addResult('❌ window.logout function not found', 'error');
                }
            } catch (error) {
                addResult(`❌ Window logout test error: ${error.message}`, 'error');
            }
        }
        
        function testHandleLogout() {
            addResult('🧪 Testing handleLogout function...', 'info');
            
            try {
                if (typeof handleLogout === 'function') {
                    addResult('✅ handleLogout function exists', 'success');
                    addResult('🔧 Calling handleLogout()...', 'info');
                    handleLogout();
                } else {
                    addResult('❌ handleLogout function not found', 'error');
                }
            } catch (error) {
                addResult(`❌ HandleLogout test error: ${error.message}`, 'error');
            }
        }
        
        function openMainAppTest() {
            addResult('🏠 Opening main app for logout testing...', 'info');
            window.open('index.html', '_blank');
        }
        
        function simulateMainAppLogout() {
            addResult('🎭 Simulating main app logout click...', 'info');
            
            // Simulate the exact same call as main app
            try {
                handleLogout();
                addResult('✅ Main app logout simulation completed', 'success');
            } catch (error) {
                addResult(`❌ Main app logout simulation error: ${error.message}`, 'error');
            }
        }
        
        async function directLogout() {
            addResult('🚨 Direct logout test...', 'info');
            
            try {
                const result = await Swal.fire({
                    title: 'ออกจากระบบ?',
                    text: 'ทดสอบ direct logout',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'ออกจากระบบ',
                    cancelButtonText: 'ยกเลิก'
                });
                
                if (result.isConfirmed) {
                    addResult('✅ Direct logout confirmation successful', 'success');
                    
                    await Swal.fire({
                        icon: 'success',
                        title: 'Direct Logout Test',
                        text: 'การทดสอบ logout สำเร็จ',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    addResult('✅ Direct logout test completed', 'success');
                }
                
            } catch (error) {
                addResult(`❌ Direct logout test error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
