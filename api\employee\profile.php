<?php
// Suppress PHP warnings to prevent JSON corruption
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

require_once '../../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Debug: Log the request
    error_log("Profile API called - Method: " . $_SERVER['REQUEST_METHOD']);
    error_log("Profile API called - URI: " . $_SERVER['REQUEST_URI']);

    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';

    error_log("Profile API - Auth header: " . ($authHeader ? 'Present' : 'Missing'));

    if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
        ob_clean();
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ']);
        exit;
    }
    
    $accessToken = substr($authHeader, 7); // Remove "Bearer " prefix
    
    // Verify LINE access token and get user profile
    error_log("Profile API - Verifying LINE token...");
    $lineProfile = verifyLineAccessToken($accessToken);
    if (!$lineProfile) {
        error_log("Profile API - LINE token verification failed");
        ob_clean();
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'โทเค็นไม่ถูกต้อง']);
        exit;
    }

    error_log("Profile API - LINE profile verified: " . json_encode($lineProfile));
    
    // Find employee by LINE ID
    error_log("Profile API - Looking up employee by LINE ID: " . $lineProfile['userId']);
    $employeeModel = new Employee();
    $employee = $employeeModel->getByLineId($lineProfile['userId']);

    if (!$employee) {
        error_log("Profile API - Employee not found for LINE ID: " . $lineProfile['userId']);
        ob_clean();
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลพนักงานที่ลงทะเบียนกับ LINE ID นี้ กรุณาลงทะเบียนก่อนใช้งาน',
            'action' => 'REGISTRATION_REQUIRED',
            'line_profile' => $lineProfile
        ]);
        exit;
    }

    error_log("Profile API - Employee found: " . $employee['employee_code']);
    
    // Clean output buffer and return employee profile
    ob_clean();

    echo json_encode([
        'success' => true,
        'data' => [
            'employee_id' => $employee['id'],
            'employee_code' => $employee['employee_code'],
            'first_name' => $employee['first_name'],
            'last_name' => $employee['last_name'],
            'nickname' => $employee['nickname'],
            'position' => $employee['position'],
            'department' => $employee['department'],
            'email' => $employee['email'],
            'phone' => $employee['phone'],
            'hire_date' => $employee['hire_date'],
            'employment_status' => $employee['employment_status'],
            'line_id' => $employee['line_id'],
            'profile_picture' => $employee['line_picture_url'] ?: ($employee['profile_picture'] ?? null)
        ],
        'line_profile' => $lineProfile
    ]);
    
} catch (Exception $e) {
    error_log("Employee profile API error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์',
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}

/**
 * Verify LINE access token and get user profile
 */
function verifyLineAccessToken($accessToken) {
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.line.me/v2/profile');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return json_decode($response, true);
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("LINE token verification error: " . $e->getMessage());
        return false;
    }
}
?>
