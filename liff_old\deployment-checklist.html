<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deployment Checklist - LINE LIFF Registration</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .checklist-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #6c757d;
            transition: all 0.3s ease;
        }
        
        .checklist-item.completed {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .checklist-item.in-progress {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .checklist-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .status-icon {
            font-size: 1.2em;
            margin-right: 10px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">📋 Deployment Checklist</h1>
            <p class="text-white-50">LINE LIFF Registration System</p>
        </div>
        
        <!-- Current Status -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title text-success">🎉 ความสำเร็จปัจจุบัน</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>✅ ส่วนที่เสร็จแล้ว:</h6>
                        <ul>
                            <li>LIFF SDK Integration</li>
                            <li>LINE OAuth Authentication</li>
                            <li>Registration UI/UX</li>
                            <li>Form Validation</li>
                            <li>Error Handling</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>⚠️ ที่ต้องทำต่อ:</h6>
                        <ul>
                            <li>Upload API Files</li>
                            <li>Update Database Schema</li>
                            <li>Test Production APIs</li>
                            <li>Test Complete Flow</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Deployment Steps -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🚀 ขั้นตอนการ Deploy</h5>
                
                <!-- Step 1: Database -->
                <div class="checklist-item" id="step1">
                    <div class="d-flex align-items-center">
                        <span class="status-icon">⏳</span>
                        <div class="flex-grow-1">
                            <h6>1. อัปเดต Database Schema</h6>
                            <p class="mb-2">เพิ่ม columns สำหรับ LINE integration</p>
                            <div class="code-block">
ALTER TABLE employees 
ADD COLUMN line_id VARCHAR(255) UNIQUE,
ADD COLUMN line_display_name VARCHAR(255),
ADD COLUMN line_picture_url TEXT,
ADD COLUMN line_registered_at TIMESTAMP NULL;
                            </div>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" onclick="markCompleted('step1')">
                            ✓ เสร็จแล้ว
                        </button>
                    </div>
                </div>
                
                <!-- Step 2: API Files -->
                <div class="checklist-item" id="step2">
                    <div class="d-flex align-items-center">
                        <span class="status-icon">⏳</span>
                        <div class="flex-grow-1">
                            <h6>2. Upload API Files</h6>
                            <p class="mb-2">Upload ไฟล์ API ไปยัง production server</p>
                            <ul class="mb-2">
                                <li><code>api/employee/register.php</code></li>
                                <li><code>api/employee/profile.php</code> (updated)</li>
                                <li><code>models/Employee.php</code> (updated)</li>
                            </ul>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" onclick="markCompleted('step2')">
                            ✓ เสร็จแล้ว
                        </button>
                    </div>
                </div>
                
                <!-- Step 3: Test APIs -->
                <div class="checklist-item" id="step3">
                    <div class="d-flex align-items-center">
                        <span class="status-icon">⏳</span>
                        <div class="flex-grow-1">
                            <h6>3. ทดสอบ API Endpoints</h6>
                            <p class="mb-2">ตรวจสอบว่า APIs ตอบสนองได้ถูกต้อง</p>
                            <div class="mt-2">
                                <button class="btn btn-info btn-sm me-2" onclick="testAPI('profile')">
                                    Test Profile API
                                </button>
                                <button class="btn btn-success btn-sm" onclick="testAPI('register')">
                                    Test Register API
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" onclick="markCompleted('step3')">
                            ✓ เสร็จแล้ว
                        </button>
                    </div>
                </div>
                
                <!-- Step 4: Test Registration -->
                <div class="checklist-item" id="step4">
                    <div class="d-flex align-items-center">
                        <span class="status-icon">⏳</span>
                        <div class="flex-grow-1">
                            <h6>4. ทดสอบ Registration Flow</h6>
                            <p class="mb-2">ทดสอบการลงทะเบียนพนักงานใหม่</p>
                            <div class="mt-2">
                                <button class="btn btn-primary btn-sm me-2" onclick="openRegistrationTest()">
                                    เปิดหน้าทดสอบ
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="openRegistrationPage()">
                                    ทดสอบ Registration
                                </button>
                            </div>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" onclick="markCompleted('step4')">
                            ✓ เสร็จแล้ว
                        </button>
                    </div>
                </div>
                
                <!-- Step 5: Test LINE App -->
                <div class="checklist-item" id="step5">
                    <div class="d-flex align-items-center">
                        <span class="status-icon">⏳</span>
                        <div class="flex-grow-1">
                            <h6>5. ทดสอบใน LINE App</h6>
                            <p class="mb-2">ทดสอบการใช้งานจริงผ่าน LINE Official Account</p>
                            <ul class="mb-2">
                                <li>เปิด LIFF ผ่าน Rich Menu</li>
                                <li>ทดสอบ Registration Flow</li>
                                <li>ทดสอบ Login Flow</li>
                            </ul>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" onclick="markCompleted('step5')">
                            ✓ เสร็จแล้ว
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 ผลการทดสอบ</h5>
                <div id="testResults">
                    <p class="text-muted">ยังไม่มีผลการทดสอบ</p>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">⚡ Quick Actions</h5>
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-primary w-100 mb-2" onclick="downloadFiles()">
                            📥 Download Files
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-info w-100 mb-2" onclick="copySQL()">
                            📋 Copy SQL Script
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success w-100 mb-2" onclick="testAllAPIs()">
                            🧪 Test All APIs
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning w-100 mb-2" onclick="resetChecklist()">
                            🔄 Reset Checklist
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        function markCompleted(stepId) {
            const step = document.getElementById(stepId);
            step.classList.remove('in-progress', 'error');
            step.classList.add('completed');
            
            const icon = step.querySelector('.status-icon');
            icon.textContent = '✅';
            
            addTestResult(`${stepId.toUpperCase()} completed successfully`);
        }
        
        function markInProgress(stepId) {
            const step = document.getElementById(stepId);
            step.classList.remove('completed', 'error');
            step.classList.add('in-progress');
            
            const icon = step.querySelector('.status-icon');
            icon.textContent = '⏳';
        }
        
        function markError(stepId, message) {
            const step = document.getElementById(stepId);
            step.classList.remove('completed', 'in-progress');
            step.classList.add('error');
            
            const icon = step.querySelector('.status-icon');
            icon.textContent = '❌';
            
            addTestResult(`${stepId.toUpperCase()} failed: ${message}`);
        }
        
        async function testAPI(type) {
            const endpoint = type === 'profile' ? '/employee/profile' : '/employee/register';
            const url = `${API_BASE_URL}${endpoint}`;
            
            try {
                const response = await fetch(url, {
                    method: type === 'register' ? 'OPTIONS' : 'GET'
                });
                
                if (response.ok || response.status === 401) {
                    addTestResult(`✅ ${type.toUpperCase()} API is available (${response.status})`);
                } else {
                    addTestResult(`❌ ${type.toUpperCase()} API failed (${response.status})`);
                }
                
            } catch (error) {
                addTestResult(`❌ ${type.toUpperCase()} API error: ${error.message}`);
            }
        }
        
        function openRegistrationTest() {
            window.open('test-registration.html', '_blank');
        }
        
        function openRegistrationPage() {
            window.open('register.html', '_blank');
        }
        
        function addTestResult(message) {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('testResults');
            
            const resultHtml = `
                <div class="alert alert-light py-2">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('ยังไม่มีผลการทดสอบ')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML += resultHtml;
            }
        }
        
        function downloadFiles() {
            const fileList = `
Files to Download and Upload:

1. API Files:
   - api/employee/register.php
   - api/employee/profile.php (updated)
   - models/Employee.php (updated)

2. Database Script:
   - database/line_integration_update.sql

3. Source Location:
   C:\\laragon\\www\\hrc\\

4. Target Location:
   https://smartapplytech.com/hrc/
            `;
            
            alert(fileList);
        }
        
        function copySQL() {
            const sql = `ALTER TABLE employees 
ADD COLUMN line_id VARCHAR(255) UNIQUE,
ADD COLUMN line_display_name VARCHAR(255),
ADD COLUMN line_picture_url TEXT,
ADD COLUMN line_registered_at TIMESTAMP NULL;

CREATE INDEX idx_employees_line_id ON employees(line_id);`;
            
            navigator.clipboard.writeText(sql).then(() => {
                alert('📋 SQL script copied to clipboard!');
            }).catch(() => {
                alert(sql);
            });
        }
        
        async function testAllAPIs() {
            addTestResult('🧪 Starting API tests...');
            await testAPI('profile');
            await testAPI('register');
            addTestResult('✅ API tests completed');
        }
        
        function resetChecklist() {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
            steps.forEach(stepId => {
                const step = document.getElementById(stepId);
                step.classList.remove('completed', 'in-progress', 'error');
                
                const icon = step.querySelector('.status-icon');
                icon.textContent = '⏳';
            });
            
            document.getElementById('testResults').innerHTML = '<p class="text-muted">ยังไม่มีผลการทดสอบ</p>';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('📋 Deployment checklist initialized');
        });
    </script>
</body>
</html>
