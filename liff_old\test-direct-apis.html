<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct API Test - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .api-link {
            background: #fff;
            border: 2px solid #ff6b6b;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            text-decoration: none;
            color: #333;
            display: block;
            transition: all 0.3s ease;
        }
        
        .api-link:hover {
            background: #ff6b6b;
            color: white;
            transform: translateY(-2px);
        }
        
        .celebration {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="celebration">
            <h1 class="text-danger">🔗 Direct API Test</h1>
            <h3 class="text-info">ทดสอบ APIs โดยตรง</h3>
            <p class="text-muted">แก้ไขปัญหา cache ด้วยการเรียก API โดยตรง</p>
        </div>
        
        <!-- Problem Analysis -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title text-danger">🚨 Problem Analysis</h5>
                
                <div class="alert alert-warning">
                    <h6>❌ ปัญหาที่พบ:</h6>
                    <ul>
                        <li>Browser cache ยังใช้ app.js version เก่า</li>
                        <li>Main app ยังเรียก <code>/api/leave-types</code> (404)</li>
                        <li>แม้ว่าจะมี cache busting <code>?v=20250111002</code></li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h6>✅ Solution:</h6>
                    <ul>
                        <li>✅ ทดสอบ APIs โดยตรงก่อน</li>
                        <li>✅ ใช้ index-new.html ที่มี JavaScript ใหม่</li>
                        <li>✅ Force refresh browser cache</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Direct API Links -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🔗 Direct API Links</h5>
                <p class="text-muted">คลิกลิงก์เหล่านี้เพื่อทดสอบ APIs โดยตรง:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>📝 Leave APIs:</h6>
                        
                        <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=test" target="_blank" class="api-link">
                            <i class="fas fa-cog text-primary me-2"></i>
                            <strong>Test API</strong><br>
                            <small>ทดสอบ API พื้นฐาน</small>
                        </a>
                        
                        <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=leave-types" target="_blank" class="api-link">
                            <i class="fas fa-list text-success me-2"></i>
                            <strong>Leave Types</strong><br>
                            <small>ประเภทการลา 4 ประเภท</small>
                        </a>
                        
                        <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=leave-history" target="_blank" class="api-link">
                            <i class="fas fa-history text-info me-2"></i>
                            <strong>Leave History</strong><br>
                            <small>ประวัติการลา + สถิติ</small>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <h6>💰 Payroll APIs:</h6>
                        
                        <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=payslip" target="_blank" class="api-link">
                            <i class="fas fa-file-invoice-dollar text-warning me-2"></i>
                            <strong>Latest Payslip</strong><br>
                            <small>สลิปเงินเดือนล่าสุด</small>
                        </a>
                        
                        <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=payroll-history" target="_blank" class="api-link">
                            <i class="fas fa-chart-line text-secondary me-2"></i>
                            <strong>Payroll History</strong><br>
                            <small>ประวัติเงินเดือน 6 เดือน</small>
                        </a>
                        
                        <a href="https://smartapplytech.com/hrc/api/debug/simple-test.php?action=payslip-detail" target="_blank" class="api-link">
                            <i class="fas fa-search text-dark me-2"></i>
                            <strong>Payslip Detail</strong><br>
                            <small>รายละเอียดสลิป</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Cache Fix Instructions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🔄 Cache Fix Instructions</h5>
                
                <div class="alert alert-warning">
                    <h6>🔧 แก้ไข Browser Cache:</h6>
                    <ol>
                        <li><strong>Hard Refresh:</strong> กด <kbd>Ctrl+Shift+R</kbd> (Windows) หรือ <kbd>Cmd+Shift+R</kbd> (Mac)</li>
                        <li><strong>Developer Tools:</strong> กด F12 → Network tab → ✅ Disable cache</li>
                        <li><strong>Private/Incognito:</strong> เปิดหน้าเว็บใน private mode</li>
                        <li><strong>Clear Browser Data:</strong> ลบ cache ทั้งหมด</li>
                    </ol>
                </div>
                
                <div class="alert alert-success">
                    <h6>✅ Alternative Solution:</h6>
                    <p>ใช้ <strong>index-new.html</strong> ที่มี JavaScript ใหม่ทั้งหมด (ไม่มี cache issues)</p>
                    <div class="d-grid gap-2 d-md-block">
                        <a href="https://smartapplytech.com/hrc/liff/index-new.html" target="_blank" class="btn btn-success">
                            🆕 Open New App
                        </a>
                        <button class="btn btn-info" onclick="compareVersions()">
                            🔍 Compare Versions
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Manual Test -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 Manual Test</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>📝 Test Leave Features:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="testLeaveTypesManual()">
                                📋 Test Leave Types
                            </button>
                            <button class="btn btn-success" onclick="testLeaveHistoryManual()">
                                📊 Test Leave History
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>💰 Test Payroll Features:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-info" onclick="testPayslipManual()">
                                💳 Test Payslip
                            </button>
                            <button class="btn btn-warning" onclick="runFullTest()">
                                🚀 Run Full Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 Test Results</h5>
                <div id="testResults">
                    <div class="alert alert-info">
                        <h6>🎯 Ready to Test:</h6>
                        <ul class="mb-0">
                            <li>✅ Simple API: 6/6 endpoints working</li>
                            <li>✅ New App: Created with fresh JavaScript</li>
                            <li>✅ Debug logging: Added to all functions</li>
                            <li>🧪 Manual testing: Ready</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Success Guide -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🎯 Success Guide</h5>
                
                <div class="alert alert-success">
                    <h6>✅ หากทดสอบสำเร็จ:</h6>
                    <ol>
                        <li>APIs ทั้งหมดส่ง JSON ที่ถูกต้อง</li>
                        <li>Main app โหลดข้อมูลได้</li>
                        <li>ฟีเจอร์ทั้งหมดทำงานได้</li>
                        <li>🚀 พร้อม Production!</li>
                    </ol>
                </div>
                
                <div class="alert alert-info">
                    <h6>🔧 หากยังมีปัญหา:</h6>
                    <ol>
                        <li>ตรวจสอบ Console (F12) เพื่อดู errors</li>
                        <li>ใช้ index-new.html แทน index.html</li>
                        <li>Clear browser cache ทั้งหมด</li>
                        <li>ทดสอบใน private/incognito mode</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
            
            const resultHtml = `
                <div class="alert ${alertClass} py-2">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
        }
        
        async function testLeaveTypesManual() {
            addResult('🧪 Testing Leave Types API manually...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/simple-test.php?action=leave-types`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Leave Types: Found ${data.data.length} types`, 'success');
                    addResult(`📋 Types: ${data.data.map(t => t.type_name).join(', ')}`, 'info');
                } else {
                    addResult(`❌ Leave Types failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Leave Types error: ${error.message}`, 'error');
            }
        }
        
        async function testLeaveHistoryManual() {
            addResult('🧪 Testing Leave History API manually...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/simple-test.php?action=leave-history`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Leave History: Found ${data.data.length} requests`, 'success');
                    addResult(`📊 Statistics: ${data.statistics.length} leave types tracked`, 'info');
                } else {
                    addResult(`❌ Leave History failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Leave History error: ${error.message}`, 'error');
            }
        }
        
        async function testPayslipManual() {
            addResult('🧪 Testing Payslip API manually...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/simple-test.php?action=payslip`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Payslip: ${data.data.pay_period_start} to ${data.data.pay_period_end}`, 'success');
                    addResult(`💰 Net Pay: ฿${Number(data.data.net_pay).toLocaleString()}`, 'info');
                } else {
                    addResult(`❌ Payslip failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Payslip error: ${error.message}`, 'error');
            }
        }
        
        async function runFullTest() {
            addResult('🚀 Running full manual test...', 'info');
            
            const tests = [
                { name: 'Leave Types', func: testLeaveTypesManual },
                { name: 'Leave History', func: testLeaveHistoryManual },
                { name: 'Payslip', func: testPayslipManual }
            ];
            
            let successCount = 0;
            
            for (const test of tests) {
                try {
                    await test.func();
                    successCount++;
                } catch (error) {
                    addResult(`❌ ${test.name} failed: ${error.message}`, 'error');
                }
                
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            addResult(`🎯 Full Test Complete: ${successCount}/${tests.length} working`, successCount === tests.length ? 'success' : 'error');
            
            if (successCount === tests.length) {
                await Swal.fire({
                    icon: 'success',
                    title: 'All APIs Working!',
                    html: `
                        <div class="text-center">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h4>APIs ทำงานได้สมบูรณ์!</h4>
                            <p>ตอนนี้ทดสอบ main app</p>
                            <div class="d-grid gap-2 d-md-block mt-3">
                                <a href="https://smartapplytech.com/hrc/liff/index-new.html" target="_blank" class="btn btn-success">
                                    🆕 Test New App
                                </a>
                                <a href="https://smartapplytech.com/hrc/liff/index.html" target="_blank" class="btn btn-primary">
                                    🏠 Test Main App
                                </a>
                            </div>
                        </div>
                    `,
                    confirmButtonText: 'ทดสอบ App!'
                });
            }
        }
        
        async function compareVersions() {
            await Swal.fire({
                title: '🔍 Version Comparison',
                html: `
                    <div class="text-left">
                        <h6>📱 App Versions:</h6>
                        <div class="row">
                            <div class="col-6">
                                <div class="alert alert-warning">
                                    <h6>🏠 index.html (Original)</h6>
                                    <ul class="mb-0">
                                        <li>❌ Cache issues</li>
                                        <li>❌ Old API endpoints</li>
                                        <li>🔄 Needs hard refresh</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="alert alert-success">
                                    <h6>🆕 index-new.html (Fixed)</h6>
                                    <ul class="mb-0">
                                        <li>✅ Fresh JavaScript</li>
                                        <li>✅ Correct API endpoints</li>
                                        <li>✅ No cache issues</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <strong>💡 Recommendation:</strong> ใช้ index-new.html สำหรับ production
                        </div>
                    </div>
                `,
                confirmButtonText: 'เข้าใจแล้ว'
            });
        }
        
        // Auto-run test on load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                runFullTest();
            }, 1000);
        });
    </script>
</body>
</html>
