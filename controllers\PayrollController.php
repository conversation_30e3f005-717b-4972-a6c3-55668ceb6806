<?php
/**
 * Payroll Controller
 * Human Resources Center System
 */

class PayrollController {
    private $payrollModel;
    private $employeeModel;
    private $salaryModel;
    private $logger;
    
    public function __construct() {
        $this->payrollModel = new Payroll();
        $this->employeeModel = new Employee();
        $this->salaryModel = new Salary();
        $this->logger = new Logger();
        
        // Require authentication
        requireLogin();
    }
    
    /**
     * List payroll records
     */
    public function index() {
        requirePermission('payroll.view');
        
        $page = $_GET['page'] ?? 1;
        $employeeId = $_GET['employee_id'] ?? null;
        $month = $_GET['month'] ?? null;
        $year = $_GET['year'] ?? date('Y');
        
        $limit = RECORDS_PER_PAGE;
        $offset = ($page - 1) * $limit;
        
        $payrolls = $this->payrollModel->getAll($limit, $offset, $employeeId, $month, $year);
        $totalRecords = $this->payrollModel->count($employeeId, $month, $year);
        $pagination = paginate($totalRecords, $page, $limit);
        
        // Get employees for filter
        $employees = $this->employeeModel->getAll();
        
        // Get statistics
        $stats = [
            'total_payrolls' => $this->payrollModel->count(null, null, $year),
            'draft_payrolls' => $this->getDraftPayrollsCount($year),
            'approved_payrolls' => $this->getApprovedPayrollsCount($year),
            'total_amount' => $this->getTotalPayrollAmount($year)
        ];
        
        include __DIR__ . '/../views/admin/payroll/index.php';
    }
    
    /**
     * Show payroll details
     */
    public function show() {
        requirePermission('payroll.view');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            setFlashMessage('error', 'Payroll ID is required');
            redirect(BASE_URL . '/admin/payroll/index.php');
        }
        
        $payroll = $this->payrollModel->getById($id);
        if (!$payroll) {
            setFlashMessage('error', 'Payroll not found');
            redirect(BASE_URL . '/admin/payroll/index.php');
        }
        
        include __DIR__ . '/../views/admin/payroll/show.php';
    }
    
    /**
     * Create payroll
     */
    public function create() {
        requirePermission('payroll.create');
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->store();
        }
        
        $employees = $this->employeeModel->getAll();
        
        include __DIR__ . '/../views/admin/payroll/create.php';
    }
    
    /**
     * Store new payroll
     */
    public function store() {
        requirePermission('payroll.create');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        // Validate input
        $validator = new Validator($_POST);
        $validator->required('employee_id', 'Employee is required')
                 ->required('pay_period_start', 'Pay period start is required')
                 ->required('pay_period_end', 'Pay period end is required')
                 ->required('pay_date', 'Pay date is required')
                 ->required('basic_salary', 'Basic salary is required')
                 ->numeric('basic_salary', 'Basic salary must be a number');
        
        if ($validator->fails()) {
            if (isset($_POST['ajax'])) {
                errorResponse('Validation failed', 400);
            }
            
            setFlashMessage('error', 'Please correct the errors below');
            $errors = $validator->getErrors();
            $employees = $this->employeeModel->getAll();
            include __DIR__ . '/../views/admin/payroll/create.php';
            return;
        }
        
        // Prepare data
        $data = [
            'employee_id' => $_POST['employee_id'],
            'pay_period_start' => $_POST['pay_period_start'],
            'pay_period_end' => $_POST['pay_period_end'],
            'pay_date' => $_POST['pay_date'],
            'basic_salary' => floatval($_POST['basic_salary']),
            'allowances' => floatval($_POST['allowances'] ?? 0),
            'overtime_hours' => floatval($_POST['overtime_hours'] ?? 0),
            'overtime_pay' => floatval($_POST['overtime_pay'] ?? 0),
            'bonus' => floatval($_POST['bonus'] ?? 0),
            'tax' => floatval($_POST['tax'] ?? 0),
            'social_security' => floatval($_POST['social_security'] ?? 0),
            'other_deductions' => floatval($_POST['other_deductions'] ?? 0),
            'status' => $_POST['status'] ?? 'draft'
        ];
        
        $result = $this->payrollModel->create($data);
        
        if (isset($_POST['ajax'])) {
            if ($result['success']) {
                successResponse($result['message'], ['id' => $result['id']]);
            } else {
                errorResponse($result['message'], 400);
            }
        }
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
            redirect(BASE_URL . '/admin/payroll/index.php');
        } else {
            setFlashMessage('error', $result['message']);
            $employees = $this->employeeModel->getAll();
            include __DIR__ . '/../views/admin/payroll/create.php';
        }
    }
    
    /**
     * Edit payroll
     */
    public function edit() {
        requirePermission('payroll.edit');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            setFlashMessage('error', 'Payroll ID is required');
            redirect(BASE_URL . '/admin/payroll/index.php');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->update($id);
        }
        
        $payroll = $this->payrollModel->getById($id);
        if (!$payroll) {
            setFlashMessage('error', 'Payroll not found');
            redirect(BASE_URL . '/admin/payroll/index.php');
        }
        
        $employees = $this->employeeModel->getAll();
        
        include __DIR__ . '/../views/admin/payroll/edit.php';
    }
    
    /**
     * Update payroll
     */
    public function update($id) {
        requirePermission('payroll.edit');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        // Validate input
        $validator = new Validator($_POST);
        $validator->required('pay_period_start', 'Pay period start is required')
                 ->required('pay_period_end', 'Pay period end is required')
                 ->required('pay_date', 'Pay date is required')
                 ->required('basic_salary', 'Basic salary is required')
                 ->numeric('basic_salary', 'Basic salary must be a number');
        
        if ($validator->fails()) {
            if (isset($_POST['ajax'])) {
                errorResponse('Validation failed', 400);
            }
            
            setFlashMessage('error', 'Please correct the errors below');
            $errors = $validator->getErrors();
            $payroll = $this->payrollModel->getById($id);
            $employees = $this->employeeModel->getAll();
            include __DIR__ . '/../views/admin/payroll/edit.php';
            return;
        }
        
        // Prepare data
        $data = [
            'pay_period_start' => $_POST['pay_period_start'],
            'pay_period_end' => $_POST['pay_period_end'],
            'pay_date' => $_POST['pay_date'],
            'basic_salary' => floatval($_POST['basic_salary']),
            'allowances' => floatval($_POST['allowances'] ?? 0),
            'overtime_hours' => floatval($_POST['overtime_hours'] ?? 0),
            'overtime_pay' => floatval($_POST['overtime_pay'] ?? 0),
            'bonus' => floatval($_POST['bonus'] ?? 0),
            'tax' => floatval($_POST['tax'] ?? 0),
            'social_security' => floatval($_POST['social_security'] ?? 0),
            'other_deductions' => floatval($_POST['other_deductions'] ?? 0),
            'status' => $_POST['status'] ?? 'draft'
        ];
        
        $result = $this->payrollModel->update($id, $data);
        
        if (isset($_POST['ajax'])) {
            if ($result['success']) {
                successResponse($result['message']);
            } else {
                errorResponse($result['message'], 400);
            }
        }
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
            redirect(BASE_URL . '/admin/payroll/index.php');
        } else {
            setFlashMessage('error', $result['message']);
            $payroll = $this->payrollModel->getById($id);
            $employees = $this->employeeModel->getAll();
            include __DIR__ . '/../views/admin/payroll/edit.php';
        }
    }
    
    /**
     * Delete payroll
     */
    public function delete() {
        requirePermission('payroll.delete');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $id = $_POST['id'] ?? null;
        if (!$id) {
            errorResponse('Payroll ID is required', 400);
        }
        
        $result = $this->payrollModel->delete($id);
        
        if ($result['success']) {
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Approve payroll
     */
    public function approve() {
        requirePermission('payroll.approve');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $id = $_POST['id'] ?? null;
        if (!$id) {
            errorResponse('Payroll ID is required', 400);
        }
        
        $result = $this->payrollModel->approve($id);
        
        if ($result['success']) {
            // Send payslip notification
            $payroll = $this->payrollModel->getById($id);
            if ($payroll) {
                require_once __DIR__ . '/../helpers/notification.php';
                sendPayslipNotification($payroll['employee_id'], $id);
            }
            
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Get payroll data for AJAX
     */
    public function getData() {
        requirePermission('payroll.view');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            errorResponse('Payroll ID is required', 400);
        }
        
        $payroll = $this->payrollModel->getById($id);
        if (!$payroll) {
            errorResponse('Payroll not found', 404);
        }
        
        successResponse('Payroll data retrieved', $payroll);
    }
    
    /**
     * Get total payroll amount for year
     */
    private function getTotalPayrollAmount($year) {
        try {
            $database = new Database();
            $pdo = $database->getConnection();

            $sql = "SELECT SUM(net_pay) FROM payroll WHERE YEAR(pay_period_start) = ? AND status = 'approved'";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$year]);

            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            error_log("Get total payroll amount error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get draft payrolls count for year
     */
    private function getDraftPayrollsCount($year) {
        try {
            $database = new Database();
            $pdo = $database->getConnection();

            $sql = "SELECT COUNT(*) FROM payroll WHERE YEAR(pay_period_start) = ? AND status = 'draft'";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$year]);

            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            error_log("Get draft payrolls count error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get approved payrolls count for year
     */
    private function getApprovedPayrollsCount($year) {
        try {
            $database = new Database();
            $pdo = $database->getConnection();

            $sql = "SELECT COUNT(*) FROM payroll WHERE YEAR(pay_period_start) = ? AND status = 'approved'";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$year]);

            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            error_log("Get approved payrolls count error: " . $e->getMessage());
            return 0;
        }
    }
}
