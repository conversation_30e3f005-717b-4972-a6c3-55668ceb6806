<?php
require_once '../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    
    if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ']);
        exit;
    }
    
    $accessToken = substr($authHeader, 7);
    
    // Verify LINE access token
    $lineProfile = verifyLineAccessToken($accessToken);
    if (!$lineProfile) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'โทเค็นไม่ถูกต้อง']);
        exit;
    }
    
    // Find employee by LINE ID
    $employeeModel = new Employee();
    $employee = $employeeModel->getByLineId($lineProfile['userId']);
    
    if (!$employee) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลพนักงาน']);
        exit;
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    $leaveModel = new Leave();
    
    switch ($method) {
        case 'GET':
            // Get leave requests for employee
            $leaves = $leaveModel->getByEmployeeId($employee['id']);
            
            echo json_encode([
                'success' => true,
                'data' => $leaves
            ]);
            break;
            
        case 'POST':
            // Create new leave request
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validate input
            if (empty($input['leave_type_id']) || empty($input['start_date']) || empty($input['end_date'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ครบถ้วน']);
                exit;
            }
            
            // Prepare leave data
            $leaveData = [
                'employee_id' => $employee['id'],
                'leave_type_id' => $input['leave_type_id'],
                'start_date' => $input['start_date'],
                'end_date' => $input['end_date'],
                'reason' => $input['reason'] ?? '',
                'status' => 'pending'
            ];
            
            $result = $leaveModel->create($leaveData);
            
            if ($result['success']) {
                echo json_encode(['success' => true, 'message' => 'ส่งคำขอลาเรียบร้อยแล้ว']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $result['message']]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'วิธีการเรียกใช้ไม่ได้รับอนุญาต']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Leave requests API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์']);
}

/**
 * Verify LINE access token and get user profile
 */
function verifyLineAccessToken($accessToken) {
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.line.me/v2/profile');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return json_decode($response, true);
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("LINE token verification error: " . $e->getMessage());
        return false;
    }
}
?>
