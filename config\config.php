<?php
/**
 * System Configuration
 * Human Resources Center System
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Bangkok');

// System Constants
define('APP_NAME', 'Human Resources Center');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/hrc');
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('LOG_PATH', __DIR__ . '/../logs/');

// Security Constants
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Database Constants
define('DB_HOST', 'localhost');
define('DB_NAME', 'hrc_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// LINE API Configuration
define('LINE_CHANNEL_ACCESS_TOKEN', 'your-line-channel-access-token');
define('LINE_CHANNEL_SECRET', 'b3c32db8f8cb4f924a32787d07aadb9c');
define('LINE_LIFF_ID', '2007905561-Bzx2VrZZ');

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'HR System');

// User Roles
define('ROLE_ADMIN', 1);
define('ROLE_HR', 2);
define('ROLE_EMPLOYEE', 3);

// Log Types
define('LOG_ACCESS', 'access');
define('LOG_ACTIVITY', 'activity');
define('LOG_ERROR', 'error');
define('LOG_SECURITY', 'security');

// File Upload Settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);

// Pagination
define('RECORDS_PER_PAGE', 10);

// Auto-load classes
spl_autoload_register(function ($class) {
    $directories = [
        __DIR__ . '/',              // config directory
        __DIR__ . '/../models/',
        __DIR__ . '/../controllers/',
        __DIR__ . '/../core/',
        __DIR__ . '/../helpers/'
    ];

    foreach ($directories as $directory) {
        $file = $directory . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            break;
        }
    }
});

// Include core classes first
require_once __DIR__ . '/database.php';

// Include helper functions
require_once __DIR__ . '/../helpers/functions.php';
require_once __DIR__ . '/../helpers/security.php';
require_once __DIR__ . '/../helpers/validation.php';

// Create necessary directories
$directories = [
    LOG_PATH,
    UPLOAD_PATH,
    UPLOAD_PATH . 'profiles/',
    UPLOAD_PATH . 'documents/',
    LOG_PATH . 'access/',
    LOG_PATH . 'activity/',
    LOG_PATH . 'error/',
    LOG_PATH . 'security/'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
