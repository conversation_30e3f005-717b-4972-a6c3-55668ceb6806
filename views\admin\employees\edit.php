<?php
$pageTitle = 'Edit Employee';
include __DIR__ . '/../../layouts/header.php';
?>

<div class="conatiner-fluid content-inner mt-n5 py-0">
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">Edit Employee</h4>
                    </div>
                    <div class="header-action">
                        <a href="<?= BASE_URL ?>/admin/employees" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employee_code" class="form-label">Employee Code</label>
                                    <input type="text" class="form-control" id="employee_code" name="employee_code" 
                                           value="<?= htmlspecialchars($employee['employee_code']) ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="title_name" class="form-label">Title</label>
                                    <select class="form-select" id="title_name" name="title_name">
                                        <option value="">Select Title</option>
                                        <option value="Mr." <?= $employee['title_name'] === 'Mr.' ? 'selected' : '' ?>>Mr.</option>
                                        <option value="Ms." <?= $employee['title_name'] === 'Ms.' ? 'selected' : '' ?>>Ms.</option>
                                        <option value="Mrs." <?= $employee['title_name'] === 'Mrs.' ? 'selected' : '' ?>>Mrs.</option>
                                        <option value="Dr." <?= $employee['title_name'] === 'Dr.' ? 'selected' : '' ?>>Dr.</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="<?= htmlspecialchars($employee['first_name']) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="<?= htmlspecialchars($employee['last_name']) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="nickname" class="form-label">Nickname</label>
                                    <input type="text" class="form-control" id="nickname" name="nickname" 
                                           value="<?= htmlspecialchars($employee['nickname']) ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="id_card" class="form-label">ID Card Number</label>
                                    <input type="text" class="form-control" id="id_card" name="id_card" 
                                           value="<?= htmlspecialchars($employee['id_card']) ?>" maxlength="13">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="birth_date" class="form-label">Birth Date</label>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                           value="<?= $employee['birth_date'] ?>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="male" <?= $employee['gender'] === 'male' ? 'selected' : '' ?>>Male</option>
                                        <option value="female" <?= $employee['gender'] === 'female' ? 'selected' : '' ?>>Female</option>
                                        <option value="other" <?= $employee['gender'] === 'other' ? 'selected' : '' ?>>Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?= htmlspecialchars($employee['phone']) ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($employee['email']) ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="line_id" class="form-label">LINE ID</label>
                            <input type="text" class="form-control" id="line_id" name="line_id" 
                                   value="<?= htmlspecialchars($employee['line_id']) ?>">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($employee['address']) ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="emergency_contact_name" class="form-label">Emergency Contact Name</label>
                                    <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name" 
                                           value="<?= htmlspecialchars($employee['emergency_contact_name']) ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="emergency_contact_phone" class="form-label">Emergency Contact Phone</label>
                                    <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" 
                                           value="<?= htmlspecialchars($employee['emergency_contact_phone']) ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="position" class="form-label">Position</label>
                                    <input type="text" class="form-control" id="position" name="position" 
                                           value="<?= htmlspecialchars($employee['position']) ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="department" class="form-label">Department</label>
                                    <input type="text" class="form-control" id="department" name="department" 
                                           value="<?= htmlspecialchars($employee['department']) ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="hire_date" class="form-label">Hire Date</label>
                                    <input type="date" class="form-control" id="hire_date" name="hire_date" 
                                           value="<?= $employee['hire_date'] ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="employment_status" class="form-label">Employment Status</label>
                                    <select class="form-select" id="employment_status" name="employment_status">
                                        <option value="active" <?= $employee['employment_status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                        <option value="inactive" <?= $employee['employment_status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        <option value="terminated" <?= $employee['employment_status'] === 'terminated' ? 'selected' : '' ?>>Terminated</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="profile_image" class="form-label">Profile Image</label>
                            <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                            <?php if ($employee['profile_image']): ?>
                            <div class="mt-2">
                                <img src="<?= BASE_URL ?>/uploads/profiles/<?= $employee['profile_image'] ?>" 
                                     alt="Current Profile" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                <p class="text-muted mt-1">Current profile image</p>
                            </div>
                            <?php endif; ?>
                            <div class="mt-2">
                                <img id="imagePreview" src="#" alt="Preview" style="max-width: 150px; max-height: 150px; display: none;" class="img-thumbnail">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Employee</button>
                            <a href="<?= BASE_URL ?>/admin/employees" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Image preview
document.getElementById('profile_image').addEventListener('change', function() {
    previewImage(this, '#imagePreview');
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    if (!validateForm('form')) {
        e.preventDefault();
        showAlert('error', 'Please fill in all required fields correctly');
        return false;
    }
});
</script>

<?php include __DIR__ . '/../../layouts/footer.php'; ?>
