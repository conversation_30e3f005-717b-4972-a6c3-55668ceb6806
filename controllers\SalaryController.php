<?php
/**
 * Salary Controller
 * Human Resources Center System
 */

class SalaryController {
    private $salaryModel;
    private $employeeModel;
    private $logger;
    
    public function __construct() {
        $this->salaryModel = new Salary();
        $this->employeeModel = new Employee();
        $this->logger = new Logger();
        
        // Require authentication
        requireLogin();
    }
    
    /**
     * List salary records
     */
    public function index() {
        requirePermission('salary.view');
        
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? null;
        $department = $_GET['department'] ?? null;
        
        $limit = RECORDS_PER_PAGE;
        $offset = ($page - 1) * $limit;
        
        // Get employees with their salary information
        $employees = $this->employeeModel->getAll($limit, $offset, $search, $department);
        $totalRecords = $this->employeeModel->count($search, $department);
        $pagination = paginate($totalRecords, $page, $limit);
        
        // Get departments for filter
        $departments = $this->employeeModel->getDepartments();
        
        // Get salary statistics
        $stats = [
            'total_employees' => $this->employeeModel->count(),
            'employees_with_salary' => $this->getEmployeesWithSalaryCount(),
            'average_salary' => $this->getAverageSalary(),
            'total_payroll' => $this->getTotalPayroll()
        ];
        
        include __DIR__ . '/../views/admin/salary/index.php';
    }
    
    /**
     * Get salary data for AJAX
     */
    public function getData() {
        requirePermission('salary.view');
        
        $employeeId = $_GET['employee_id'] ?? null;
        if (!$employeeId) {
            errorResponse('Employee ID is required', 400);
        }
        
        $salary = $this->salaryModel->getByEmployeeId($employeeId);
        
        successResponse('Salary data retrieved', $salary);
    }
    
    /**
     * Create or update salary
     */
    public function store() {
        requirePermission('salary.edit');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        // Validate input
        $validator = new Validator($_POST);
        $validator->required('employee_id', 'Employee ID is required')
                 ->required('basic_salary', 'Basic salary is required')
                 ->required('effective_date', 'Effective date is required')
                 ->numeric('basic_salary', 'Basic salary must be a number')
                 ->date('effective_date', 'Y-m-d', 'Invalid effective date format');
        
        if ($validator->fails()) {
            if (isset($_POST['ajax'])) {
                errorResponse('Validation failed', 400);
            }
            
            setFlashMessage('error', 'Please correct the errors below');
            redirect(BASE_URL . '/admin/salary/index.php');
            return;
        }
        
        // Prepare data
        $data = [
            'employee_id' => $_POST['employee_id'],
            'basic_salary' => floatval($_POST['basic_salary']),
            'allowances' => floatval($_POST['allowances'] ?? 0),
            'overtime_rate' => floatval($_POST['overtime_rate'] ?? 0),
            'effective_date' => $_POST['effective_date'],
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        // Check if salary record exists
        $existingSalary = $this->salaryModel->getByEmployeeId($data['employee_id']);
        
        if ($existingSalary) {
            // Deactivate old salary first
            $this->salaryModel->deactivateOldSalaries($data['employee_id']);
            
            // Create new salary record
            $result = $this->salaryModel->create($data);
        } else {
            // Create new salary
            $result = $this->salaryModel->create($data);
        }
        
        if (isset($_POST['ajax'])) {
            if ($result['success']) {
                successResponse($result['message']);
            } else {
                errorResponse($result['message'], 400);
            }
        }
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
        } else {
            setFlashMessage('error', $result['message']);
        }
        
        redirect(BASE_URL . '/admin/salary/index.php');
    }
    
    /**
     * Get salary history for employee
     */
    public function getHistory() {
        requirePermission('salary.view');
        
        $employeeId = $_GET['employee_id'] ?? null;
        if (!$employeeId) {
            errorResponse('Employee ID is required', 400);
        }
        
        $history = $this->salaryModel->getSalaryHistory($employeeId);
        
        successResponse('Salary history retrieved', $history);
    }
    
    /**
     * Delete salary record
     */
    public function delete() {
        requirePermission('salary.delete');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $id = $_POST['id'] ?? null;
        if (!$id) {
            errorResponse('Salary ID is required', 400);
        }
        
        $result = $this->salaryModel->delete($id);
        
        if ($result['success']) {
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Get employees with salary count
     */
    private function getEmployeesWithSalaryCount() {
        try {
            $database = new Database();
            $pdo = $database->getConnection();
            
            $sql = "SELECT COUNT(DISTINCT employee_id) FROM salaries WHERE is_active = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchColumn() ?: 0;
            
        } catch (Exception $e) {
            error_log("Get employees with salary count error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get average salary
     */
    private function getAverageSalary() {
        try {
            $database = new Database();
            $pdo = $database->getConnection();
            
            $sql = "SELECT AVG(basic_salary + allowances) FROM salaries WHERE is_active = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchColumn() ?: 0;
            
        } catch (Exception $e) {
            error_log("Get average salary error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get total payroll
     */
    private function getTotalPayroll() {
        try {
            $database = new Database();
            $pdo = $database->getConnection();
            
            $sql = "SELECT SUM(basic_salary + allowances) FROM salaries WHERE is_active = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchColumn() ?: 0;
            
        } catch (Exception $e) {
            error_log("Get total payroll error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Export salary report
     */
    public function exportReport() {
        requirePermission('salary.export');
        
        $format = $_GET['format'] ?? 'csv';
        $department = $_GET['department'] ?? null;
        
        // Get all employees with salary data
        $employees = $this->employeeModel->getAll(null, 0, null, $department);
        
        $data = [];
        foreach ($employees as $employee) {
            $salary = $this->salaryModel->getByEmployeeId($employee['id']);
            $data[] = [
                'Employee Code' => $employee['employee_code'],
                'Name' => $employee['first_name'] . ' ' . $employee['last_name'],
                'Department' => $employee['department'] ?? '-',
                'Position' => $employee['position'] ?? '-',
                'Basic Salary' => $salary ? number_format($salary['basic_salary'], 2) : '0.00',
                'Allowances' => $salary ? number_format($salary['allowances'], 2) : '0.00',
                'Total Salary' => $salary ? number_format($salary['basic_salary'] + $salary['allowances'], 2) : '0.00',
                'Effective Date' => $salary ? $salary['effective_date'] : '-',
                'Status' => $salary && $salary['is_active'] ? 'Active' : 'Inactive'
            ];
        }
        
        if ($format === 'csv') {
            $this->exportCSV($data, 'salary_report_' . date('Y-m-d') . '.csv');
        } else {
            $this->exportExcel($data, 'salary_report_' . date('Y-m-d') . '.xlsx');
        }
    }
    
    /**
     * Export data as CSV
     */
    private function exportCSV($data, $filename) {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Write header
        if (!empty($data)) {
            fputcsv($output, array_keys($data[0]));
            
            // Write data
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export data as Excel (simplified)
     */
    private function exportExcel($data, $filename) {
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        echo '<table border="1">';
        
        // Write header
        if (!empty($data)) {
            echo '<tr>';
            foreach (array_keys($data[0]) as $header) {
                echo '<th>' . htmlspecialchars($header) . '</th>';
            }
            echo '</tr>';
            
            // Write data
            foreach ($data as $row) {
                echo '<tr>';
                foreach ($row as $cell) {
                    echo '<td>' . htmlspecialchars($cell) . '</td>';
                }
                echo '</tr>';
            }
        }
        
        echo '</table>';
        exit;
    }
}
