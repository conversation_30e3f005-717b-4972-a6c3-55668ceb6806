<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LIFF Test Page</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LIFF Test & Debug Page</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>Configuration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>LIFF ID:</strong><br>
                        <code id="liffId">Loading...</code>
                    </div>
                    <div class="col-md-6">
                        <strong>API Base URL:</strong><br>
                        <code id="apiBaseUrl">Loading...</code>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>Environment Information</h5>
            </div>
            <div class="card-body">
                <div id="envInfo">Loading...</div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>LIFF Tests</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary me-2" onclick="testLiffSdk()">Test LIFF SDK</button>
                <button class="btn btn-secondary me-2" onclick="testLiffInit()">Test LIFF Init</button>
                <button class="btn btn-info me-2" onclick="testApiConnection()">Test API Connection</button>
                <button class="btn btn-warning" onclick="clearResults()">Clear Results</button>
                
                <div id="testResults" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Console Logs</h5>
            </div>
            <div class="card-body">
                <div id="consoleLogs" style="background: #000; color: #0f0; padding: 10px; border-radius: 5px; font-family: monospace; height: 300px; overflow-y: auto;"></div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        // Override console.log to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const logs = [];
        
        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString()});
            updateConsoleLogs();
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            updateConsoleLogs();
            originalError.apply(console, args);
        };
        
        function updateConsoleLogs() {
            const consoleDiv = document.getElementById('consoleLogs');
            consoleDiv.innerHTML = logs.map(log => 
                `<div style="color: ${log.type === 'error' ? '#f00' : '#0f0'}">[${log.time}] ${log.message}</div>`
            ).join('');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            logs.length = 0;
            updateConsoleLogs();
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('liffId').textContent = LIFF_ID;
            document.getElementById('apiBaseUrl').textContent = API_BASE_URL;
            
            // Environment info
            const envInfo = `
                <strong>User Agent:</strong><br>
                <small>${navigator.userAgent}</small><br><br>
                <strong>Current URL:</strong><br>
                <small>${window.location.href}</small><br><br>
                <strong>Referrer:</strong><br>
                <small>${document.referrer || 'None'}</small><br><br>
                <strong>LIFF SDK Available:</strong><br>
                <span class="${window.liff ? 'text-success' : 'text-danger'}">${window.liff ? 'Yes' : 'No'}</span>
            `;
            document.getElementById('envInfo').innerHTML = envInfo;
            
            console.log('Test page loaded');
        });
        
        function testLiffSdk() {
            addTestResult('Testing LIFF SDK availability...', 'info');
            
            if (window.liff) {
                addTestResult('✓ LIFF SDK is available', 'success');
                console.log('LIFF SDK version:', liff.getVersion());
            } else {
                addTestResult('✗ LIFF SDK is not available', 'error');
            }
        }
        
        async function testLiffInit() {
            addTestResult('Testing LIFF initialization...', 'info');
            
            try {
                await liff.init({ liffId: LIFF_ID });
                addTestResult('✓ LIFF initialized successfully', 'success');
                
                const context = liff.getContext();
                console.log('LIFF context:', context);
                addTestResult(`Context: ${JSON.stringify(context)}`, 'info');
                
                if (liff.isLoggedIn()) {
                    addTestResult('✓ User is logged in', 'success');
                    try {
                        const profile = await liff.getProfile();
                        addTestResult(`User: ${profile.displayName} (${profile.userId})`, 'success');
                    } catch (error) {
                        addTestResult(`✗ Failed to get profile: ${error.message}`, 'error');
                    }
                } else {
                    addTestResult('User is not logged in', 'info');
                }
                
            } catch (error) {
                addTestResult(`✗ LIFF initialization failed: ${error.message}`, 'error');
                console.error('LIFF init error:', error);
            }
        }
        
        async function testApiConnection() {
            addTestResult('Testing API connection...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/employee/profile`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                addTestResult(`API Response Status: ${response.status}`, response.status === 401 ? 'info' : 'error');
                
                const data = await response.json();
                addTestResult(`API Response: ${JSON.stringify(data)}`, 'info');
                
            } catch (error) {
                addTestResult(`✗ API connection failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
