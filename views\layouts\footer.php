    </main>
    
    <!-- Footer -->
    <footer class="iq-footer">
        <div class="container-fluid">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <ul class="list-inline p-0 m-0 d-flex flex-wrap align-items-center">
                                <li class="list-inline-item me-3">
                                    <p class="mb-0">© <?= date('Y') ?> <?= APP_NAME ?>. All rights reserved.</p>
                                </li>
                            </ul>
                        </div>
                        <div class="col-lg-6 text-end">
                            <span class="mr-1">
                                <script>document.write(new Date().getFullYear())</script>©
                            </span>
                            <a href="#" class="">HR Center</a>.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Library Bundle Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/core/libs.min.js"></script>
    
    <!-- External Library Bundle Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/core/external.min.js"></script>
    
    <!-- Widgetchart Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/charts/widgetcharts.js"></script>
    
    <!-- mapchart Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/charts/vectore-chart.js"></script>
    <script src="<?= BASE_URL ?>/public/assets/js/charts/dashboard.js"></script>
    
    <!-- fslightbox Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/plugins/fslightbox.js"></script>
    
    <!-- Settings Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/plugins/setting.js"></script>
    
    <!-- Slider-tab Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/plugins/slider-tabs.js"></script>
    
    <!-- Form Wizard Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/plugins/form-wizard.js"></script>
    
    <!-- AOS Animation Plugin-->
    <script src="<?= BASE_URL ?>/public/assets/vendor/aos/dist/aos.js"></script>
    
    <!-- App Script -->
    <script src="<?= BASE_URL ?>/public/assets/js/hope-ui.js" defer></script>

    <!-- Custom Scripts -->
    <script>
        // CSRF Token for AJAX requests
        const CSRF_TOKEN = '<?= generateCSRFToken() ?>';
        const BASE_URL = '<?= BASE_URL ?>';
        
        // Set CSRF token for all AJAX requests
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", CSRF_TOKEN);
                }
            }
        });

        // Common functions
        function showAlert(type, message, title = '') {
            Swal.fire({
                icon: type,
                title: title || (type === 'success' ? 'สำเร็จ' : type === 'error' ? 'เกิดข้อผิดพลาด' : 'ข้อมูล'),
                text: message,
                confirmButtonText: 'ตกลง'
            });
        }

        function showConfirm(message, callback, title = 'คุณแน่ใจหรือไม่?') {
            Swal.fire({
                title: title,
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'ใช่',
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed && typeof callback === 'function') {
                    callback();
                }
            });
        }

        function showLoading(message = 'กำลังโหลด...') {
            Swal.fire({
                title: message,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        function hideLoading() {
            Swal.close();
        }

        // Handle AJAX errors
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            hideLoading();

            if (xhr.status === 401) {
                showAlert('error', 'เซสชันของคุณหมดอายุแล้ว กรุณาเข้าสู่ระบบใหม่');
                setTimeout(() => {
                    window.location.href = BASE_URL + '/login.php';
                }, 2000);
            } else if (xhr.status === 403) {
                showAlert('error', 'คุณไม่มีสิทธิ์ในการดำเนินการนี้');
            } else if (xhr.status === 404) {
                showAlert('error', 'ไม่พบข้อมูลที่ต้องการ');
            } else if (xhr.status >= 500) {
                showAlert('error', 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์ กรุณาลองใหม่อีกครั้ง');
            } else {
                try {
                    const response = JSON.parse(xhr.responseText);
                    showAlert('error', response.message || 'เกิดข้อผิดพลาด');
                } catch (e) {
                    showAlert('error', 'เกิดข้อผิดพลาดที่ไม่คาดคิด');
                }
            }
        });

        // Form validation helper
        function validateForm(formSelector) {
            const form = $(formSelector);
            let isValid = true;
            
            form.find('[required]').each(function() {
                const field = $(this);
                const value = field.val().trim();
                
                if (!value) {
                    field.addClass('is-invalid');
                    isValid = false;
                } else {
                    field.removeClass('is-invalid');
                }
            });
            
            // Email validation
            form.find('input[type="email"]').each(function() {
                const field = $(this);
                const value = field.val().trim();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                if (value && !emailRegex.test(value)) {
                    field.addClass('is-invalid');
                    isValid = false;
                } else if (value) {
                    field.removeClass('is-invalid');
                }
            });
            
            return isValid;
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // DataTable default configuration
        if (typeof $.fn.DataTable !== 'undefined') {
            $.extend(true, $.fn.dataTable.defaults, {
                responsive: true,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        }

        // File upload preview
        function previewImage(input, previewSelector) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $(previewSelector).attr('src', e.target.result).show();
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Format currency
        function formatCurrency(amount, currency = '฿') {
            return currency + ' ' + parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // Format date
        function formatDate(dateString, format = 'DD/MM/YYYY') {
            if (!dateString) return '-';
            const date = new Date(dateString);
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            
            switch (format) {
                case 'DD/MM/YYYY':
                    return `${day}/${month}/${year}`;
                case 'MM/DD/YYYY':
                    return `${month}/${day}/${year}`;
                case 'YYYY-MM-DD':
                    return `${year}-${month}-${day}`;
                default:
                    return `${day}/${month}/${year}`;
            }
        }

        // Debounce function for search
        function debounce(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }

        // Auto-logout on session timeout
        let sessionTimeout;
        function resetSessionTimeout() {
            clearTimeout(sessionTimeout);
            sessionTimeout = setTimeout(function() {
                showAlert('warning', 'เซสชันของคุณจะหมดอายุใน 5 นาที กรุณาบันทึกงานของคุณ');
                setTimeout(function() {
                    window.location.href = BASE_URL + '/logout.php';
                }, 300000); // 5 minutes
            }, <?= (SESSION_TIMEOUT - 300) * 1000 ?>); // 5 minutes before actual timeout
        }

        // Reset session timeout on user activity
        $(document).on('click keypress scroll', resetSessionTimeout);
        resetSessionTimeout();
    </script>

    <?php if (isset($additionalScripts)): ?>
        <?= $additionalScripts ?>
    <?php endif; ?>
</body>
</html>
