<?php
// Suppress PHP warnings to prevent JSON corruption
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

require_once '../../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Handle different HTTP methods
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Return API info for GET requests
        echo json_encode([
            'success' => true,
            'message' => 'Employee Registration API',
            'methods' => ['POST'],
            'description' => 'Use POST method to register employee with LINE ID'
        ]);
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'วิธีการเรียกใช้ไม่ได้รับอนุญาต']);
        exit;
    }
    
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    
    if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ']);
        exit;
    }
    
    $accessToken = substr($authHeader, 7);
    
    // Verify LINE access token and get user profile
    $lineProfile = verifyLineAccessToken($accessToken);
    if (!$lineProfile) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'โทเค็น LINE ไม่ถูกต้อง']);
        exit;
    }
    
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ถูกต้อง']);
        exit;
    }
    
    // Validate required fields
    $employeeCode = trim($input['employee_code'] ?? '');
    $phone = trim($input['phone'] ?? '');
    
    if (empty($employeeCode) || empty($phone)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'กรุณากรอกรหัสพนักงานและเบอร์โทรศัพท์']);
        exit;
    }
    
    // Validate phone format (Thai mobile number)
    if (!preg_match('/^0[6-9]\d{8}$/', $phone)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง (ตัวอย่าง: ************)']);
        exit;
    }
    
    // Check if employee exists and verify credentials
    error_log("Registration API - Looking up employee: Code={$employeeCode}, Phone={$phone}");

    $employeeModel = new Employee();
    $employee = $employeeModel->getByEmployeeCodeAndPhone($employeeCode, $phone);

    error_log("Registration API - Employee lookup result: " . ($employee ? 'Found' : 'Not found'));

    if (!$employee) {
        error_log("Registration API - Employee not found for code: {$employeeCode}, phone: {$phone}");
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบข้อมูลพนักงานที่ตรงกับรหัสพนักงานและเบอร์โทรศัพท์ที่ระบุ กรุณาติดต่อฝ่ายบุคคล',
            'debug' => [
                'employee_code' => $employeeCode,
                'phone' => $phone,
                'line_user_id' => $lineProfile['userId']
            ]
        ]);
        exit;
    }

    error_log("Registration API - Employee found: ID={$employee['id']}, Code={$employee['employee_code']}");
    
    // Check if this employee already has LINE ID registered
    if (!empty($employee['line_id']) && $employee['line_id'] !== $lineProfile['userId']) {
        http_response_code(409);
        echo json_encode([
            'success' => false, 
            'message' => 'รหัสพนักงานนี้ได้ลงทะเบียน LINE ID แล้ว หากมีปัญหากรุณาติดต่อฝ่ายบุคคล'
        ]);
        exit;
    }
    
    // Check if this LINE ID is already registered with another employee
    $existingEmployee = $employeeModel->getByLineId($lineProfile['userId']);
    if ($existingEmployee && $existingEmployee['id'] !== $employee['id']) {
        http_response_code(409);
        echo json_encode([
            'success' => false, 
            'message' => 'LINE ID นี้ได้ลงทะเบียนกับพนักงานคนอื่นแล้ว'
        ]);
        exit;
    }
    
    // Update employee with LINE ID and profile info
    $updateData = [
        'line_id' => $lineProfile['userId'],
        'line_display_name' => $lineProfile['displayName'] ?? null,
        'line_picture_url' => $lineProfile['pictureUrl'] ?? null,
        'phone' => $phone, // Update phone in case it changed
        'line_registered_at' => date('Y-m-d H:i:s')
    ];
    
    $result = $employeeModel->updateLineRegistration($employee['id'], $updateData);
    
    if ($result['success']) {
        // Log the registration (simple error_log)
        error_log("Employee registered LINE ID successfully - Employee ID: {$employee['id']}, LINE ID: {$lineProfile['userId']}, Code: {$employeeCode}");

        // Clean output buffer and return employee data
        $updatedEmployee = $employeeModel->getById($employee['id']);

        ob_clean();
        echo json_encode([
            'success' => true,
            'message' => 'ลงทะเบียนเรียบร้อยแล้ว ยินดีต้อนรับเข้าสู่ระบบ HR Center',
            'data' => [
                'employee_id' => $updatedEmployee['id'],
                'employee_code' => $updatedEmployee['employee_code'],
                'first_name' => $updatedEmployee['first_name'],
                'last_name' => $updatedEmployee['last_name'],
                'nickname' => $updatedEmployee['nickname'],
                'position' => $updatedEmployee['position'],
                'department' => $updatedEmployee['department'],
                'email' => $updatedEmployee['email'],
                'phone' => $updatedEmployee['phone'],
                'hire_date' => $updatedEmployee['hire_date'],
                'employment_status' => $updatedEmployee['employment_status'],
                'line_id' => $updatedEmployee['line_id'],
                'profile_picture' => $updatedEmployee['line_picture_url'] ?: ($updatedEmployee['profile_picture'] ?? null)
            ]
        ]);
        
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => $result['message']]);
    }
    
} catch (Exception $e) {
    error_log("Employee registration API error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์',
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}

/**
 * Verify LINE access token and get user profile
 */
function verifyLineAccessToken($accessToken) {
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.line.me/v2/profile');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return json_decode($response, true);
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("LINE token verification error: " . $e->getMessage());
        return false;
    }
}
?>
