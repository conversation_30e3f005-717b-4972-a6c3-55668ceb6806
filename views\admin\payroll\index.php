<?php
require_once __DIR__ . '/../../../config/config.php';

$auth = new Auth();

// Require authentication
if (!$auth->isAuthenticated()) {
    redirect(BASE_URL . '/login.php');
}

$currentUser = $auth->getCurrentUser();
$pageTitle = 'Payroll Management';
include __DIR__ . '/../../layouts/header.php';
?>

<div class="conatiner-fluid content-inner mt-n5 py-0">
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Total Payrolls</span>
                            <h4 class="mb-0"><?= number_format($stats['total_payrolls']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-primary-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.7688 8.71387H16.2312C18.5886 8.71387 20.5 10.5831 20.5 12.8885V17.8254C20.5 20.1308 18.5886 22 16.2312 22H7.7688C5.41136 22 3.5 20.1308 3.5 17.8254V12.8885C3.5 10.5831 5.41136 8.71387 7.7688 8.71387Z" fill="currentColor"></path>
                                <path opacity="0.4" d="M17.523 7.39595V8.86667C17.1673 8.7673 16.7913 8.71761 16.4052 8.71761H15.7447V7.39595C15.7447 5.37868 14.0681 3.73903 12.0053 3.73903C9.94257 3.73903 8.26594 5.36874 8.25578 7.37608V8.71761H7.60545C7.20916 8.71761 6.83319 8.7673 6.47754 8.87661V7.39595C6.4877 4.41476 8.95692 2 11.985 2C15.0537 2 17.523 4.41476 17.523 7.39595Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Draft Payrolls</span>
                            <h4 class="mb-0"><?= number_format($stats['draft_payrolls']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-warning-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Approved</span>
                            <h4 class="mb-0"><?= number_format($stats['approved_payrolls']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-success-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11.3194 14.3209C11.1261 14.3219 10.9328 14.2523 10.7838 14.1091L8.86695 12.2656C8.57097 11.9793 8.56795 11.5145 8.86091 11.2262C9.15387 10.9369 9.63207 10.934 9.92906 11.2193L11.3083 12.5451L14.6758 9.22479C14.9698 8.93552 15.448 8.93258 15.744 9.21793C16.041 9.50426 16.044 9.97004 15.751 10.2574L11.8519 14.1022C11.7049 14.2474 11.5127 14.3199 11.3194 14.3209Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Total Amount</span>
                            <h4 class="mb-0"><?= formatCurrency($stats['total_amount']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-info-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M13.3051 5.88243V6.06547C12.8144 6.05584 12.3237 6.05584 11.8331 6.05584V5.89206C11.8331 5.22733 11.2737 4.68784 10.6064 4.68784H9.63482C8.52589 4.68784 7.62305 3.80152 7.62305 2.72254C7.62305 2.32755 7.95671 2 8.35906 2C8.77123 2 9.09508 2.32755 9.09508 2.72254C9.09508 3.01155 9.34042 3.24276 9.63482 3.24276H10.6064C12.0882 3.2524 13.2953 4.43736 13.3051 5.88243Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M15.164 6.08279C15.4791 6.08712 15.7949 6.09145 16.1119 6.09469C19.5172 6.09469 22 8.52241 22 11.875V16.1813C22 19.5339 19.5172 21.9616 16.1119 21.9616C14.7478 21.9905 13.3837 22.0001 12.0098 22.0001C10.6359 22.0001 9.25221 21.9905 7.88813 21.9616C4.48283 21.9616 2 19.5339 2 16.1813V11.875C2 8.52241 4.48283 6.09469 7.89794 6.09469C9.18351 6.07542 10.4985 6.05615 11.8332 6.05615C12.3238 6.05615 12.8145 6.05615 13.3052 6.06579C13.9238 6.06579 14.5425 6.07427 15.164 6.08279Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">Payroll Records</h4>
                    </div>
                    <div class="header-action">
                        <?php if (hasPermission('payroll.create')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#payrollModal">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path opacity="0.4" d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2Z" fill="currentColor"></path>
                            </svg>
                            Create Payroll
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="form-group">
                                <select class="form-select" id="employeeFilter">
                                    <option value="">All Employees</option>
                                    <?php foreach ($employees as $emp): ?>
                                    <option value="<?= $emp['id'] ?>" 
                                            <?= ($_GET['employee_id'] ?? '') == $emp['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($emp['first_name'] . ' ' . $emp['last_name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select class="form-select" id="monthFilter">
                                    <option value="">All Months</option>
                                    <?php for ($i = 1; $i <= 12; $i++): ?>
                                    <option value="<?= $i ?>" <?= ($_GET['month'] ?? '') == $i ? 'selected' : '' ?>>
                                        <?= date('F', mktime(0, 0, 0, $i, 1)) ?>
                                    </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select class="form-select" id="yearFilter">
                                    <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                    <option value="<?= $y ?>" <?= ($_GET['year'] ?? date('Y')) == $y ? 'selected' : '' ?>>
                                        <?= $y ?>
                                    </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="draft" <?= ($_GET['status'] ?? '') === 'draft' ? 'selected' : '' ?>>Draft</option>
                                    <option value="approved" <?= ($_GET['status'] ?? '') === 'approved' ? 'selected' : '' ?>>Approved</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-primary w-100" onclick="applyFilters()">
                                <svg width="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                    <circle cx="11" cy="11" r="8" stroke="currentcolor" stroke-width="1.5"></circle>
                                    <path d="21 21l-4.35-4.35" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                Filter
                            </button>
                        </div>
                    </div>

                    <!-- Payroll Table -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="payrollTable">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Pay Period</th>
                                    <th>Pay Date</th>
                                    <th>Gross Pay</th>
                                    <th>Deductions</th>
                                    <th>Net Pay</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($payrolls as $payroll): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($payroll['first_name'] . ' ' . $payroll['last_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($payroll['employee_code']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?= formatDate($payroll['pay_period_start']) ?><br>
                                        <small class="text-muted">to <?= formatDate($payroll['pay_period_end']) ?></small>
                                    </td>
                                    <td><?= formatDate($payroll['pay_date']) ?></td>
                                    <td><?= formatCurrency($payroll['gross_pay']) ?></td>
                                    <td><?= formatCurrency(($payroll['tax_deduction'] ?? 0) + ($payroll['social_security_deduction'] ?? 0) + ($payroll['other_deductions'] ?? 0)) ?></td>
                                    <td><strong><?= formatCurrency($payroll['net_pay']) ?></strong></td>
                                    <td>
                                        <span class="badge bg-<?= $payroll['status'] === 'approved' ? 'success' : 'warning' ?>">
                                            <?= ucfirst($payroll['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="flex align-items-center list-user-action">
                                            <a class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="View" href="show.php?id=<?= $payroll['id'] ?>">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill="currentColor" d="M9.76 14.37L12 12.13L14.24 14.37L15.65 12.96L13.41 10.72L15.65 8.48L14.24 7.07L12 9.31L9.76 7.07L8.35 8.48L10.59 10.72L8.35 12.96L9.76 14.37Z"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            
                                            <?php if (hasPermission('payroll.edit') && $payroll['status'] === 'draft'): ?>
                                            <a class="btn btn-sm btn-icon btn-warning" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Edit" href="edit.php?id=<?= $payroll['id'] ?>">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M11.4925 2.78906H7.75349C4.67849 2.78906 2.75049 4.96606 2.75049 8.04806V16.3621C2.75049 19.4441 4.66949 21.6211 7.75349 21.6211H16.5775C19.6625 21.6211 21.5815 19.4441 21.5815 16.3621V12.3341" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.82812 10.921L16.3011 3.44799C17.2321 2.51799 18.7411 2.51799 19.6721 3.44799L20.8891 4.66499C21.8201 5.59599 21.8201 7.10599 20.8891 8.03599L13.3801 15.545C12.9731 15.952 12.4211 16.181 11.8451 16.181H8.09912L8.19312 12.401C8.20712 11.845 8.43412 11.315 8.82812 10.921Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path d="M15.1655 4.60254L19.7315 9.16854" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (hasPermission('payroll.approve') && $payroll['status'] === 'draft'): ?>
                                            <a class="btn btn-sm btn-icon btn-success" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Approve" href="#" onclick="approvePayroll(<?= $payroll['id'] ?>)">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M11.3194 14.3209C11.1261 14.3219 10.9328 14.2523 10.7838 14.1091L8.86695 12.2656C8.57097 11.9793 8.56795 11.5145 8.86091 11.2262C9.15387 10.9369 9.63207 10.934 9.92906 11.2193L11.3083 12.5451L14.6758 9.22479C14.9698 8.93552 15.448 8.93258 15.744 9.21793C16.041 9.50426 16.044 9.97004 15.751 10.2574L11.8519 14.1022C11.7049 14.2474 11.5127 14.3199 11.3194 14.3209Z" fill="currentColor"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (hasPermission('payroll.delete') && $payroll['status'] === 'draft'): ?>
                                            <a class="btn btn-sm btn-icon btn-danger" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Delete" href="#" onclick="deletePayroll(<?= $payroll['id'] ?>, '<?= htmlspecialchars($payroll['first_name'] . ' ' . $payroll['last_name']) ?>')">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor">
                                                        <polyline points="3,6 5,6 21,6"></polyline>
                                                        <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                                                        <line x1="10" y1="11" x2="10" y2="17"></line>
                                                        <line x1="14" y1="11" x2="14" y2="17"></line>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['previous_page'] ?>&employee_id=<?= urlencode($_GET['employee_id'] ?? '') ?>&month=<?= urlencode($_GET['month'] ?? '') ?>&year=<?= urlencode($_GET['year'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>">Previous</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                            <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&employee_id=<?= urlencode($_GET['employee_id'] ?? '') ?>&month=<?= urlencode($_GET['month'] ?? '') ?>&year=<?= urlencode($_GET['year'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>"><?= $i ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['next_page'] ?>&employee_id=<?= urlencode($_GET['employee_id'] ?? '') ?>&month=<?= urlencode($_GET['month'] ?? '') ?>&year=<?= urlencode($_GET['year'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>">Next</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$additionalScripts = '
<script>
    // Apply filters
    function applyFilters() {
        const employee = document.getElementById("employeeFilter").value;
        const month = document.getElementById("monthFilter").value;
        const year = document.getElementById("yearFilter").value;
        const status = document.getElementById("statusFilter").value;
        
        const params = new URLSearchParams();
        if (employee) params.append("employee_id", employee);
        if (month) params.append("month", month);
        if (year) params.append("year", year);
        if (status) params.append("status", status);
        
        window.location.href = "?" + params.toString();
    }

    // Approve payroll
    function approvePayroll(id) {
        showConfirm("Are you sure you want to approve this payroll? This action cannot be undone.", function() {
            showLoading("Approving payroll...");
            
            fetch("../api/payroll/approve.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": CSRF_TOKEN
                },
                body: JSON.stringify({ 
                    id: id,
                    csrf_token: CSRF_TOKEN 
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert("success", data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert("error", data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert("error", "Failed to approve payroll");
            });
        });
    }

    // Delete payroll
    function deletePayroll(id, name) {
        showConfirm(`Are you sure you want to delete payroll for "${name}"?`, function() {
            showLoading("Deleting payroll...");
            
            fetch("../api/payroll.php", {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": CSRF_TOKEN
                },
                body: JSON.stringify({ 
                    id: id,
                    csrf_token: CSRF_TOKEN 
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert("success", data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert("error", data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert("error", "Failed to delete payroll");
            });
        });
    }
</script>
';

include __DIR__ . '/../../layouts/footer.php';
?>
