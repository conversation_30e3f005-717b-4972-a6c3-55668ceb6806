<?php
require_once 'config/config.php';

$auth = new Auth();

// Redirect if already logged in
if ($auth->isAuthenticated()) {
    redirect(BASE_URL . 'views/admin/dashboard.php');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password';
    } else {
        $result = $auth->login($username, $password, $rememberMe);
        
        if ($result['success']) {
            redirect(BASE_URL . '/views/admin/dashboard.php');
        } else {
            $error = $result['message'];
        }
    }
}

$csrfToken = generateCSRFToken();
?>
<!doctype html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Login - <?= APP_NAME ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="public/assets/images/favicon.ico" />
    
    <!-- Library / Plugin Css Build -->
    <link rel="stylesheet" href="public/assets/css/core/libs.min.css" />
    
    <!-- Hope Ui Design System Css -->
    <link rel="stylesheet" href="public/assets/css/hope-ui.min.css?v=2.0.0" />
    
    <!-- Custom Css -->
    <link rel="stylesheet" href="public/assets/css/custom.min.css?v=2.0.0" />
    
    <!-- Dark Css -->
    <link rel="stylesheet" href="public/assets/css/dark.min.css"/>
    
    <!-- Customizer Css -->
    <link rel="stylesheet" href="public/assets/css/customizer.min.css" />
    
    <!-- RTL Css -->
    <link rel="stylesheet" href="public/assets/css/rtl.min.css"/>
</head>

<body class="">
    <!-- loader Start -->
    <div id="loading">
        <div class="loader simple-loader">
            <div class="loader-body"></div>
        </div>
    </div>
    <!-- loader END -->

    <div class="wrapper">
        <section class="login-content">
            <div class="row m-0 align-items-center bg-white vh-100">
                <div class="col-md-6">
                    <div class="row justify-content-center">
                        <div class="col-md-10">
                            <div class="card card-transparent shadow-none d-flex justify-content-center mb-0 auth-card">
                                <div class="card-body">
                                    <a href="<?= BASE_URL ?>" class="navbar-brand d-flex align-items-center mb-3">
                                        <svg class="icon-30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect x="-0.757324" y="19.2427" width="28" height="4" rx="2" transform="rotate(-45 -0.757324 19.2427)" fill="currentColor"/>
                                            <rect x="7.72803" y="27.728" width="28" height="4" rx="2" transform="rotate(-45 7.72803 27.728)" fill="currentColor"/>
                                            <rect x="10.5366" y="16.3945" width="16" height="4" rx="2" transform="rotate(45 10.5366 16.3945)" fill="currentColor"/>
                                            <rect x="10.5562" y="-0.556152" width="28" height="4" rx="2" transform="rotate(45 10.5562 -0.556152)" fill="currentColor"/>
                                        </svg>
                                        <h4 class="logo-title ms-3"><?= APP_NAME ?></h4>
                                    </a>
                                    <h2 class="mb-2 text-center">Sign In</h2>
                                    <p class="text-center">Login to stay connected.</p>
                                    
                                    <?php if ($error): ?>
                                    <div class="alert alert-danger" role="alert">
                                        <div class="iq-alert-icon">
                                            <i class="icon">
                                                <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.67 2H16.34C19.73 2 22 4.38 22 7.92V16.091C22 19.62 19.73 22 16.34 22H7.67C4.28 22 2 19.62 2 16.091V7.92C2 4.38 4.28 2 7.67 2ZM11.43 14.99L16.18 10.24C16.52 9.9 16.52 9.35 16.18 9.01C15.84 8.67 15.28 8.67 14.94 9.01L10.81 13.14L9.06 11.39C8.72 11.05 8.16 11.05 7.82 11.39C7.48 11.73 7.48 12.28 7.82 12.62L10.2 14.99C10.37 15.16 10.59 15.24 10.81 15.24C11.04 15.24 11.26 15.16 11.43 14.99Z" fill="currentColor"></path>
                                                </svg>
                                            </i>
                                        </div>
                                        <div class="iq-alert-text"><?= htmlspecialchars($error) ?></div>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <form method="POST" action="">
                                        <input type="hidden" name="csrf_token" value="<?= $csrfToken ?>">
                                        
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="form-group">
                                                    <label for="username" class="form-label">Username</label>
                                                    <input type="text" class="form-control" id="username" name="username" 
                                                           value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" 
                                                           placeholder="Enter your username" required>
                                                </div>
                                            </div>
                                            <div class="col-lg-12">
                                                <div class="form-group">
                                                    <label for="password" class="form-label">Password</label>
                                                    <input type="password" class="form-control" id="password" name="password" 
                                                           placeholder="Enter your password" required>
                                                </div>
                                            </div>
                                            <div class="col-lg-12">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                                                        <label class="form-check-label" for="remember_me">
                                                            Remember Me
                                                        </label>
                                                    </div>
                                                    <a href="forgot-password.php">Forgot Password?</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-center">
                                            <button type="submit" class="btn btn-primary">Sign In</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sign-bg">
                        <svg width="280" height="230" viewBox="0 0 431 398" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g opacity="0.05">
                                <rect x="-157.085" y="193.773" width="543" height="77.5714" rx="38.7857" transform="rotate(-45 -157.085 193.773)" fill="#3B82F6"/>
                            </g>
                        </svg>
                    </div>
                </div>
                <div class="col-md-6 d-md-block d-none bg-primary p-0 mt-n1 vh-100 overflow-hidden">
                    <img src="public/assets/images/auth/01.png" class="img-fluid gradient-main animated-scaleX" alt="images">
                </div>
            </div>
        </section>
    </div>

    <!-- Library Bundle Script -->
    <script src="public/assets/js/core/libs.min.js"></script>
    
    <!-- External Library Bundle Script -->
    <script src="public/assets/js/core/external.min.js"></script>
    
    <!-- Widgetchart Script -->
    <script src="public/assets/js/charts/widgetcharts.js"></script>
    
    <!-- mapchart Script -->
    <script src="public/assets/js/charts/vectore-chart.js"></script>
    <script src="public/assets/js/charts/dashboard.js" ></script>
    
    <!-- fslightbox Script -->
    <script src="public/assets/js/plugins/fslightbox.js"></script>
    
    <!-- Settings Script -->
    <script src="public/assets/js/plugins/setting.js"></script>
    
    <!-- Slider-tab Script -->
    <script src="public/assets/js/plugins/slider-tabs.js"></script>
    
    <!-- Form Wizard Script -->
    <script src="public/assets/js/plugins/form-wizard.js"></script>
    
    <!-- AOS Animation Plugin-->
    <script src="public/assets/vendor/aos/dist/aos.js"></script>
    
    <!-- App Script -->
    <script src="public/assets/js/hope-ui.js" defer></script>
    
    <script>
        // Auto-focus on username field
        document.getElementById('username').focus();
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('Please enter both username and password');
                return false;
            }
        });
    </script>
</body>
</html>
