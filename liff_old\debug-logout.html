<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Logout - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .debug-log {
            background: #000;
            color: #0f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            font-size: 12px;
        }
        
        .error-log { color: #f00; }
        .success-log { color: #0f0; }
        .info-log { color: #ff0; }
        .warning-log { color: #ffa500; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">🔧 Debug Logout Issue</h1>
            <p class="text-white-50">แก้ไขปัญหา Logout ไม่ทำงาน</p>
        </div>
        
        <!-- Problem Analysis -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title text-danger">🚨 ปัญหาที่พบ</h5>
                <div class="alert alert-warning">
                    <strong>ปัญหา:</strong> กดปุ่ม logout ใน main app แต่ไม่มีอะไรเกิดขึ้น<br>
                    <strong>สาเหตุที่เป็นไปได้:</strong>
                    <ul class="mb-0">
                        <li>JavaScript function ไม่ได้ถูกเรียก</li>
                        <li>SweetAlert2 ไม่ได้ load</li>
                        <li>Function scope issues</li>
                        <li>Console errors</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Debug Tools -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🔍 Debug Tools</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>🧪 Test Functions:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="testLogoutFunction()">
                                🔧 Test Logout Function
                            </button>
                            <button class="btn btn-info" onclick="testSweetAlert()">
                                🍭 Test SweetAlert2
                            </button>
                            <button class="btn btn-warning" onclick="testFallbackLogout()">
                                🔄 Test Fallback Logout
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>📱 Test in Apps:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="openMainApp()">
                                🏠 Open Main App
                            </button>
                            <button class="btn btn-secondary" onclick="checkConsole()">
                                📋 Check Console
                            </button>
                            <button class="btn btn-danger" onclick="forceLogout()">
                                🚨 Force Logout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Console -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📋 Debug Console</h5>
                <div id="debugLogs" class="debug-log">
                    [Ready] Debug logout console initialized...
                </div>
                <div class="mt-2">
                    <button class="btn btn-secondary btn-sm" onclick="clearLogs()">
                        🗑️ Clear Logs
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Solutions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">💡 Solutions Applied</h5>
                
                <div class="alert alert-success">
                    <h6>✅ Fixes Applied:</h6>
                    <ul>
                        <li>✅ เพิ่ม console.log debug messages</li>
                        <li>✅ เพิ่ม SweetAlert2 availability check</li>
                        <li>✅ แยก performLogout function</li>
                        <li>✅ เพิ่ม handleLogout wrapper function</li>
                        <li>✅ เพิ่ม fallbackLogout function</li>
                        <li>✅ เปลี่ยน onclick เป็น handleLogout()</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6>🔧 How it works now:</h6>
                    <ol>
                        <li>กดปุ่ม logout → เรียก handleLogout()</li>
                        <li>handleLogout() → ตรวจสอบ logout function</li>
                        <li>หาก logout function มี → เรียก logout()</li>
                        <li>หาก logout function ไม่มี → เรียก fallbackLogout()</li>
                        <li>fallbackLogout() → ใช้ confirm() แทน SweetAlert2</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsDiv = document.getElementById('debugLogs');
            const colorClass = type === 'error' ? 'error-log' : type === 'success' ? 'success-log' : type === 'warning' ? 'warning-log' : 'info-log';
            logsDiv.innerHTML += `<div class="${colorClass}">${logEntry}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('debugLogs').innerHTML = '[Ready] Debug logout console cleared...';
        }
        
        // Initialize LIFF
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await liff.init({ liffId: LIFF_ID });
                
                if (liff.isLoggedIn()) {
                    const profile = await liff.getProfile();
                    log(`✅ LIFF initialized - User: ${profile.displayName}`, 'success');
                } else {
                    log('⚠️ User not logged in', 'warning');
                }
            } catch (error) {
                log(`❌ LIFF initialization failed: ${error.message}`, 'error');
            }
        });
        
        function testLogoutFunction() {
            log('🧪 Testing logout function...', 'info');
            
            try {
                // Check if logout function exists
                if (typeof logout === 'function') {
                    log('✅ logout function exists', 'success');
                    log('🔧 Calling logout function...', 'info');
                    logout();
                } else {
                    log('❌ logout function not found', 'error');
                    log('💡 This means app.js is not loaded or function is not in global scope', 'warning');
                }
            } catch (error) {
                log(`❌ Error testing logout function: ${error.message}`, 'error');
            }
        }
        
        function testSweetAlert() {
            log('🍭 Testing SweetAlert2...', 'info');
            
            try {
                if (typeof Swal === 'undefined') {
                    log('❌ SweetAlert2 not loaded', 'error');
                    return;
                }
                
                log('✅ SweetAlert2 is available', 'success');
                
                Swal.fire({
                    title: 'SweetAlert2 Test',
                    text: 'SweetAlert2 ทำงานได้ปกติ',
                    icon: 'success',
                    confirmButtonText: 'เยี่ยม!'
                }).then(() => {
                    log('✅ SweetAlert2 dialog completed', 'success');
                });
                
            } catch (error) {
                log(`❌ SweetAlert2 test error: ${error.message}`, 'error');
            }
        }
        
        function testFallbackLogout() {
            log('🔄 Testing fallback logout...', 'info');
            
            try {
                if (confirm('ทดสอบ fallback logout - ออกจากระบบหรือไม่?')) {
                    log('✅ User confirmed fallback logout', 'success');
                    log('🔧 Performing fallback logout operations...', 'info');
                    
                    // Simulate logout operations
                    log('📝 Clearing local data...', 'info');
                    log('🚪 Hiding main content...', 'info');
                    log('📱 LIFF logout...', 'info');
                    log('🔄 Redirecting...', 'info');
                    
                    log('✅ Fallback logout test completed', 'success');
                } else {
                    log('ℹ️ User cancelled fallback logout', 'info');
                }
            } catch (error) {
                log(`❌ Fallback logout test error: ${error.message}`, 'error');
            }
        }
        
        function openMainApp() {
            log('🏠 Opening main app for testing...', 'info');
            window.open('index.html', '_blank');
        }
        
        function checkConsole() {
            log('📋 Checking console...', 'info');
            log('💡 Please open browser console (F12) and check for errors', 'warning');
            log('💡 Look for: "handleLogout called", "logout function", errors', 'warning');
            
            // Test console logging
            console.log('=== LOGOUT DEBUG TEST ===');
            console.log('This message should appear in browser console');
            console.log('Check if logout-related messages appear when clicking logout button');
            
            log('✅ Console check messages sent', 'success');
        }
        
        function forceLogout() {
            log('🚨 Force logout...', 'info');
            
            try {
                if (confirm('Force logout - ออกจากระบบทันทีหรือไม่?')) {
                    log('✅ User confirmed force logout', 'success');
                    
                    // Force logout operations
                    if (typeof liff !== 'undefined' && liff.isLoggedIn()) {
                        liff.logout();
                        log('📱 LIFF logout performed', 'success');
                    }
                    
                    if (typeof liff !== 'undefined' && liff.isInClient()) {
                        liff.closeWindow();
                        log('🚪 LIFF window closed', 'success');
                    } else {
                        window.location.reload();
                        log('🔄 Page reloaded', 'success');
                    }
                }
            } catch (error) {
                log(`❌ Force logout error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
