<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple APIs - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .celebration {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #28a745;
        }
        
        .success-card {
            border-left: 4px solid #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Celebration Header -->
        <div class="celebration">
            <h1 class="text-success">🔧 Simple API Test</h1>
            <h3 class="text-primary">ทดสอบ APIs แบบง่าย (ไม่ใช้ complex models)</h3>
            <p class="text-muted">แก้ไขปัญหา "Unexpected token" ด้วย static data</p>
        </div>
        
        <!-- Problem & Solution -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">✅ การแก้ไขปัญหา</h5>
                <div class="alert alert-warning">
                    <h6>🚨 Root Cause:</h6>
                    <ul>
                        <li>❌ Leave model มี PHP errors</li>
                        <li>❌ Complex database operations ทำให้เกิด warnings</li>
                        <li>❌ Missing dependencies หรือ autoloader issues</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h6>✅ Solution:</h6>
                    <ul>
                        <li>✅ สร้าง Simple API ด้วย static data</li>
                        <li>✅ ไม่ใช้ complex models</li>
                        <li>✅ Enhanced output buffering</li>
                        <li>✅ Pure JSON responses</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Simple API Tests -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 Simple API Tests</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>📝 Leave APIs:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="testSimpleLeaveTypes()">
                                📋 Simple Leave Types
                            </button>
                            <button class="btn btn-success" onclick="testSimpleLeaveHistory()">
                                📊 Simple Leave History
                            </button>
                            <button class="btn btn-warning" onclick="testSimpleAPI()">
                                🔧 Simple API Test
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>💰 Payroll APIs:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-info" onclick="testSimplePayslip()">
                                💳 Simple Payslip
                            </button>
                            <button class="btn btn-secondary" onclick="testSimplePayrollHistory()">
                                📈 Simple Payroll History
                            </button>
                            <button class="btn btn-dark" onclick="runSimpleSystemTest()">
                                🚀 Simple System Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Expected Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🎯 Expected Results</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-success">
                            <h6>✅ Leave Types:</h6>
                            <pre style="font-size: 12px;">
{
  "success": true,
  "data": [
    {
      "id": 1,
      "type_name": "ลาพักผ่อน",
      "max_days_per_year": 6,
      "is_paid": true
    }
  ]
}
                            </pre>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6>✅ Payslip:</h6>
                            <pre style="font-size: 12px;">
{
  "success": true,
  "data": {
    "base_salary": 45000.00,
    "net_pay": 45175.00,
    "pay_period_start": "2024-08-01"
  }
}
                            </pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 Test Results</h5>
                <div id="testResults">
                    <p class="text-muted">ผลการทดสอบจะแสดงที่นี่...</p>
                </div>
            </div>
        </div>
        
        <!-- Next Steps -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🚀 Next Steps</h5>
                
                <div class="alert alert-success">
                    <h6>✅ หลังจากทดสอบ Simple APIs สำเร็จ:</h6>
                    <ol>
                        <li>Upload simple-test.php ไปยัง production</li>
                        <li>ทดสอบ simple APIs ใน production</li>
                        <li>อัปเดต main app ให้ใช้ simple APIs</li>
                        <li>ทดสอบ complete system flow</li>
                        <li>Deploy และใช้งานจริง</li>
                    </ol>
                </div>
                
                <div class="alert alert-info">
                    <h6>🔧 API Endpoints (Simple):</h6>
                    <ul class="mb-0">
                        <li><code>GET /api/debug/simple-test.php?action=leave-types</code></li>
                        <li><code>GET /api/debug/simple-test.php?action=leave-history</code></li>
                        <li><code>GET /api/debug/simple-test.php?action=payslip</code></li>
                        <li><code>GET /api/debug/simple-test.php?action=payroll-history</code></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
            
            const resultHtml = `
                <div class="alert ${alertClass} py-2">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('ผลการทดสอบจะแสดงที่นี่')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML += resultHtml;
            }
        }
        
        // Simple API Tests
        async function testSimpleAPI() {
            addResult('🧪 Testing simple API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/simple-test.php?action=test`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Simple API works! PHP ${data.php_version}`, 'success');
                    addResult(`📋 Available actions: ${data.available_actions.join(', ')}`, 'info');
                } else {
                    addResult(`❌ Simple API failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Simple API error: ${error.message}`, 'error');
            }
        }
        
        async function testSimpleLeaveTypes() {
            addResult('🧪 Testing simple leave types...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/simple-test.php?action=leave-types`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Leave Types: Found ${data.data.length} types`, 'success');
                    addResult(`📋 Types: ${data.data.map(t => t.type_name).join(', ')}`, 'info');
                } else {
                    addResult(`❌ Leave Types failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Simple Leave Types error: ${error.message}`, 'error');
            }
        }
        
        async function testSimpleLeaveHistory() {
            addResult('🧪 Testing simple leave history...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/simple-test.php?action=leave-history`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Leave History: Found ${data.data.length} requests`, 'success');
                    addResult(`📊 Statistics: ${data.statistics.length} leave types tracked`, 'info');
                } else {
                    addResult(`❌ Leave History failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Simple Leave History error: ${error.message}`, 'error');
            }
        }
        
        async function testSimplePayslip() {
            addResult('🧪 Testing simple payslip...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/simple-test.php?action=payslip`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Payslip: ${data.data.pay_period_start} to ${data.data.pay_period_end}`, 'success');
                    addResult(`💰 Net Pay: ฿${Number(data.data.net_pay).toLocaleString()}`, 'info');
                    addResult(`👤 Employee: ${data.employee.name} (${data.employee.position})`, 'info');
                } else {
                    addResult(`❌ Payslip failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Simple Payslip error: ${error.message}`, 'error');
            }
        }
        
        async function testSimplePayrollHistory() {
            addResult('🧪 Testing simple payroll history...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/debug/simple-test.php?action=payroll-history`);
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Payroll History: Found ${data.data.length} records`, 'success');
                    if (data.data.length > 0) {
                        addResult(`📈 Latest: ฿${Number(data.data[0].net_pay).toLocaleString()} (${data.data[0].pay_period_start})`, 'info');
                    }
                } else {
                    addResult(`❌ Payroll History failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Simple Payroll History error: ${error.message}`, 'error');
            }
        }
        
        async function runSimpleSystemTest() {
            addResult('🚀 Running simple system test...', 'info');
            
            const tests = [
                { name: 'API Test', func: testSimpleAPI },
                { name: 'Leave Types', func: testSimpleLeaveTypes },
                { name: 'Leave History', func: testSimpleLeaveHistory },
                { name: 'Payslip', func: testSimplePayslip },
                { name: 'Payroll History', func: testSimplePayrollHistory }
            ];
            
            let successCount = 0;
            
            for (const test of tests) {
                addResult(`🔄 Testing ${test.name}...`, 'info');
                
                try {
                    await test.func();
                    successCount++;
                } catch (error) {
                    addResult(`❌ ${test.name} failed: ${error.message}`, 'error');
                }
                
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            addResult(`✅ Simple system test completed! ${successCount}/${tests.length} tests passed`, 'success');
            
            if (successCount === tests.length) {
                await Swal.fire({
                    icon: 'success',
                    title: 'All Tests Passed!',
                    html: `
                        <div class="text-center">
                            <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                            <h4>Simple APIs ทำงานได้สมบูรณ์!</h4>
                            <p>พร้อม integrate กับ main app</p>
                        </div>
                    `,
                    confirmButtonText: 'เยี่ยมมาก!'
                });
            } else {
                await Swal.fire({
                    icon: 'warning',
                    title: 'Some Tests Failed',
                    text: `${successCount}/${tests.length} tests passed. Check logs for details.`,
                    confirmButtonText: 'ตรวจสอบ'
                });
            }
        }
        
        // Auto-run basic test on load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testSimpleAPI();
            }, 1000);
        });
    </script>
</body>
</html>
