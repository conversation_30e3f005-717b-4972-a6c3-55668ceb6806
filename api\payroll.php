<?php
require_once '../config/config.php';

header('Content-Type: application/json');

$auth = new Auth();
if (!$auth->isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));
$payrollId = isset($pathParts[2]) ? (int)$pathParts[2] : null;

$payrollController = new PayrollController();

try {
    switch ($method) {
        case 'GET':
            if ($payrollId) {
                // Get single payroll
                requirePermission('payroll.view');
                $payrollController->getData();
            } else {
                // Get all payrolls (for AJAX table)
                requirePermission('payroll.view');
                $payrollModel = new Payroll();
                
                $page = $_GET['page'] ?? 1;
                $employeeId = $_GET['employee_id'] ?? null;
                $month = $_GET['month'] ?? null;
                $year = $_GET['year'] ?? date('Y');
                
                $limit = RECORDS_PER_PAGE;
                $offset = ($page - 1) * $limit;
                
                $payrolls = $payrollModel->getAll($limit, $offset, $employeeId, $month, $year);
                $totalRecords = $payrollModel->count($employeeId, $month, $year);
                $pagination = paginate($totalRecords, $page, $limit);
                
                echo json_encode([
                    'success' => true,
                    'data' => $payrolls,
                    'pagination' => $pagination
                ]);
            }
            break;
            
        case 'POST':
            requirePermission('payroll.create');
            $_POST['ajax'] = true;
            $payrollController->store();
            break;
            
        case 'PUT':
            requirePermission('payroll.edit');
            
            // Parse PUT data
            $putData = [];
            parse_str(file_get_contents('php://input'), $putData);
            $_POST = array_merge($_POST, $putData);
            $_POST['ajax'] = true;
            
            if ($payrollId) {
                $payrollController->update($payrollId);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Payroll ID required']);
            }
            break;
            
        case 'DELETE':
            requirePermission('payroll.delete');
            
            $input = json_decode(file_get_contents('php://input'), true);
            $_POST = $input;
            
            $payrollController->delete();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
