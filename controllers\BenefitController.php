<?php
/**
 * Benefit Controller
 * Human Resources Center System
 */

class BenefitController {
    private $benefitModel;
    private $employeeModel;
    private $logger;
    
    public function __construct() {
        $this->benefitModel = new Benefit();
        $this->employeeModel = new Employee();
        $this->logger = new Logger();
        
        // Require authentication
        requireLogin();
    }
    
    /**
     * List benefits
     */
    public function index() {
        requirePermission('benefits.view');
        
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? null;
        $type = $_GET['type'] ?? null;
        
        $limit = RECORDS_PER_PAGE;
        $offset = ($page - 1) * $limit;
        
        $benefits = $this->benefitModel->getAll($limit, $offset, $search, $type);
        $totalRecords = $this->benefitModel->count($search, $type);
        $pagination = paginate($totalRecords, $page, $limit);
        
        // Get benefit types for filter
        $benefitTypes = ['health', 'insurance', 'allowance', 'bonus', 'other'];
        
        // Get statistics
        $stats = [
            'total_benefits' => $this->benefitModel->count(),
            'active_benefits' => $this->getActiveBenefitsCount(),
            'health_benefits' => $this->benefitModel->count(null, 'health'),
            'insurance_benefits' => $this->benefitModel->count(null, 'insurance')
        ];
        
        include __DIR__ . '/../views/admin/benefits/index.php';
    }
    
    /**
     * Show benefit details
     */
    public function show() {
        requirePermission('benefits.view');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            setFlashMessage('error', 'Benefit ID is required');
            redirect(BASE_URL . '/admin/benefits/index.php');
        }
        
        $benefit = $this->benefitModel->getById($id);
        if (!$benefit) {
            setFlashMessage('error', 'Benefit not found');
            redirect(BASE_URL . '/admin/benefits/index.php');
        }
        
        include __DIR__ . '/../views/admin/benefits/show.php';
    }
    
    /**
     * Create benefit
     */
    public function create() {
        requirePermission('benefits.create');
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->store();
        }
        
        include __DIR__ . '/../views/admin/benefits/create.php';
    }
    
    /**
     * Store new benefit
     */
    public function store() {
        requirePermission('benefits.create');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        // Validate input
        $validator = new Validator($_POST);
        $validator->required('name', 'Benefit name is required')
                 ->required('type', 'Benefit type is required')
                 ->numeric('amount', 'Amount must be a number');
        
        if ($validator->fails()) {
            if (isset($_POST['ajax'])) {
                errorResponse('Validation failed', 400);
            }
            
            setFlashMessage('error', 'Please correct the errors below');
            $errors = $validator->getErrors();
            include __DIR__ . '/../views/admin/benefits/create.php';
            return;
        }
        
        // Prepare data
        $data = [
            'name' => $_POST['name'],
            'type' => $_POST['type'],
            'description' => $_POST['description'] ?? null,
            'amount' => floatval($_POST['amount'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        $result = $this->benefitModel->create($data);
        
        if (isset($_POST['ajax'])) {
            if ($result['success']) {
                successResponse($result['message'], ['id' => $result['id']]);
            } else {
                errorResponse($result['message'], 400);
            }
        }
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
            redirect(BASE_URL . '/admin/benefits/index.php');
        } else {
            setFlashMessage('error', $result['message']);
            include __DIR__ . '/../views/admin/benefits/create.php';
        }
    }
    
    /**
     * Edit benefit
     */
    public function edit() {
        requirePermission('benefits.edit');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            setFlashMessage('error', 'Benefit ID is required');
            redirect(BASE_URL . '/admin/benefits/index.php');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->update($id);
        }
        
        $benefit = $this->benefitModel->getById($id);
        if (!$benefit) {
            setFlashMessage('error', 'Benefit not found');
            redirect(BASE_URL . '/admin/benefits/index.php');
        }
        
        include __DIR__ . '/../views/admin/benefits/edit.php';
    }
    
    /**
     * Update benefit
     */
    public function update($id) {
        requirePermission('benefits.edit');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        // Validate input
        $validator = new Validator($_POST);
        $validator->required('name', 'Benefit name is required')
                 ->required('type', 'Benefit type is required')
                 ->numeric('amount', 'Amount must be a number');
        
        if ($validator->fails()) {
            if (isset($_POST['ajax'])) {
                errorResponse('Validation failed', 400);
            }
            
            setFlashMessage('error', 'Please correct the errors below');
            $errors = $validator->getErrors();
            $benefit = $this->benefitModel->getById($id);
            include __DIR__ . '/../views/admin/benefits/edit.php';
            return;
        }
        
        // Prepare data
        $data = [
            'name' => $_POST['name'],
            'type' => $_POST['type'],
            'description' => $_POST['description'] ?? null,
            'amount' => floatval($_POST['amount'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        $result = $this->benefitModel->update($id, $data);
        
        if (isset($_POST['ajax'])) {
            if ($result['success']) {
                successResponse($result['message']);
            } else {
                errorResponse($result['message'], 400);
            }
        }
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
            redirect(BASE_URL . '/admin/benefits/index.php');
        } else {
            setFlashMessage('error', $result['message']);
            $benefit = $this->benefitModel->getById($id);
            include __DIR__ . '/../views/admin/benefits/edit.php';
        }
    }
    
    /**
     * Delete benefit
     */
    public function delete() {
        requirePermission('benefits.delete');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $id = $_POST['id'] ?? null;
        if (!$id) {
            errorResponse('Benefit ID is required', 400);
        }
        
        $result = $this->benefitModel->delete($id);
        
        if ($result['success']) {
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Assign benefit to employee
     */
    public function assignToEmployee() {
        requirePermission('benefits.assign');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $employeeId = $_POST['employee_id'] ?? null;
        $benefitId = $_POST['benefit_id'] ?? null;
        $startDate = $_POST['start_date'] ?? null;
        $endDate = $_POST['end_date'] ?? null;
        
        if (!$employeeId || !$benefitId) {
            errorResponse('Employee ID and Benefit ID are required', 400);
        }
        
        $result = $this->benefitModel->assignToEmployee($employeeId, $benefitId, $startDate, $endDate);
        
        if ($result['success']) {
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Remove benefit from employee
     */
    public function removeFromEmployee() {
        requirePermission('benefits.assign');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $employeeId = $_POST['employee_id'] ?? null;
        $benefitId = $_POST['benefit_id'] ?? null;
        
        if (!$employeeId || !$benefitId) {
            errorResponse('Employee ID and Benefit ID are required', 400);
        }
        
        $result = $this->benefitModel->removeFromEmployee($employeeId, $benefitId);
        
        if ($result['success']) {
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Get benefit data for AJAX
     */
    public function getData() {
        requirePermission('benefits.view');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            errorResponse('Benefit ID is required', 400);
        }
        
        $benefit = $this->benefitModel->getById($id);
        if (!$benefit) {
            errorResponse('Benefit not found', 404);
        }
        
        successResponse('Benefit data retrieved', $benefit);
    }
    
    /**
     * Get active benefits count
     */
    private function getActiveBenefitsCount() {
        try {
            $database = new Database();
            $pdo = $database->getConnection();

            $sql = "SELECT COUNT(*) FROM benefits WHERE is_active = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();

            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            error_log("Get active benefits count error: " . $e->getMessage());
            return 0;
        }
    }
}
