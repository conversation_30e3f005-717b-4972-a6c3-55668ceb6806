<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SweetAlert2 Logout - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .feature-card {
            border-left: 4px solid #667eea;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .celebration {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #667eea;
        }
        
        .demo-logout {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 2px solid #667eea;
        }
        
        .btn-logout-demo {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-logout-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Celebration Header -->
        <div class="celebration">
            <h1 class="text-primary">🍭 SweetAlert2 Logout! 🍭</h1>
            <h3 class="text-info">การยืนยันการออกจากระบบแบบสวยงาม</h3>
            <p class="text-muted">เปลี่ยนจาก browser confirm ธรรมดาเป็น SweetAlert2</p>
        </div>
        
        <!-- Features Overview -->
        <div class="card feature-card">
            <div class="card-body">
                <h5 class="card-title text-primary">✨ SweetAlert2 Features</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>🎨 Visual Enhancements:</h6>
                        <ul>
                            <li>✅ Beautiful confirmation dialog</li>
                            <li>✅ Custom icons and colors</li>
                            <li>✅ Animated loading spinner</li>
                            <li>✅ Progress bar timer</li>
                            <li>✅ Custom button styling</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🔧 UX Improvements:</h6>
                        <ul>
                            <li>✅ Reverse button order (Cancel first)</li>
                            <li>✅ Focus on cancel button</li>
                            <li>✅ Prevent outside click</li>
                            <li>✅ Enhanced error messages</li>
                            <li>✅ Smooth animations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Demo Section -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 SweetAlert2 Logout Demo</h5>
                
                <div class="demo-logout">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <img src="https://via.placeholder.com/60" alt="Profile" style="width: 60px; height: 60px; border-radius: 50%; margin-right: 15px;">
                            <div>
                                <h6 class="mb-1">Jane Smith</h6>
                                <p class="mb-0 text-muted">HR Manager</p>
                                <small class="text-muted">EMP002</small>
                            </div>
                        </div>
                        <button class="btn btn-logout-demo btn-sm" onclick="demoSweetAlertLogout()" title="ออกจากระบบ">
                            <i class="fas fa-sign-out-alt me-1"></i>ออกจากระบบ
                        </button>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        กดปุ่ม "ออกจากระบบ" เพื่อดู SweetAlert2 ที่สวยงาม
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-4">
                        <h6>🎭 Demo Variations:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="demoSweetAlertLogout()">
                                🍭 Standard SweetAlert2
                            </button>
                            <button class="btn btn-success" onclick="demoEnhancedLogout()">
                                ✨ Enhanced Version
                            </button>
                            <button class="btn btn-warning" onclick="demoMinimalLogout()">
                                🎯 Minimal Version
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>📱 App Tests:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-info" onclick="openMainApp()">
                                🏠 Test Main App
                            </button>
                            <button class="btn btn-secondary" onclick="openRegistration()">
                                👤 Test Registration
                            </button>
                            <button class="btn btn-dark" onclick="compareVersions()">
                                🔄 Compare Versions
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>🔧 Fallback Tests:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="testFallback()">
                                🔄 Test Fallback
                            </button>
                            <button class="btn btn-outline-success" onclick="testBrowserConfirm()">
                                📋 Browser Confirm
                            </button>
                            <button class="btn btn-outline-danger" onclick="testErrorHandling()">
                                ⚠️ Error Handling
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Comparison -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 Before vs After</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <h6>❌ Before (Browser Confirm):</h6>
                            <ul class="mb-0">
                                <li>ธรรมดา ไม่สวยงาม</li>
                                <li>ไม่สามารถปรับแต่งได้</li>
                                <li>ไม่มี loading animation</li>
                                <li>ไม่มี success message</li>
                                <li>UX ไม่ดี</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-success">
                            <h6>✅ After (SweetAlert2):</h6>
                            <ul class="mb-0">
                                <li>สวยงาม มีสีสัน</li>
                                <li>ปรับแต่งได้ทุกอย่าง</li>
                                <li>มี loading animation</li>
                                <li>มี success message</li>
                                <li>UX ดีเยี่ยม</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 Test Results</h5>
                <div id="testResults">
                    <p class="text-muted">ผลการทดสอบจะแสดงที่นี่...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
            
            const resultHtml = `
                <div class="alert ${alertClass} py-2">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('ผลการทดสอบจะแสดงที่นี่')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML += resultHtml;
            }
        }
        
        // Standard SweetAlert2 logout
        async function demoSweetAlertLogout() {
            addResult('🍭 Testing standard SweetAlert2 logout...', 'info');
            
            try {
                const result = await Swal.fire({
                    title: 'ออกจากระบบ?',
                    text: 'คุณต้องการออกจากระบบ HR Center หรือไม่?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: '<i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ',
                    cancelButtonText: '<i class="fas fa-times me-2"></i>ยกเลิก',
                    reverseButtons: true,
                    focusCancel: true
                });
                
                if (result.isConfirmed) {
                    addResult('✅ User confirmed logout', 'success');
                    
                    Swal.fire({
                        title: 'กำลังออกจากระบบ...',
                        html: '<i class="fas fa-spinner fa-spin fa-2x text-primary"></i><br><br>กรุณารอสักครู่...',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    await Swal.fire({
                        icon: 'success',
                        title: 'ออกจากระบบแล้ว',
                        text: 'คุณได้ออกจากระบบ HR Center เรียบร้อยแล้ว',
                        timer: 2000,
                        timerProgressBar: true,
                        showConfirmButton: false
                    });
                    
                    addResult('✅ Standard SweetAlert2 logout completed', 'success');
                } else {
                    addResult('ℹ️ User cancelled logout', 'info');
                }
                
            } catch (error) {
                addResult(`❌ SweetAlert2 logout error: ${error.message}`, 'error');
            }
        }
        
        // Enhanced SweetAlert2 logout
        async function demoEnhancedLogout() {
            addResult('✨ Testing enhanced SweetAlert2 logout...', 'info');
            
            try {
                const result = await Swal.fire({
                    title: 'ออกจากระบบ?',
                    html: `
                        <div class="text-center">
                            <i class="fas fa-sign-out-alt fa-3x text-danger mb-3"></i>
                            <p class="mb-0">คุณต้องการออกจากระบบ HR Center หรือไม่?</p>
                        </div>
                    `,
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: '<i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ',
                    cancelButtonText: '<i class="fas fa-times me-2"></i>ยกเลิก',
                    reverseButtons: true,
                    focusCancel: true,
                    customClass: {
                        popup: 'animate__animated animate__fadeIn',
                        confirmButton: 'btn btn-danger',
                        cancelButton: 'btn btn-secondary'
                    },
                    buttonsStyling: false
                });
                
                if (result.isConfirmed) {
                    addResult('✅ User confirmed enhanced logout', 'success');
                    
                    Swal.fire({
                        title: 'กำลังออกจากระบบ...',
                        html: `
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <br>
                                <p class="mb-0">กรุณารอสักครู่...</p>
                            </div>
                        `,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false
                    });
                    
                    await new Promise(resolve => setTimeout(resolve, 2500));
                    
                    await Swal.fire({
                        html: `
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                                <h4 class="text-success">ออกจากระบบแล้ว</h4>
                                <p class="mb-0">คุณได้ออกจากระบบ HR Center เรียบร้อยแล้ว</p>
                            </div>
                        `,
                        timer: 3000,
                        timerProgressBar: true,
                        showConfirmButton: false,
                        customClass: {
                            popup: 'animate__animated animate__fadeIn'
                        }
                    });
                    
                    addResult('✅ Enhanced SweetAlert2 logout completed', 'success');
                }
                
            } catch (error) {
                addResult(`❌ Enhanced logout error: ${error.message}`, 'error');
            }
        }
        
        // Minimal SweetAlert2 logout
        async function demoMinimalLogout() {
            addResult('🎯 Testing minimal SweetAlert2 logout...', 'info');
            
            try {
                const result = await Swal.fire({
                    title: 'ออกจากระบบ?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'ออกจากระบบ',
                    cancelButtonText: 'ยกเลิก'
                });
                
                if (result.isConfirmed) {
                    await Swal.fire('ออกจากระบบแล้ว!', '', 'success');
                    addResult('✅ Minimal SweetAlert2 logout completed', 'success');
                }
                
            } catch (error) {
                addResult(`❌ Minimal logout error: ${error.message}`, 'error');
            }
        }
        
        function testBrowserConfirm() {
            addResult('📋 Testing browser confirm (old way)...', 'info');
            
            if (confirm('ออกจากระบบ HR Center หรือไม่? (Browser Confirm)')) {
                addResult('✅ Browser confirm works (but ugly)', 'success');
            } else {
                addResult('ℹ️ User cancelled browser confirm', 'info');
            }
        }
        
        async function compareVersions() {
            addResult('🔄 Comparing logout versions...', 'info');
            
            const choice = await Swal.fire({
                title: 'เลือกเวอร์ชันที่ต้องการทดสอบ',
                showDenyButton: true,
                showCancelButton: true,
                confirmButtonText: 'SweetAlert2',
                denyButtonText: 'Browser Confirm',
                cancelButtonText: 'ยกเลิก'
            });
            
            if (choice.isConfirmed) {
                demoSweetAlertLogout();
            } else if (choice.isDenied) {
                testBrowserConfirm();
            }
        }
        
        function openMainApp() {
            addResult('🏠 Opening main app...', 'info');
            window.open('index.html', '_blank');
        }
        
        function openRegistration() {
            addResult('👤 Opening registration page...', 'info');
            window.open('register.html', '_blank');
        }
        
        async function testFallback() {
            addResult('🔄 Testing fallback mechanism...', 'info');
            addResult('✅ Fallback now uses SweetAlert2 instead of browser confirm', 'success');
        }
        
        async function testErrorHandling() {
            addResult('⚠️ Testing error handling...', 'info');
            
            await Swal.fire({
                icon: 'error',
                title: 'เกิดข้อผิดพลาด',
                html: `
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                        <br>
                        <p class="mb-0">ไม่สามารถออกจากระบบได้ กรุณาลองใหม่อีกครั้ง</p>
                    </div>
                `,
                confirmButtonText: '<i class="fas fa-redo me-2"></i>ลองใหม่'
            });
            
            addResult('✅ Error handling with SweetAlert2 works', 'success');
        }
    </script>
</body>
</html>
