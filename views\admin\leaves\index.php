<?php
require_once __DIR__ . '/../../../config/config.php';

$auth = new Auth();

// Require authentication
if (!$auth->isAuthenticated()) {
    redirect(BASE_URL . '/login.php');
}

$currentUser = $auth->getCurrentUser();
$pageTitle = 'Leave Management';
include __DIR__ . '/../../layouts/header.php';
?>

<div class="conatiner-fluid content-inner mt-n5 py-0">
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Pending Requests</span>
                            <h4 class="mb-0" id="pendingCount"><?= (new LeaveRequest())->count(null, 'pending') ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-warning-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Approved</span>
                            <h4 class="mb-0" id="approvedCount"><?= (new LeaveRequest())->count(null, 'approved') ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-success-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M12.0865 22C11.9627 22 11.8388 21.9716 11.7271 21.9137L8.12599 20.0496C7.10415 19.5201 6.30481 18.9259 5.68063 18.2336C4.31449 16.7195 3.5544 14.776 3.54232 12.7599L3.50004 6.12426C3.495 5.35842 3.98931 4.67103 4.72826 4.41215L11.3405 2.10679C11.7331 1.96656 12.1711 1.9646 12.5707 2.09992L19.2081 4.32684C19.9511 4.57493 20.4535 5.25742 20.4575 6.02228L20.4998 12.6628C20.5129 14.676 19.779 16.6274 18.434 18.1581C17.8168 18.8602 17.0245 19.4632 16.0128 20.0025L12.4439 21.9088C12.3331 21.9686 12.2103 21.999 12.0865 22Z" fill="currentColor"></path>
                                <path d="M11.3194 14.3209C11.1261 14.3219 10.9328 14.2523 10.7838 14.1091L8.86695 12.2656C8.57097 11.9793 8.56795 11.5145 8.86091 11.2262C9.15387 10.9369 9.63207 10.934 9.92906 11.2193L11.3083 12.5451L14.6758 9.22479C14.9698 8.93552 15.448 8.93258 15.744 9.21793C16.041 9.50426 16.044 9.97004 15.751 10.2574L11.8519 14.1022C11.7049 14.2474 11.5127 14.3199 11.3194 14.3209Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Rejected</span>
                            <h4 class="mb-0" id="rejectedCount"><?= (new LeaveRequest())->count(null, 'rejected') ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-danger-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.7688 8.71387H16.2312C18.5886 8.71387 20.5 10.5831 20.5 12.8885V17.8254C20.5 20.1308 18.5886 22 16.2312 22H7.7688C5.41136 22 3.5 20.1308 3.5 17.8254V12.8885C3.5 10.5831 5.41136 8.71387 7.7688 8.71387Z" fill="currentColor"></path>
                                <path opacity="0.4" d="M11.9955 17.8842C11.4673 17.8842 11.0376 17.4561 11.0376 16.9306V14.2619C11.0376 13.7364 11.4673 13.3083 11.9955 13.3083C12.5238 13.3083 12.9535 13.7364 12.9535 14.2619V16.9306C12.9535 17.4561 12.5238 17.8842 11.9955 17.8842Z" fill="currentColor"></path>
                                <path opacity="0.4" d="M11.9955 11.3589C11.4673 11.3589 11.0376 10.9308 11.0376 10.4053C11.0376 9.87977 11.4673 9.45166 11.9955 9.45166C12.5238 9.45166 12.9535 9.87977 12.9535 10.4053C12.9535 10.9308 12.5238 11.3589 11.9955 11.3589Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">Total Requests</span>
                            <h4 class="mb-0" id="totalCount"><?= $totalRecords ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-info-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M13.3051 5.88243V6.06547C12.8144 6.05584 12.3237 6.05584 11.8331 6.05584V5.89206C11.8331 5.22733 11.2737 4.68784 10.6064 4.68784H9.63482C8.52589 4.68784 7.62305 3.80152 7.62305 2.72254C7.62305 2.32755 7.95671 2 8.35906 2C8.77123 2 9.09508 2.32755 9.09508 2.72254C9.09508 3.01155 9.34042 3.24276 9.63482 3.24276H10.6064C12.0882 3.2524 13.2953 4.43736 13.3051 5.88243Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M15.164 6.08279C15.4791 6.08712 15.7949 6.09145 16.1119 6.09469C19.5172 6.09469 22 8.52241 22 11.875V16.1813C22 19.5339 19.5172 21.9616 16.1119 21.9616C14.7478 21.9905 13.3837 22.0001 12.0098 22.0001C10.6359 22.0001 9.25221 21.9905 7.88813 21.9616C4.48283 21.9616 2 19.5339 2 16.1813V11.875C2 8.52241 4.48283 6.09469 7.89794 6.09469C9.18351 6.07542 10.4985 6.05615 11.8332 6.05615C12.3238 6.05615 12.8145 6.05615 13.3052 6.06579C13.9238 6.06579 14.5425 6.07427 15.164 6.08279Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">Leave Requests</h4>
                    </div>
                    <div class="header-action">
                        <?php if (hasPermission('leave.create')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#leaveModal">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path opacity="0.4" d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2Z" fill="currentColor"></path>
                            </svg>
                            Add Leave Request
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="form-group">
                                <select class="form-select" id="employeeFilter">
                                    <option value="">All Employees</option>
                                    <?php foreach ($employees as $emp): ?>
                                    <option value="<?= $emp['id'] ?>" 
                                            <?= ($_GET['employee_id'] ?? '') == $emp['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($emp['first_name'] . ' ' . $emp['last_name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="pending" <?= ($_GET['status'] ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="approved" <?= ($_GET['status'] ?? '') === 'approved' ? 'selected' : '' ?>>Approved</option>
                                    <option value="rejected" <?= ($_GET['status'] ?? '') === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                                    <option value="cancelled" <?= ($_GET['status'] ?? '') === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <input type="date" class="form-control" id="dateFromFilter" placeholder="From Date"
                                       value="<?= htmlspecialchars($_GET['date_from'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <input type="date" class="form-control" id="dateToFilter" placeholder="To Date"
                                       value="<?= htmlspecialchars($_GET['date_to'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-primary w-100" onclick="applyFilters()">
                                <svg width="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                    <circle cx="11" cy="11" r="8" stroke="currentcolor" stroke-width="1.5"></circle>
                                    <path d="21 21l-4.35-4.35" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                Filter
                            </button>
                        </div>
                    </div>

                    <!-- Leave Requests Table -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="leaveRequestsTable">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Leave Type</th>
                                    <th>Period</th>
                                    <th>Days</th>
                                    <th>Reason</th>
                                    <th>Status</th>
                                    <th>Requested</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($leaveRequests as $leave): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($leave['first_name'] . ' ' . $leave['last_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($leave['employee_code']) ?></small>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($leave['leave_type_name']) ?></td>
                                    <td>
                                        <?= formatDate($leave['start_date']) ?><br>
                                        <small class="text-muted">to <?= formatDate($leave['end_date']) ?></small>
                                    </td>
                                    <td><span class="badge bg-info"><?= $leave['total_days'] ?> days</span></td>
                                    <td>
                                        <?php if ($leave['reason']): ?>
                                        <span data-bs-toggle="tooltip" title="<?= htmlspecialchars($leave['reason']) ?>">
                                            <?= htmlspecialchars(substr($leave['reason'], 0, 30)) ?><?= strlen($leave['reason']) > 30 ? '...' : '' ?>
                                        </span>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $leave['status'] === 'approved' ? 'success' : ($leave['status'] === 'rejected' ? 'danger' : ($leave['status'] === 'cancelled' ? 'secondary' : 'warning')) ?>">
                                            <?= ucfirst($leave['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= formatDateTime($leave['created_at'], 'd/m/Y H:i') ?></td>
                                    <td>
                                        <div class="flex align-items-center list-user-action">
                                            <a class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="View" href="#" onclick="viewLeaveRequest(<?= $leave['id'] ?>)">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill="currentColor" d="M9.76 14.37L12 12.13L14.24 14.37L15.65 12.96L13.41 10.72L15.65 8.48L14.24 7.07L12 9.31L9.76 7.07L8.35 8.48L10.59 10.72L8.35 12.96L9.76 14.37Z"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            
                                            <?php if ($leave['status'] === 'pending' && hasPermission('leave.approve')): ?>
                                            <a class="btn btn-sm btn-icon btn-success" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Approve" href="#" onclick="approveLeave(<?= $leave['id'] ?>)">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M11.3194 14.3209C11.1261 14.3219 10.9328 14.2523 10.7838 14.1091L8.86695 12.2656C8.57097 11.9793 8.56795 11.5145 8.86091 11.2262C9.15387 10.9369 9.63207 10.934 9.92906 11.2193L11.3083 12.5451L14.6758 9.22479C14.9698 8.93552 15.448 8.93258 15.744 9.21793C16.041 9.50426 16.044 9.97004 15.751 10.2574L11.8519 14.1022C11.7049 14.2474 11.5127 14.3199 11.3194 14.3209Z" fill="currentColor"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            
                                            <a class="btn btn-sm btn-icon btn-danger" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Reject" href="#" onclick="rejectLeave(<?= $leave['id'] ?>)">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M18.3 5.71L12 12.01L5.7 5.71L4.29 7.12L10.59 13.42L4.29 19.72L5.7 21.13L12 14.83L18.3 21.13L19.71 19.72L13.41 13.42L19.71 7.12L18.3 5.71Z" fill="currentColor"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (in_array($leave['status'], ['pending', 'approved']) && hasPermission('leave.cancel')): ?>
                                            <a class="btn btn-sm btn-icon btn-warning" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Cancel" href="#" onclick="cancelLeave(<?= $leave['id'] ?>)">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11H7v-2h10v2z" fill="currentColor"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['previous_page'] ?>&employee_id=<?= urlencode($_GET['employee_id'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>&date_from=<?= urlencode($_GET['date_from'] ?? '') ?>&date_to=<?= urlencode($_GET['date_to'] ?? '') ?>">Previous</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                            <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&employee_id=<?= urlencode($_GET['employee_id'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>&date_from=<?= urlencode($_GET['date_from'] ?? '') ?>&date_to=<?= urlencode($_GET['date_to'] ?? '') ?>"><?= $i ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['next_page'] ?>&employee_id=<?= urlencode($_GET['employee_id'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>&date_from=<?= urlencode($_GET['date_from'] ?? '') ?>&date_to=<?= urlencode($_GET['date_to'] ?? '') ?>">Next</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leave Request Modal -->
<div class="modal fade" id="leaveModal" tabindex="-1" aria-labelledby="leaveModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leaveModalLabel">Add Leave Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="leaveForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="employee_id" class="form-label">Employee <span class="text-danger">*</span></label>
                                <select class="form-select" id="employee_id" name="employee_id" required>
                                    <option value="">Select Employee</option>
                                    <?php foreach ($employees as $emp): ?>
                                    <option value="<?= $emp['id'] ?>">
                                        <?= htmlspecialchars($emp['first_name'] . ' ' . $emp['last_name']) ?> (<?= htmlspecialchars($emp['employee_code']) ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="leave_type_id" class="form-label">Leave Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="leave_type_id" name="leave_type_id" required>
                                    <option value="">Select Leave Type</option>
                                    <?php foreach ($leaveTypes as $type): ?>
                                    <option value="<?= $type['id'] ?>">
                                        <?= htmlspecialchars($type['name']) ?>
                                        <?php if ($type['max_days_per_year'] > 0): ?>
                                        (Max: <?= $type['max_days_per_year'] ?> days/year)
                                        <?php endif; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="reason" class="form-label">Reason</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="Enter reason for leave"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Request</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additionalScripts = '
<script>
    // Apply filters
    function applyFilters() {
        const employee = document.getElementById("employeeFilter").value;
        const status = document.getElementById("statusFilter").value;
        const dateFrom = document.getElementById("dateFromFilter").value;
        const dateTo = document.getElementById("dateToFilter").value;
        
        const params = new URLSearchParams();
        if (employee) params.append("employee_id", employee);
        if (status) params.append("status", status);
        if (dateFrom) params.append("date_from", dateFrom);
        if (dateTo) params.append("date_to", dateTo);
        
        window.location.href = "?" + params.toString();
    }

    // View leave request
    function viewLeaveRequest(id) {
        window.location.href = `show.php?id=${id}`;
    }

    // Approve leave
    function approveLeave(id) {
        Swal.fire({
            title: "Approve Leave Request",
            input: "textarea",
            inputLabel: "Comments (optional)",
            inputPlaceholder: "Enter approval comments...",
            showCancelButton: true,
            confirmButtonText: "Approve",
            confirmButtonColor: "#28a745",
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading("Approving leave request...");
                
                fetch("../api/leaves/approve", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": CSRF_TOKEN
                    },
                    body: JSON.stringify({ 
                        id: id, 
                        comments: result.value,
                        csrf_token: CSRF_TOKEN 
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showAlert("success", data.message);
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showAlert("error", data.message);
                    }
                })
                .catch(error => {
                    hideLoading();
                    showAlert("error", "Failed to approve leave request");
                });
            }
        });
    }

    // Reject leave
    function rejectLeave(id) {
        Swal.fire({
            title: "Reject Leave Request",
            input: "textarea",
            inputLabel: "Rejection Reason (required)",
            inputPlaceholder: "Enter reason for rejection...",
            inputValidator: (value) => {
                if (!value) {
                    return "Rejection reason is required";
                }
            },
            showCancelButton: true,
            confirmButtonText: "Reject",
            confirmButtonColor: "#dc3545",
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading("Rejecting leave request...");
                
                fetch("../api/leaves/reject", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": CSRF_TOKEN
                    },
                    body: JSON.stringify({ 
                        id: id, 
                        reason: result.value,
                        csrf_token: CSRF_TOKEN 
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        showAlert("success", data.message);
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showAlert("error", data.message);
                    }
                })
                .catch(error => {
                    hideLoading();
                    showAlert("error", "Failed to reject leave request");
                });
            }
        });
    }

    // Cancel leave
    function cancelLeave(id) {
        showConfirm("Are you sure you want to cancel this leave request?", function() {
            showLoading("Cancelling leave request...");
            
            fetch("../api/leaves/cancel", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": CSRF_TOKEN
                },
                body: JSON.stringify({ 
                    id: id,
                    csrf_token: CSRF_TOKEN 
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert("success", data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert("error", data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert("error", "Failed to cancel leave request");
            });
        });
    }

    // Handle form submission
    document.getElementById("leaveForm").addEventListener("submit", function(e) {
        e.preventDefault();
        
        if (!validateForm("#leaveForm")) {
            showAlert("error", "Please fill in all required fields correctly");
            return;
        }
        
        // Validate dates
        const startDate = new Date(document.getElementById("start_date").value);
        const endDate = new Date(document.getElementById("end_date").value);
        
        if (startDate > endDate) {
            showAlert("error", "Start date cannot be after end date");
            return;
        }
        
        showLoading("Creating leave request...");
        
        const formData = new FormData(this);
        formData.append("ajax", "1");
        
        fetch("../api/leaves", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert("success", data.message);
                bootstrap.Modal.getInstance(document.getElementById("leaveModal")).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert("error", data.message);
            }
        })
        .catch(error => {
            hideLoading();
            showAlert("error", "Failed to create leave request");
        });
    });

    // Reset form when modal is hidden
    document.getElementById("leaveModal").addEventListener("hidden.bs.modal", function() {
        document.getElementById("leaveForm").reset();
    });

    // Set minimum date to today
    const today = new Date().toISOString().split("T")[0];
    document.getElementById("start_date").min = today;
    document.getElementById("end_date").min = today;

    // Update end date minimum when start date changes
    document.getElementById("start_date").addEventListener("change", function() {
        document.getElementById("end_date").min = this.value;
    });
</script>
';

include __DIR__ . '/../../layouts/footer.php';
?>
