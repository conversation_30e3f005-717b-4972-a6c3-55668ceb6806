<?php
require_once __DIR__ . '/../../../config/config.php';

$auth = new Auth();

// Require authentication
if (!$auth->isAuthenticated()) {
    redirect(BASE_URL . '/login.php');
}

$currentUser = $auth->getCurrentUser();

// Get employees data
$employeeModel = new Employee();

// Handle pagination and filters
$page = $_GET['page'] ?? 1;
$search = $_GET['search'] ?? null;
$department = $_GET['department'] ?? null;
$status = $_GET['status'] ?? null;

$limit = RECORDS_PER_PAGE;
$offset = ($page - 1) * $limit;

$employees = $employeeModel->getAll($limit, $offset, $search, $department, $status);
$totalRecords = $employeeModel->count($search, $department, $status);
$pagination = paginate($totalRecords, $page, $limit);

// Get unique departments for filter
$allEmployees = $employeeModel->getAll();
$departments = array_unique(array_filter(array_column($allEmployees, 'department')));

$pageTitle = 'Employees Management';
include __DIR__ . '/../../layouts/header.php';

?>

<div class="conatiner-fluid content-inner mt-n5 py-0">
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">Employees</h4>
                    </div>
                    <div class="header-action">
                        <?php if (hasPermission('employees.create')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#employeeModal">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M9.87651 15.2063C6.03251 15.2063 2.74951 15.7873 2.74951 18.1153C2.74951 20.4433 6.01251 21.0453 9.87651 21.0453C13.7215 21.0453 17.0035 20.4633 17.0035 18.1363C17.0035 15.8093 13.7415 15.2063 9.87651 15.2063Z" fill="currentColor"></path>
                                <path opacity="0.4" fill-rule="evenodd" clip-rule="evenodd" d="M9.8766 11.886C12.3996 11.886 14.4446 9.841 14.4446 7.318C14.4446 4.795 12.3996 2.75 9.8766 2.75C7.3546 2.75 5.3096 4.795 5.3096 7.318C5.3096 9.841 7.3546 11.886 9.8766 11.886Z" fill="currentColor"></path>
                                <path opacity="0.4" d="M19.2036 8.66919V12.6792" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path opacity="0.4" d="M21.2497 10.6741H17.1597" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                            Add Employee
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search employees..." 
                                       value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select class="form-select" id="departmentFilter">
                                    <option value="">All Departments</option>
                                    <?php foreach ($departments as $dept): ?>
                                    <option value="<?= htmlspecialchars($dept) ?>" 
                                            <?= ($_GET['department'] ?? '') === $dept ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($dept) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="active" <?= ($_GET['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= ($_GET['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    <option value="terminated" <?= ($_GET['status'] ?? '') === 'terminated' ? 'selected' : '' ?>>Terminated</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-primary w-100" id="filterBtn">
                                <svg width="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                    <circle cx="11" cy="11" r="8" stroke="currentcolor" stroke-width="1.5"></circle>
                                    <path d="M21 21l-4.35-4.35" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                Filter
                            </button>
                        </div>
                    </div>

                    <!-- Employees Table -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="employeesTable">
                            <thead>
                                <tr>
                                    <th>Photo</th>
                                    <th>Employee Code</th>
                                    <th>Name</th>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Phone</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($employees as $employee): ?>
                                <tr>
                                    <td>
                                        <?php if ($employee['profile_image']): ?>
                                        <img src="<?= BASE_URL ?>/uploads/profiles/<?= $employee['profile_image'] ?>" 
                                             alt="Profile" class="img-fluid rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                        <?php else: ?>
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <span class="text-white"><?= strtoupper(substr($employee['first_name'], 0, 1)) ?></span>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= htmlspecialchars($employee['employee_code']) ?></td>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']) ?></strong>
                                            <?php if ($employee['nickname']): ?>
                                            <br><small class="text-muted">(<?= htmlspecialchars($employee['nickname']) ?>)</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($employee['position']) ?></td>
                                    <td><?= htmlspecialchars($employee['department']) ?></td>
                                    <td><?= $employee['phone'] ?></td>
                                    <td>
                                        <span class="badge bg-<?= $employee['employment_status'] === 'active' ? 'success' : ($employee['employment_status'] === 'inactive' ? 'warning' : 'danger') ?>">
                                            <?= ucfirst($employee['employment_status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="flex align-items-center list-user-action">
                                            <a class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip"
                                               data-bs-placement="top" title="View" href="#" data-action="view" data-id="<?= $employee['id'] ?>">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M15.58 12C15.58 13.98 13.98 15.58 12 15.58C10.02 15.58 8.42 13.98 8.42 12C8.42 10.02 10.02 8.42 12 8.42C13.98 8.42 15.58 10.02 15.58 12Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path d="M12 20.27C15.53 20.27 18.82 18.19 21.11 14.59C22.01 13.18 22.01 10.81 21.11 9.4C18.82 5.8 15.53 3.72 12 3.72C8.47 3.72 5.18 5.8 2.89 9.4C1.99 10.81 1.99 13.18 2.89 14.59C5.18 18.19 8.47 20.27 12 20.27Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            
                                            <?php if (hasPermission('employees.edit')): ?>
                                            <a class="btn btn-sm btn-icon btn-warning" data-bs-toggle="tooltip"
                                               data-bs-placement="top" title="Edit" href="#" data-action="edit" data-id="<?= $employee['id'] ?>">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M11.4925 2.78906H7.75349C4.67849 2.78906 2.75049 4.96606 2.75049 8.04806V16.3621C2.75049 19.4441 4.66949 21.6211 7.75349 21.6211H16.5775C19.6625 21.6211 21.5815 19.4441 21.5815 16.3621V12.3341" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.82812 10.921L16.3011 3.44799C17.2321 2.51799 18.7411 2.51799 19.6721 3.44799L20.8891 4.66499C21.8201 5.59599 21.8201 7.10599 20.8891 8.03599L13.3801 15.545C12.9731 15.952 12.4211 16.181 11.8451 16.181H8.09912L8.19312 12.401C8.20712 11.845 8.43412 11.315 8.82812 10.921Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                        <path d="M15.1655 4.60254L19.7315 9.16854" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (hasPermission('employees.delete')): ?>
                                            <a class="btn btn-sm btn-icon btn-danger" data-bs-toggle="tooltip"
                                               data-bs-placement="top" title="Delete" href="#" data-action="delete" data-id="<?= $employee['id'] ?>" data-name="<?= htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']) ?>">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor">
                                                        <polyline points="3,6 5,6 21,6"></polyline>
                                                        <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                                                        <line x1="10" y1="11" x2="10" y2="17"></line>
                                                        <line x1="14" y1="11" x2="14" y2="17"></line>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['previous_page'] ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&department=<?= urlencode($_GET['department'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>">Previous</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                            <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&department=<?= urlencode($_GET['department'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>"><?= $i ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['next_page'] ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&department=<?= urlencode($_GET['department'] ?? '') ?>&status=<?= urlencode($_GET['status'] ?? '') ?>">Next</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employee Modal -->
<div class="modal fade" id="employeeModal" tabindex="-1" aria-labelledby="employeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="employeeModalLabel">Add Employee</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="employeeForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                    <input type="hidden" name="employee_id" id="employee_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="employee_code" class="form-label">Employee Code</label>
                                <input type="text" class="form-control" id="employee_code" name="employee_code" placeholder="Auto-generated if empty">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="title_name" class="form-label">Title</label>
                                <select class="form-select" id="title_name" name="title_name">
                                    <option value="">Select Title</option>
                                    <option value="Mr.">Mr.</option>
                                    <option value="Ms.">Ms.</option>
                                    <option value="Mrs.">Mrs.</option>
                                    <option value="Dr.">Dr.</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="nickname" class="form-label">Nickname</label>
                                <input type="text" class="form-control" id="nickname" name="nickname">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="id_card" class="form-label">ID Card Number</label>
                                <input type="text" class="form-control" id="id_card" name="id_card" maxlength="13">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="birth_date" class="form-label">Birth Date</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="gender" class="form-label">Gender</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">Select Gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" class="form-control" id="position" name="position">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="department" class="form-label">Department</label>
                                <input type="text" class="form-control" id="department" name="department">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="hire_date" class="form-label">Hire Date</label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="employment_status" class="form-label">Employment Status</label>
                                <select class="form-select" id="employment_status" name="employment_status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="terminated">Terminated</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="profile_image" class="form-label">Profile Image</label>
                        <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                        <div class="mt-2">
                            <img id="imagePreview" src="#" alt="Preview" style="max-width: 150px; max-height: 150px; display: none;" class="img-thumbnail">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Employee</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additionalScripts = <<<'EOD'
<script>
    // BASE_URL and CSRF_TOKEN are already defined in footer.php
    // No need to redeclare them here

    let isEditMode = false;
    let isViewMode = false;

    // Test if JavaScript is working
    console.log('JavaScript loaded successfully');
    console.log('BASE_URL:', BASE_URL);
    console.log('CSRF_TOKEN:', CSRF_TOKEN);

    // Make functions global so they can be called from onclick
    window.viewEmployee = viewEmployee;
    window.editEmployee = editEmployee;
    window.deleteEmployee = deleteEmployee;

    // Apply filters
    function applyFilters() {
        const search = document.getElementById("searchInput").value;
        const department = document.getElementById("departmentFilter").value;
        const status = document.getElementById("statusFilter").value;
        
        const params = new URLSearchParams();
        if (search) params.append("search", search);
        if (department) params.append("department", department);
        if (status) params.append("status", status);
        
        window.location.href = "?" + params.toString();
    }

    // Search on Enter key
    document.getElementById("searchInput").addEventListener("keypress", function(e) {
        if (e.key === "Enter") {
            applyFilters();
        }
    });

    // View employee
    function viewEmployee(id) {
        showLoading("Loading employee data...");

        fetch(BASE_URL + "/api/employees.php?id=" + id, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response URL:', response.url);

            return response.text().then(text => {
                console.log('Raw response body:', text);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${text}`);
                }

                // Try to parse as JSON
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Failed to parse JSON:', e);
                    console.error('Response text:', text);
                    throw new Error('Server returned invalid JSON response: ' + text.substring(0, 200));
                }
            });
        })
        .then(data => {
            hideLoading();
            console.log('API Response:', data);
            if (data.success) {
                // Show employee details in modal (view-only)
                const employee = data.data;

                // Set modal title
                document.getElementById("employeeModalLabel").textContent = "View Employee - " + employee.first_name + " " + employee.last_name;

                // Reset form first
                document.getElementById("employeeForm").reset();

                // Populate form fields (read-only)
                Object.keys(employee).forEach(key => {
                    const field = document.getElementById(key);
                    if (field) {
                        field.value = employee[key] || '';
                        field.readOnly = true;
                        field.disabled = true;
                    }
                });

                // Handle select fields
                const selectFields = ['title_name', 'gender', 'position', 'department', 'employment_status'];
                selectFields.forEach(fieldName => {
                    const field = document.getElementById(fieldName);
                    if (field && employee[fieldName]) {
                        field.value = employee[fieldName];
                        field.disabled = true;
                    }
                });

                // Show existing image if available
                if (employee.profile_image) {
                    const preview = document.getElementById("imagePreview");
                    preview.src = BASE_URL + "/uploads/profiles/" + employee.profile_image;
                    preview.style.display = "block";
                }

                // Hide submit button for view mode
                const submitBtn = document.querySelector("#employeeModal .btn-primary");
                if (submitBtn) submitBtn.style.display = "none";

                // Disable file input
                const fileInput = document.getElementById("profile_image");
                if (fileInput) fileInput.disabled = true;

                isViewMode = true;
                new bootstrap.Modal(document.getElementById("employeeModal")).show();
            } else {
                showAlert("error", data.message);
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Fetch error:', error);
            showAlert("error", "ไม่สามารถโหลดข้อมูลพนักงานได้: " + error.message);
        });
    }

    // Edit employee
    function editEmployee(id) {
        isEditMode = true;
        document.getElementById("employeeModalLabel").textContent = "Edit Employee";
        document.getElementById("employee_id").value = id;
        
        // Load employee data
        fetch(BASE_URL + "/api/employees.php?id=" + id, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                console.log('Edit Response status:', response.status);
                console.log('Edit Response URL:', response.url);
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Edit API Response:', data);
                if (data.success) {
                    const employee = data.data;
                    Object.keys(employee).forEach(key => {
                        const field = document.getElementById(key);
                        if (field) {
                            field.value = employee[key] || "";
                        }
                    });

                    // Show existing image if available
                    if (employee.profile_image) {
                        const preview = document.getElementById("imagePreview");
                        preview.src = BASE_URL + "/uploads/profiles/" + employee.profile_image;
                        preview.style.display = "block";
                    }
                    
                    new bootstrap.Modal(document.getElementById("employeeModal")).show();
                } else {
                    showAlert("error", data.message);
                }
            })
            .catch(error => {
                showAlert("error", "ไม่สามารถโหลดข้อมูลพนักงานได้");
            });
    }

    // Delete employee
    function deleteEmployee(id, name) {
        showConfirm("Are you sure you want to delete employee \"" + name + "\"?", function() {
            showLoading("Deleting employee...");

            fetch(BASE_URL + "/api/employees.php?id=" + id, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": CSRF_TOKEN
                },
                body: JSON.stringify({ id: id, csrf_token: CSRF_TOKEN })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert("success", data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert("error", data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert("error", "ไม่สามารถลบข้อมูลพนักงานได้");
            });
        });
    }

    // Handle form submission
    document.getElementById("employeeForm").addEventListener("submit", function(e) {
        e.preventDefault();

        // Don't submit if in view mode
        if (isViewMode) {
            return;
        }

        if (!validateForm("#employeeForm")) {
            showAlert("error", "กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วนและถูกต้อง");
            return;
        }

        showLoading(isEditMode ? "กำลังอัปเดตข้อมูลพนักงาน..." : "กำลังสร้างข้อมูลพนักงาน...");

        const formData = new FormData(this);
        const employeeId = document.getElementById("employee_id").value;
        const url = isEditMode ? BASE_URL + "/api/employees-v2.php?id=" + employeeId : BASE_URL + "/api/employees.php";
        const method = isEditMode ? "PUT" : "POST";

        // Debug: Log form data
        console.log('Form method:', method);
        console.log('Form URL:', url);
        console.log('Employee ID:', document.getElementById("employee_id").value);

        // Log form data contents
        for (let [key, value] of formData.entries()) {
            console.log('Form data:', key, value);
        }

        // For PUT requests, we need to handle FormData differently
        let requestBody;
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (method === 'PUT') {
            // Get original employee data to compare changes
            const employeeId = document.getElementById("employee_id").value;

            // Convert FormData to URLSearchParams for PUT request
            const params = new URLSearchParams();
            for (let [key, value] of formData.entries()) {
                if (value instanceof File) {
                    // Handle file uploads separately if needed
                    console.log('File field:', key, value.name);
                } else {
                    // Handle empty date fields
                    if ((key === 'birth_date' || key === 'hire_date') && value === '') {
                        console.log('Empty date field:', key, 'skipping...');
                        continue; // Skip empty date fields
                    }

                    // Handle empty gender field
                    if (key === 'gender' && value === '') {
                        console.log('Empty gender field, skipping...');
                        continue; // Skip empty gender field
                    }

                    // Handle empty employment_status field
                    if (key === 'employment_status' && value === '') {
                        console.log('Empty employment_status field, skipping...');
                        continue; // Skip empty employment_status field
                    }

                    // Handle empty id_card field - but allow existing values
                    if (key === 'id_card' && value === '') {
                        console.log('Empty id_card field, skipping...');
                        continue; // Skip empty id_card field
                    }

                    // Handle empty employee_code field
                    if (key === 'employee_code' && value === '') {
                        console.log('Empty employee_code field, skipping...');
                        continue; // Skip empty employee_code field
                    }

                    params.append(key, value);
                }
            }

            headers['Content-Type'] = 'application/x-www-form-urlencoded';
            headers['X-CSRFToken'] = CSRF_TOKEN;
            requestBody = params.toString();
            console.log('PUT request body:', requestBody);
        } else {
            // For POST, use FormData as is
            requestBody = formData;
        }

        fetch(url, {
            method: method,
            headers: headers,
            body: requestBody,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            return response.text().then(text => {
                console.log('Raw response body:', text);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${text}`);
                }

                // Try to parse as JSON
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Failed to parse JSON:', e);
                    console.error('Response text:', text);
                    throw new Error('Server returned invalid JSON response: ' + text.substring(0, 200));
                }
            });
        })
        .then(data => {
            hideLoading();
            console.log('Success response:', data);
            if (data.success) {
                showAlert("success", data.message);
                bootstrap.Modal.getInstance(document.getElementById("employeeModal")).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert("error", data.message);
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Fetch error:', error);
            showAlert("error", isEditMode ? "ไม่สามารถอัปเดตข้อมูลพนักงานได้: " + error.message : "ไม่สามารถสร้างข้อมูลพนักงานได้: " + error.message);
        });
    });

    // Reset form when modal is hidden
    document.getElementById("employeeModal").addEventListener("hidden.bs.modal", function() {
        isEditMode = false;
        isViewMode = false;
        document.getElementById("employeeModalLabel").textContent = "Add Employee";
        document.getElementById("employeeForm").reset();
        document.getElementById("employee_id").value = "";
        document.getElementById("imagePreview").style.display = "none";

        // Re-enable all fields
        const formFields = document.querySelectorAll("#employeeForm input, #employeeForm select, #employeeForm textarea");
        formFields.forEach(field => {
            field.readOnly = false;
            field.disabled = false;
        });

        // Show submit button
        const submitBtn = document.querySelector("#employeeModal .btn-primary");
        if (submitBtn) submitBtn.style.display = "inline-block";
    });

    // Image preview
    document.getElementById("profile_image").addEventListener("change", function() {
        previewImage(this, "#imagePreview");
    });

    // Add event listeners for action buttons
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, setting up event listeners');

        // Filter button
        const filterBtn = document.getElementById('filterBtn');
        if (filterBtn) {
            filterBtn.addEventListener('click', applyFilters);
        }

        // View buttons
        document.querySelectorAll('[data-action="view"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                console.log('View clicked for ID:', id);
                viewEmployee(id);
            });
        });

        // Edit buttons
        document.querySelectorAll('[data-action="edit"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                console.log('Edit clicked for ID:', id);
                editEmployee(id);
            });
        });

        // Delete buttons
        document.querySelectorAll('[data-action="delete"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                console.log('Delete clicked for ID:', id, 'Name:', name);
                deleteEmployee(id, name);
            });
        });
    });
</script>
EOD;

include __DIR__ . '/../../layouts/footer.php';
?>
