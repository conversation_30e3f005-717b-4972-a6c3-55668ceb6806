{"timestamp":"2025-08-10 05:04:46","type":"error","user_id":1,"ip_address":"::1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","action":"update_employee_error","description":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'first_name' cannot be null","data":{"employee_id":1}}
{"timestamp":"2025-08-10 05:15:08","type":"error","user_id":1,"ip_address":"::1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","action":"update_employee_error","description":"SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect date value: '' for column 'birth_date' at row 1","data":{"employee_id":1}}
{"timestamp":"2025-08-10 05:19:45","type":"error","user_id":1,"ip_address":"::1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","action":"update_employee_error","description":"SQLSTATE[01000]: Warning: 1265 Data truncated for column 'gender' at row 1","data":{"employee_id":1}}
{"timestamp":"2025-08-10 05:44:09","type":"error","user_id":1,"ip_address":"::1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","action":"update_employee_error","description":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '1234567890123' for key 'employees.id_card'","data":{"employee_id":1}}
{"timestamp":"2025-08-10 05:45:12","type":"error","user_id":1,"ip_address":"::1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","action":"update_employee_error","description":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '1234567890123' for key 'employees.id_card'","data":{"employee_id":1}}
