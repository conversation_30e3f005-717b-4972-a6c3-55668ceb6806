<?php
require_once '../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'วิธีการเรียกใช้ไม่ได้รับอนุญาต']);
        exit;
    }
    
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    
    if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ']);
        exit;
    }
    
    $accessToken = substr($authHeader, 7);
    
    // Verify LINE access token
    $lineProfile = verifyLineAccessToken($accessToken);
    if (!$lineProfile) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'โทเค็นไม่ถูกต้อง']);
        exit;
    }
    
    // Get leave types
    $leaveTypeModel = new LeaveType();
    $leaveTypes = $leaveTypeModel->getAll();
    
    echo json_encode([
        'success' => true,
        'data' => $leaveTypes
    ]);
    
} catch (Exception $e) {
    error_log("Leave types API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดของเซิร์ฟเวอร์']);
}

/**
 * Verify LINE access token and get user profile
 */
function verifyLineAccessToken($accessToken) {
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.line.me/v2/profile');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return json_decode($response, true);
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("LINE token verification error: " . $e->getMessage());
        return false;
    }
}
?>
