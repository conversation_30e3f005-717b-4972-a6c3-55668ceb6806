<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deployment Guide - Upload API Files</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .success-card {
            border-left: 4px solid #28a745;
        }
        .warning-card {
            border-left: 4px solid #ffc107;
        }
        .info-card {
            border-left: 4px solid #17a2b8;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .file-tree {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            margin: 10px 0;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">📤 Deployment Guide</h1>
            <p class="text-white-50">Upload API Files to Production Server</p>
        </div>
        
        <!-- Current Status -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">🎉 ความสำเร็จ</h5>
                <p class="card-text">
                    <strong>ยินดีด้วย!</strong> LIFF ทำงานได้แล้ว 100%<br>
                    ✅ LIFF initialization สำเร็จ<br>
                    ✅ OAuth authentication สำเร็จ<br>
                    ✅ User profile ได้แล้ว<br><br>
                    <strong>เหลือเพียง:</strong> Upload API files ไปยัง production server
                </p>
            </div>
        </div>
        
        <!-- Problem -->
        <div class="card warning-card">
            <div class="card-body">
                <h5 class="card-title text-warning">⚠️ ปัญหาปัจจุบัน</h5>
                <div class="code-block">
GET https://smartapplytech.com/hrc/api/employee/profile 404 (Not Found)
                </div>
                <p><strong>สาเหตุ:</strong> API files ยังไม่ได้ upload ไปยัง production server</p>
            </div>
        </div>
        
        <!-- Files to Upload -->
        <div class="card info-card">
            <div class="card-body">
                <h5 class="card-title text-info">📁 ไฟล์ที่ต้อง Upload</h5>
                
                <h6>API Files:</h6>
                <div class="file-tree">
api/
├── .htaccess
├── employee/
│   └── profile.php
├── leave-requests.php
├── leave-types.php
├── benefits.php
├── salary.php
└── salary/
    ├── history.php
    └── export.php
                </div>
                
                <div class="alert alert-info">
                    <strong>💡 หมายเหตุ:</strong><br>
                    ไฟล์เหล่านี้อยู่ในโฟลเดอร์ <code>C:\laragon\www\hrc\api\</code> ของ localhost
                </div>
            </div>
        </div>
        
        <!-- Step by Step -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🚀 ขั้นตอนการ Deploy</h5>
                
                <div class="step mb-4">
                    <h6><span class="step-number">1</span>เตรียมไฟล์</h6>
                    <p>Copy ไฟล์จาก localhost:</p>
                    <div class="code-block">
# จาก localhost
C:\laragon\www\hrc\api\

# ไปยัง production
https://smartapplytech.com/hrc/api/
                    </div>
                </div>
                
                <div class="step mb-4">
                    <h6><span class="step-number">2</span>Upload ผ่าน FTP/cPanel</h6>
                    <p>ใช้ FTP client หรือ cPanel File Manager:</p>
                    <ul>
                        <li>เข้า cPanel ของ smartapplytech.com</li>
                        <li>เปิด File Manager</li>
                        <li>ไปที่โฟลเดอร์ <code>public_html/hrc/</code></li>
                        <li>สร้างโฟลเดอร์ <code>api</code> (หากยังไม่มี)</li>
                        <li>Upload ไฟล์ทั้งหมดตามโครงสร้าง</li>
                    </ul>
                </div>
                
                <div class="step mb-4">
                    <h6><span class="step-number">3</span>ตั้งค่าสิทธิ์ไฟล์</h6>
                    <div class="code-block">
# โฟลเดอร์
chmod 755 api/
chmod 755 api/employee/
chmod 755 api/salary/

# ไฟล์ PHP
chmod 644 api/*.php
chmod 644 api/employee/*.php
chmod 644 api/salary/*.php

# ไฟล์ .htaccess
chmod 644 api/.htaccess
                    </div>
                </div>
                
                <div class="step mb-4">
                    <h6><span class="step-number">4</span>ทดสอบ API Endpoints</h6>
                    <p>ทดสอบ URL เหล่านี้ใน browser:</p>
                    <div class="code-block">
https://smartapplytech.com/hrc/api/employee/profile
https://smartapplytech.com/hrc/api/leave-requests
https://smartapplytech.com/hrc/api/leave-types
                    </div>
                    <p><strong>ผลลัพธ์ที่คาดหวัง:</strong> JSON response (ไม่ใช่ HTML error page)</p>
                </div>
            </div>
        </div>
        
        <!-- Expected Structure -->
        <div class="card info-card">
            <div class="card-body">
                <h5 class="card-title text-info">🏗️ โครงสร้างไฟล์บน Production</h5>
                <div class="file-tree">
https://smartapplytech.com/hrc/
├── index.php
├── config/
│   └── config.php
├── models/
│   ├── Employee.php
│   ├── Leave.php
│   ├── Benefit.php
│   └── Salary.php
├── api/
│   ├── .htaccess
│   ├── employees.php
│   ├── benefits.php
│   ├── salary.php
│   ├── employee/
│   │   └── profile.php
│   ├── leave-requests.php
│   ├── leave-types.php
│   └── salary/
│       ├── history.php
│       └── export.php
├── liff/
│   ├── index.html
│   ├── js/
│   │   └── app.js
│   └── debug.html
└── views/
    └── ...
                </div>
            </div>
        </div>
        
        <!-- Testing -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 การทดสอบ</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>✅ ทดสอบ API:</h6>
                        <div class="d-grid gap-2">
                            <a href="https://smartapplytech.com/hrc/api/employee/profile" target="_blank" class="btn btn-outline-primary btn-sm">
                                Test Employee Profile API
                            </a>
                            <a href="https://smartapplytech.com/hrc/api/leave-types" target="_blank" class="btn btn-outline-info btn-sm">
                                Test Leave Types API
                            </a>
                            <a href="https://smartapplytech.com/hrc/api/leave-requests" target="_blank" class="btn btn-outline-success btn-sm">
                                Test Leave Requests API
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>📱 ทดสอบ LIFF:</h6>
                        <div class="d-grid gap-2">
                            <a href="https://smartapplytech.com/hrc/liff/" target="_blank" class="btn btn-primary btn-sm">
                                Test LIFF App
                            </a>
                            <a href="https://smartapplytech.com/hrc/liff/debug.html" target="_blank" class="btn btn-info btn-sm">
                                Test Debug Tool
                            </a>
                            <button class="btn btn-success btn-sm" onclick="testInLineApp()">
                                Test in LINE App
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Success Criteria -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">🎯 เกณฑ์ความสำเร็จ</h5>
                <p>หลังจาก deploy แล้ว ควรเห็น:</p>
                <ul>
                    <li>✅ API endpoints ตอบกลับด้วย JSON (ไม่ใช่ 404)</li>
                    <li>✅ LIFF app โหลดข้อมูลพนักงานได้</li>
                    <li>✅ แสดงหน้า main content</li>
                    <li>✅ สามารถส่งคำขอลาได้</li>
                    <li>✅ ดูประวัติการลาได้</li>
                </ul>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">⚡ Quick Actions</h5>
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-primary w-100 mb-2" onclick="copyApiFiles()">
                            📋 Copy File List
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-info w-100 mb-2" onclick="showFtpCommands()">
                            💻 FTP Commands
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success w-100 mb-2" onclick="testAllEndpoints()">
                            🔍 Test All APIs
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="index.html" class="btn btn-warning w-100 mb-2">
                            🔙 Back to LIFF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function copyApiFiles() {
            const fileList = `
API Files to Upload:

api/.htaccess
api/employee/profile.php
api/leave-requests.php
api/leave-types.php
api/benefits.php
api/salary.php
api/salary/history.php
api/salary/export.php

Source: C:\\laragon\\www\\hrc\\api\\
Target: https://smartapplytech.com/hrc/api/
            `;
            
            navigator.clipboard.writeText(fileList).then(() => {
                alert('📋 File list copied to clipboard!');
            }).catch(() => {
                alert(fileList);
            });
        }
        
        function showFtpCommands() {
            const commands = `
🔧 FTP Commands:

# Create directories
mkdir api
mkdir api/employee
mkdir api/salary

# Upload files
put api/.htaccess
put api/employee/profile.php
put api/leave-requests.php
put api/leave-types.php
put api/benefits.php
put api/salary.php
put api/salary/history.php
put api/salary/export.php

# Set permissions
chmod 755 api
chmod 755 api/employee
chmod 755 api/salary
chmod 644 api/*.php
chmod 644 api/employee/*.php
chmod 644 api/salary/*.php
chmod 644 api/.htaccess
            `;
            
            alert(commands);
        }
        
        function testAllEndpoints() {
            const endpoints = [
                'https://smartapplytech.com/hrc/api/employee/profile',
                'https://smartapplytech.com/hrc/api/leave-types',
                'https://smartapplytech.com/hrc/api/leave-requests'
            ];
            
            endpoints.forEach((url, index) => {
                setTimeout(() => {
                    window.open(url, '_blank');
                }, index * 1000);
            });
        }
        
        function testInLineApp() {
            const liffUrl = 'https://smartapplytech.com/hrc/liff/';
            const message = `
📱 ทดสอบใน LINE App:

1. เปิด LINE app บนมือถือ
2. ส่ง URL นี้ให้ตัวเองใน chat:
   ${liffUrl}
3. กด URL ใน LINE chat
4. จะเปิดใน LINE's internal browser
5. ทดสอบการทำงานของ LIFF app

URL: ${liffUrl}
            `;
            
            alert(message);
        }
    </script>
</body>
</html>
