<?php
/**
 * Employee Controller
 * Human Resources Center System
 */

class EmployeeController {
    private $employeeModel;
    private $logger;
    
    public function __construct() {
        $this->employeeModel = new Employee();
        $this->logger = new Logger();
        
        // Require authentication
        requireLogin();
    }
    
    /**
     * List employees
     */
    public function index() {
        requirePermission('employees.view');
        
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? null;
        $department = $_GET['department'] ?? null;
        $status = $_GET['status'] ?? null;
        
        $limit = RECORDS_PER_PAGE;
        $offset = ($page - 1) * $limit;
        
        $employees = $this->employeeModel->getAll($limit, $offset, $search, $department, $status);
        $totalRecords = $this->employeeModel->count($search, $department, $status);
        $pagination = paginate($totalRecords, $page, $limit);
        
        $departments = $this->employeeModel->getDepartments();
        $positions = $this->employeeModel->getPositions();
        
        include __DIR__ . '/../views/admin/employees/index.php';
    }
    
    /**
     * Show employee details
     */
    public function show() {
        requirePermission('employees.view');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            setFlashMessage('error', 'Employee ID is required');
            redirect(BASE_URL . '/admin/employees');
        }
        
        $employee = $this->employeeModel->getById($id);
        if (!$employee) {
            setFlashMessage('error', 'Employee not found');
            redirect(BASE_URL . '/admin/employees');
        }
        
        // Get additional employee data
        $salaryModel = new Salary();
        $leaveModel = new LeaveRequest();
        
        $currentSalary = $salaryModel->getCurrentSalary($id);
        $recentLeaves = $leaveModel->getAll(5, 0, $id);
        
        include __DIR__ . '/../views/admin/employees/show.php';
    }
    
    /**
     * Create employee
     */
    public function create() {
        requirePermission('employees.create');
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->store();
        }
        
        $departments = $this->employeeModel->getDepartments();
        $positions = $this->employeeModel->getPositions();
        
        include __DIR__ . '/../views/admin/employees/create.php';
    }
    
    /**
     * Store new employee
     */
    public function store() {
        requirePermission('employees.create');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        // Validate input
        $validator = new Validator($_POST);
        $validator->required('first_name', 'First name is required')
                 ->required('last_name', 'Last name is required')
                 ->email('email', 'Invalid email format')
                 ->phone('phone', 'Invalid phone number')
                 ->thaiId('id_card', 'Invalid Thai ID number')
                 ->unique('employee_code', 'employees', 'employee_code', null, 'Employee code already exists')
                 ->unique('id_card', 'employees', 'id_card', null, 'ID card number already exists');
        
        if ($validator->fails()) {
            if (isset($_POST['ajax'])) {
                errorResponse('Validation failed', 400);
            }
            
            setFlashMessage('error', 'Please correct the errors below');
            $errors = $validator->getErrors();
            include __DIR__ . '/../views/admin/employees/create.php';
            return;
        }
        
        // Handle file upload
        $profileImage = null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['profile_image'], UPLOAD_PATH . 'profiles/');
            if ($uploadResult['success']) {
                $profileImage = $uploadResult['filename'];
            } else {
                if (isset($_POST['ajax'])) {
                    errorResponse($uploadResult['message'], 400);
                }
                setFlashMessage('error', $uploadResult['message']);
                include __DIR__ . '/../views/admin/employees/create.php';
                return;
            }
        }
        
        // Prepare data
        $data = [
            'employee_code' => $_POST['employee_code'] ?? null,
            'title_name' => $_POST['title_name'] ?? null,
            'first_name' => $_POST['first_name'],
            'last_name' => $_POST['last_name'],
            'nickname' => $_POST['nickname'] ?? null,
            'id_card' => $_POST['id_card'] ?? null,
            'birth_date' => $_POST['birth_date'] ?? null,
            'gender' => $_POST['gender'] ?? null,
            'phone' => $_POST['phone'] ?? null,
            'email' => $_POST['email'] ?? null,
            'line_id' => $_POST['line_id'] ?? null,
            'address' => $_POST['address'] ?? null,
            'emergency_contact_name' => $_POST['emergency_contact_name'] ?? null,
            'emergency_contact_phone' => $_POST['emergency_contact_phone'] ?? null,
            'position' => $_POST['position'] ?? null,
            'department' => $_POST['department'] ?? null,
            'hire_date' => $_POST['hire_date'] ?? null,
            'employment_status' => $_POST['employment_status'] ?? 'active',
            'profile_image' => $profileImage
        ];
        
        $result = $this->employeeModel->create($data);
        
        if (isset($_POST['ajax'])) {
            if ($result['success']) {
                successResponse($result['message'], ['id' => $result['id']]);
            } else {
                errorResponse($result['message'], 400);
            }
        }
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
            redirect(BASE_URL . '/admin/employees');
        } else {
            setFlashMessage('error', $result['message']);
            include __DIR__ . '/../views/admin/employees/create.php';
        }
    }
    
    /**
     * Edit employee
     */
    public function edit() {
        requirePermission('employees.edit');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            setFlashMessage('error', 'Employee ID is required');
            redirect(BASE_URL . '/admin/employees');
        }
        
        $employee = $this->employeeModel->getById($id);
        if (!$employee) {
            setFlashMessage('error', 'Employee not found');
            redirect(BASE_URL . '/admin/employees');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->update($id);
        }
        
        $departments = $this->employeeModel->getDepartments();
        $positions = $this->employeeModel->getPositions();
        
        include __DIR__ . '/../views/admin/employees/edit.php';
    }
    
    /**
     * Update employee
     */
    public function update($id) {
        requirePermission('employees.edit');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $employee = $this->employeeModel->getById($id);
        if (!$employee) {
            if (isset($_POST['ajax'])) {
                errorResponse('Employee not found', 404);
            }
            setFlashMessage('error', 'Employee not found');
            redirect(BASE_URL . '/admin/employees');
        }
        
        // Validate input
        $validator = new Validator($_POST);
        $validator->required('first_name', 'First name is required')
                 ->required('last_name', 'Last name is required')
                 ->email('email', 'Invalid email format')
                 ->phone('phone', 'Invalid phone number')
                 ->thaiId('id_card', 'Invalid Thai ID number')
                 ->unique('id_card', 'employees', 'id_card', $id, 'ID card number already exists');
        
        if ($validator->fails()) {
            if (isset($_POST['ajax'])) {
                errorResponse('Validation failed', 400);
            }
            
            setFlashMessage('error', 'Please correct the errors below');
            $errors = $validator->getErrors();
            include __DIR__ . '/../views/admin/employees/edit.php';
            return;
        }
        
        // Handle file upload
        $profileImage = $employee['profile_image']; // Keep existing image
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['profile_image'], UPLOAD_PATH . 'profiles/');
            if ($uploadResult['success']) {
                // Delete old image
                if ($employee['profile_image']) {
                    deleteFile(UPLOAD_PATH . 'profiles/' . $employee['profile_image']);
                }
                $profileImage = $uploadResult['filename'];
            } else {
                if (isset($_POST['ajax'])) {
                    errorResponse($uploadResult['message'], 400);
                }
                setFlashMessage('error', $uploadResult['message']);
                include __DIR__ . '/../views/admin/employees/edit.php';
                return;
            }
        }
        
        // Prepare data
        $data = [
            'title_name' => $_POST['title_name'] ?? null,
            'first_name' => $_POST['first_name'],
            'last_name' => $_POST['last_name'],
            'nickname' => $_POST['nickname'] ?? null,
            'id_card' => $_POST['id_card'] ?? null,
            'birth_date' => $_POST['birth_date'] ?? null,
            'gender' => $_POST['gender'] ?? null,
            'phone' => $_POST['phone'] ?? null,
            'email' => $_POST['email'] ?? null,
            'line_id' => $_POST['line_id'] ?? null,
            'address' => $_POST['address'] ?? null,
            'emergency_contact_name' => $_POST['emergency_contact_name'] ?? null,
            'emergency_contact_phone' => $_POST['emergency_contact_phone'] ?? null,
            'position' => $_POST['position'] ?? null,
            'department' => $_POST['department'] ?? null,
            'hire_date' => $_POST['hire_date'] ?? null,
            'employment_status' => $_POST['employment_status'] ?? 'active',
            'profile_image' => $profileImage
        ];
        
        $result = $this->employeeModel->update($id, $data);
        
        if (isset($_POST['ajax'])) {
            if ($result['success']) {
                successResponse($result['message']);
            } else {
                errorResponse($result['message'], 400);
            }
        }
        
        if ($result['success']) {
            setFlashMessage('success', $result['message']);
            redirect(BASE_URL . '/admin/employees');
        } else {
            setFlashMessage('error', $result['message']);
            include __DIR__ . '/../views/admin/employees/edit.php';
        }
    }
    
    /**
     * Delete employee
     */
    public function delete() {
        requirePermission('employees.delete');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            errorResponse('Invalid CSRF token', 403);
        }
        
        $id = $_POST['id'] ?? null;
        if (!$id) {
            errorResponse('Employee ID is required', 400);
        }
        
        $result = $this->employeeModel->delete($id);
        
        if ($result['success']) {
            successResponse($result['message']);
        } else {
            errorResponse($result['message'], 400);
        }
    }
    
    /**
     * Get employee data for AJAX
     */
    public function getData() {
        requirePermission('employees.view');
        
        $id = $_GET['id'] ?? null;
        if (!$id) {
            errorResponse('Employee ID is required', 400);
        }
        
        $employee = $this->employeeModel->getById($id);
        if (!$employee) {
            errorResponse('Employee not found', 404);
        }
        
        successResponse('Employee data retrieved', $employee);
    }
}
