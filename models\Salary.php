<?php
/**
 * Salary Model
 * Human Resources Center System
 */

class Salary {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new Database();
        $this->logger = new Logger();
    }
    
    /**
     * Get current salary for employee
     */
    public function getCurrentSalary($employeeId) {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT * FROM salaries
                    WHERE employee_id = ? AND is_active = 1
                    ORDER BY effective_date DESC
                    LIMIT 1";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([$employeeId]);

            return $stmt->fetch();

        } catch (Exception $e) {
            error_log("Get current salary error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'get_current_salary_error', $e->getMessage(), ['employee_id' => $employeeId]);
            return null;
        }
    }

    /**
     * Get salary by employee ID (alias for getCurrentSalary)
     */
    public function getByEmployeeId($employeeId) {
        return $this->getCurrentSalary($employeeId);
    }

    /**
     * Get count of employees with salary
     */
    public function getEmployeesWithSalaryCount() {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT COUNT(DISTINCT employee_id) as count
                    FROM salaries
                    WHERE is_active = 1";

            $stmt = $pdo->prepare($sql);
            $stmt->execute();

            $result = $stmt->fetch();
            return $result['count'] ?? 0;

        } catch (Exception $e) {
            error_log("Get employees with salary count error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get average salary
     */
    public function getAverageSalary() {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT AVG(basic_salary + allowances) as avg_salary
                    FROM salaries
                    WHERE is_active = 1";

            $stmt = $pdo->prepare($sql);
            $stmt->execute();

            $result = $stmt->fetch();
            return $result['avg_salary'] ?? 0;

        } catch (Exception $e) {
            error_log("Get average salary error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get total payroll
     */
    public function getTotalPayroll() {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT SUM(basic_salary + allowances) as total_payroll
                    FROM salaries
                    WHERE is_active = 1";

            $stmt = $pdo->prepare($sql);
            $stmt->execute();

            $result = $stmt->fetch();
            return $result['total_payroll'] ?? 0;

        } catch (Exception $e) {
            error_log("Get total payroll error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Deactivate old salaries for employee
     */
    public function deactivateOldSalaries($employeeId) {
        try {
            $pdo = $this->db->getConnection();

            $sql = "UPDATE salaries SET is_active = 0 WHERE employee_id = ? AND is_active = 1";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$employeeId]);

            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'salary_deactivated',
                    "Old salaries deactivated for employee ID: {$employeeId}",
                    ['employee_id' => $employeeId]
                );

                return ['success' => true, 'message' => 'Old salaries deactivated successfully'];
            }

            return ['success' => false, 'message' => 'Failed to deactivate old salaries'];

        } catch (Exception $e) {
            error_log("Deactivate old salaries error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'deactivate_old_salaries_error', $e->getMessage(), ['employee_id' => $employeeId]);
            return ['success' => false, 'message' => 'Failed to deactivate old salaries: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get salary history for employee
     */
    public function getSalaryHistory($employeeId) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT s.*, u.username as created_by_username
                    FROM salaries s
                    LEFT JOIN users u ON s.created_by = u.id
                    WHERE s.employee_id = ?
                    ORDER BY s.effective_date DESC";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$employeeId]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get salary history error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create new salary record
     */
    public function create($data) {
        try {
            $pdo = $this->db->getConnection();
            $pdo->beginTransaction();
            
            // Deactivate previous salary records
            $sql = "UPDATE salaries SET is_active = 0 WHERE employee_id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$data['employee_id']]);
            
            // Insert new salary record
            $sql = "INSERT INTO salaries (
                        employee_id, basic_salary, allowances, overtime_rate,
                        effective_date, is_active, created_by
                    ) VALUES (?, ?, ?, ?, ?, 1, ?)";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['employee_id'],
                $data['basic_salary'],
                $data['allowances'] ?? 0,
                $data['overtime_rate'] ?? 0,
                $data['effective_date'],
                getCurrentUserId()
            ]);
            
            if ($result) {
                $salaryId = $pdo->lastInsertId();
                $pdo->commit();
                
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'salary_created',
                    "New salary record created for employee ID: {$data['employee_id']}",
                    ['salary_id' => $salaryId, 'employee_id' => $data['employee_id']]
                );
                
                return ['success' => true, 'id' => $salaryId, 'message' => 'สร้างข้อมูลเงินเดือนเรียบร้อยแล้ว'];
            }
            
            $pdo->rollback();
            return ['success' => false, 'message' => 'ไม่สามารถสร้างข้อมูลเงินเดือนได้'];
            
        } catch (Exception $e) {
            $pdo->rollback();
            error_log("Create salary error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'create_salary_error', $e->getMessage(), $data);
            return ['success' => false, 'message' => 'ไม่สามารถสร้างข้อมูลเงินเดือนได้: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update salary record
     */
    public function update($id, $data) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE salaries SET 
                        basic_salary = ?, allowances = ?, overtime_rate = ?,
                        effective_date = ?, updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['basic_salary'],
                $data['allowances'] ?? 0,
                $data['overtime_rate'] ?? 0,
                $data['effective_date'],
                $id
            ]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'salary_updated',
                    "Salary record updated: ID {$id}",
                    ['salary_id' => $id]
                );
                
                return ['success' => true, 'message' => 'อัปเดตข้อมูลเงินเดือนเรียบร้อยแล้ว'];
            }
            
            return ['success' => false, 'message' => 'ไม่สามารถอัปเดตข้อมูลเงินเดือนได้'];
            
        } catch (Exception $e) {
            error_log("Update salary error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'update_salary_error', $e->getMessage(), ['salary_id' => $id]);
            return ['success' => false, 'message' => 'ไม่สามารถอัปเดตข้อมูลเงินเดือนได้: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete salary record
     */
    public function delete($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "DELETE FROM salaries WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'salary_deleted',
                    "Salary record deleted: ID {$id}",
                    ['salary_id' => $id]
                );
                
                return ['success' => true, 'message' => 'Salary record deleted successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to delete salary record'];
            
        } catch (Exception $e) {
            error_log("Delete salary error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'delete_salary_error', $e->getMessage(), ['salary_id' => $id]);
            return ['success' => false, 'message' => 'Failed to delete salary record: ' . $e->getMessage()];
        }
    }
}
