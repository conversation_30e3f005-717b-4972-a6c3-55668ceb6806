<?php
// Prevent any output before JSON response
ob_start();

// Set JSON content type
header('Content-Type: application/json');

// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("API Fatal Error: {$error['message']} in {$error['file']} on line {$error['line']}");

        // Clean any output buffer
        while (ob_get_level()) {
            ob_end_clean();
        }

        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Fatal server error',
            'debug_info' => [
                'error_message' => $error['message'],
                'file' => basename($error['file']),
                'line' => $error['line'],
                'type' => $error['type'],
                'php_version' => phpversion(),
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]);
        exit;
    }
});

// Error handling
set_error_handler(function($severity, $message, $file, $line) {
    error_log("API PHP Error: $message in $file on line $line");

    // Clean any output buffer
    while (ob_get_level()) {
        ob_end_clean();
    }

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'debug_info' => [
            'error_message' => $message,
            'file' => basename($file),
            'line' => $line,
            'severity' => $severity,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
    exit;
});

try {
    // Try different config paths for production compatibility
    $configPaths = [
        '../config/config.php',
        dirname(__DIR__) . '/config/config.php',
        __DIR__ . '/../config/config.php'
    ];

    $configLoaded = false;
    foreach ($configPaths as $configPath) {
        if (file_exists($configPath)) {
            require_once $configPath;
            $configLoaded = true;
            break;
        }
    }

    if (!$configLoaded) {
        throw new Exception('Configuration file not found in any expected location');
    }

    // Helper function for JSON responses (check if not already declared)
    if (!function_exists('jsonResponse')) {
        function jsonResponse($data, $statusCode = 200) {
            ob_end_clean();
            http_response_code($statusCode);
            echo json_encode($data);
            exit;
        }
    }

    // Check if required constants are defined
    if (!defined('BASE_URL')) {
        throw new Exception('BASE_URL constant not defined');
    }

    if (!defined('DB_HOST')) {
        throw new Exception('Database constants not defined');
    }

    // Check if required classes exist
    if (!class_exists('Auth')) {
        throw new Exception('Auth class not found');
    }

    if (!class_exists('Employee')) {
        throw new Exception('Employee class not found');
    }

    if (!class_exists('Database')) {
        throw new Exception('Database class not found');
    }

    // Test database connection
    try {
        $db = new Database();
        $pdo = $db->getConnection();
        if (!$pdo) {
            throw new Exception('Database connection failed');
        }
    } catch (Exception $e) {
        error_log("Database connection error: " . $e->getMessage());
        jsonResponse(['success' => false, 'message' => 'Database connection error'], 500);
    }

    $auth = new Auth();
    if (!$auth->isAuthenticated()) {
        jsonResponse(['success' => false, 'message' => 'ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบ'], 401);
    }

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Debug: Log the path parts for troubleshooting (only in development)
if (defined('DEBUG') && DEBUG) {
    error_log("API Path: " . $path);
    error_log("Path Parts: " . print_r($pathParts, true));
}

// Get employee ID from URL path or query parameter
$employeeId = null;
if (isset($pathParts[2]) && is_numeric($pathParts[2])) {
    $employeeId = (int)$pathParts[2];
} elseif (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $employeeId = (int)$_GET['id'];
}

    // Remove EmployeeController dependency for now
    // $employeeController = new EmployeeController();

    error_log("API: Processing method: " . $method);
    error_log("API: Employee ID: " . ($employeeId ?? 'null'));

    switch ($method) {
        case 'GET':
            error_log("API: Entering GET case");
            if ($employeeId) {
                // Get single employee
                if (!function_exists('requirePermission')) {
                    jsonResponse(['success' => false, 'message' => 'Permission function not available'], 500);
                }

                try {
                    requirePermission('employees.view');
                } catch (Exception $e) {
                    error_log("Permission error: " . $e->getMessage());
                    jsonResponse(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง'], 403);
                }

                try {
                    $employeeModel = new Employee();

                    if (!method_exists($employeeModel, 'getById')) {
                        throw new Exception('Employee getById method not found');
                    }

                    $employee = $employeeModel->getById($employeeId);

                    if (!$employee) {
                        jsonResponse(['success' => false, 'message' => 'Employee not found'], 404);
                    }
                } catch (Exception $e) {
                    error_log("Employee model error: " . $e->getMessage());
                    jsonResponse(['success' => false, 'message' => 'Failed to retrieve employee data'], 500);
                }

                jsonResponse([
                    'success' => true,
                    'data' => $employee,
                    'message' => 'Employee data retrieved successfully'
                ]);
            } else {
                // Get all employees (for AJAX table)
                try {
                    requirePermission('employees.view');
                } catch (Exception $e) {
                    jsonResponse(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง'], 403);
                }

                $employeeModel = new Employee();

                $page = $_GET['page'] ?? 1;
                $search = $_GET['search'] ?? null;
                $department = $_GET['department'] ?? null;
                $status = $_GET['status'] ?? null;

                try {
                    $limit = defined('RECORDS_PER_PAGE') ? RECORDS_PER_PAGE : 10;
                    $offset = ($page - 1) * $limit;

                    $employees = $employeeModel->getAll($limit, $offset, $search, $department, $status);
                    $totalRecords = $employeeModel->count($search, $department, $status);

                    if (function_exists('paginate')) {
                        $pagination = paginate($totalRecords, $page, $limit);
                    } else {
                        $pagination = [
                            'current_page' => $page,
                            'total_pages' => ceil($totalRecords / $limit),
                            'total_records' => $totalRecords
                        ];
                    }

                    jsonResponse([
                        'success' => true,
                        'data' => $employees,
                        'pagination' => $pagination
                    ]);
                } catch (Exception $e) {
                    error_log("Employee getAll error: " . $e->getMessage());
                    jsonResponse(['success' => false, 'message' => 'Failed to retrieve employees'], 500);
                }
            }
            break;
            
        case 'POST':
            requirePermission('employees.create');

            // Validate CSRF token (check both POST data and headers)
            $csrfToken = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRFTOKEN'] ?? '';
            if (!verifyCSRFToken($csrfToken)) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'โทเค็น CSRF ไม่ถูกต้อง']);
                exit;
            }

            // Validate required fields
            if (empty($_POST['first_name']) || empty($_POST['last_name'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'First name and last name are required']);
                exit;
            }

            $employeeModel = new Employee();
            $result = $employeeModel->create($_POST);

            if ($result['success']) {
                ob_end_clean();
                echo json_encode([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => ['id' => $result['id']]
                ]);
            } else {
                ob_end_clean();
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $result['message']]);
            }
            break;
            
        case 'PUT':
            error_log("API: Entering PUT case");
            try {
                error_log("API: Checking PUT permissions");
                requirePermission('employees.edit');
                error_log("API: PUT permissions OK");
            } catch (Exception $e) {
                error_log("PUT Permission error: " . $e->getMessage());
                jsonResponse(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง'], 403);
            }

            if (!$employeeId) {
                jsonResponse(['success' => false, 'message' => 'ต้องระบุรหัสพนักงาน'], 400);
            }

            // Parse PUT data (handle both form data and JSON)
            $putData = [];

            try {
                $contentType = $_SERVER['CONTENT_TYPE'] ?? '';

                // Debug logging (only in development)
                if (defined('DEBUG') && DEBUG) {
                    error_log("PUT Content-Type: " . $contentType);
                    error_log("PUT POST data: " . print_r($_POST, true));
                    error_log("PUT FILES data: " . print_r($_FILES, true));
                }

                if (strpos($contentType, 'multipart/form-data') !== false) {
                    // For multipart/form-data, use $_POST and $_FILES
                    $putData = $_POST;
                    if (!empty($_FILES)) {
                        $putData['files'] = $_FILES;
                    }
                } else {
                    // For other content types, parse the raw input
                    $rawInput = file_get_contents('php://input');
                    if (defined('DEBUG') && DEBUG) {
                        error_log("PUT raw input: " . $rawInput);
                    }

                    if (!empty($rawInput)) {
                        parse_str($rawInput, $putData);
                    }
                }

                if (defined('DEBUG') && DEBUG) {
                    error_log("PUT parsed data: " . print_r($putData, true));
                    error_log("Employee ID: " . $employeeId);
                }

            } catch (Exception $e) {
                error_log("PUT data parsing error: " . $e->getMessage());
                jsonResponse(['success' => false, 'message' => 'Failed to parse request data'], 400);
            }

            // Validate CSRF token (check both form data and headers)
            try {
                $csrfToken = $putData['csrf_token'] ?? $_SERVER['HTTP_X_CSRFTOKEN'] ?? '';

                if (!function_exists('verifyCSRFToken')) {
                    error_log("PUT error: verifyCSRFToken function not found");
                    jsonResponse(['success' => false, 'message' => 'CSRF validation not available'], 500);
                }

                if (!verifyCSRFToken($csrfToken)) {
                    jsonResponse(['success' => false, 'message' => 'โทเค็น CSRF ไม่ถูกต้อง'], 403);
                }
            } catch (Exception $e) {
                error_log("PUT CSRF validation error: " . $e->getMessage());
                jsonResponse(['success' => false, 'message' => 'การตรวจสอบ CSRF ล้มเหลว'], 403);
            }

            // Validate required fields
            if (empty($putData['first_name']) || empty($putData['last_name'])) {
                jsonResponse(['success' => false, 'message' => 'ต้องระบุชื่อและนามสกุล'], 400);
            }

            try {
                $employeeModel = new Employee();

                if (!method_exists($employeeModel, 'update')) {
                    throw new Exception('Employee update method not found');
                }

                $result = $employeeModel->update($employeeId, $putData);

                if ($result['success']) {
                    jsonResponse([
                        'success' => true,
                        'message' => $result['message']
                    ]);
                } else {
                    jsonResponse(['success' => false, 'message' => $result['message']], 400);
                }

            } catch (Exception $e) {
                error_log("PUT Employee update error: " . $e->getMessage());
                error_log("PUT Employee ID: " . $employeeId);
                error_log("PUT Data: " . print_r($putData, true));
                jsonResponse([
                    'success' => false,
                    'message' => 'Failed to update employee',
                    'debug_info' => [
                        'error' => $e->getMessage(),
                        'employee_id' => $employeeId,
                        'timestamp' => date('Y-m-d H:i:s')
                    ]
                ], 500);
            }
            break;
            
        case 'DELETE':
            requirePermission('employees.delete');

            if (!$employeeId) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'ต้องระบุรหัสพนักงาน']);
                exit;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Validate CSRF token (check both JSON data and headers)
            $csrfToken = $input['csrf_token'] ?? $_SERVER['HTTP_X_CSRFTOKEN'] ?? '';
            if (!verifyCSRFToken($csrfToken)) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'โทเค็น CSRF ไม่ถูกต้อง']);
                exit;
            }

            $employeeModel = new Employee();
            $result = $employeeModel->delete($employeeId);

            if ($result['success']) {
                ob_end_clean();
                echo json_encode([
                    'success' => true,
                    'message' => $result['message']
                ]);
            } else {
                ob_end_clean();
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => $result['message']]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'วิธีการเรียกใช้ไม่ได้รับอนุญาต']);
            break;
    }

} catch (Exception $e) {
    error_log("API Exception caught: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    error_log("API Method: " . ($method ?? 'unknown'));
    error_log("API Employee ID: " . ($employeeId ?? 'null'));
    error_log("Stack trace: " . $e->getTraceAsString());

    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error_code' => $e->getCode(),
        'debug_info' => [
            'error_message' => $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'method' => $method ?? 'unknown',
            'employee_id' => $employeeId ?? null,
            'php_version' => phpversion(),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
} catch (Error $e) {
    error_log("API Fatal Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    error_log("API Method: " . ($method ?? 'unknown'));
    error_log("API Employee ID: " . ($employeeId ?? 'null'));

    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Fatal server error',
        'debug_info' => [
            'error_message' => $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'method' => $method ?? 'unknown',
            'employee_id' => $employeeId ?? null,
            'php_version' => phpversion(),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
}
?>
