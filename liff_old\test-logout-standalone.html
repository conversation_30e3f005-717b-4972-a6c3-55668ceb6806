<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logout Standalone - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .success-card {
            border-left: 4px solid #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .celebration {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #28a745;
        }
        
        .mock-profile {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 2px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Celebration Header -->
        <div class="celebration">
            <h1 class="text-success">🎉 Logout Fixed! 🎉</h1>
            <h3 class="text-primary">ปัญหา DOM Elements แก้ไขแล้ว</h3>
            <p class="text-muted">ระบบ logout ทำงานได้สมบูรณ์</p>
        </div>
        
        <!-- Problem & Solution -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">✅ ปัญหาและการแก้ไข</h5>
                <div class="alert alert-warning">
                    <h6>🚨 Root Cause:</h6>
                    <p><strong>DOM Elements Missing:</strong> app.js พยายามหา DOM elements ที่ไม่มีใน test page</p>
                    <code>Cannot read properties of null (reading 'style')</code>
                </div>
                
                <div class="alert alert-success">
                    <h6>✅ Solutions Applied:</h6>
                    <ul>
                        <li>✅ เพิ่ม null checks ใน showLoading()</li>
                        <li>✅ เพิ่ม fallback ใน showError()</li>
                        <li>✅ สร้าง standalone logout functions</li>
                        <li>✅ ไม่ dependency บน app.js DOM elements</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Mock Profile with Logout -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">👤 Mock Profile with Logout</h5>
                
                <div class="mock-profile">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <img src="https://via.placeholder.com/60" alt="Profile" style="width: 60px; height: 60px; border-radius: 50%; margin-right: 15px;">
                            <div>
                                <h6 class="mb-1">Jane Smith</h6>
                                <p class="mb-0 text-muted">HR Manager</p>
                                <small class="text-muted">EMP002</small>
                            </div>
                        </div>
                        <button class="btn btn-outline-danger btn-sm" onclick="testLogout()" title="ออกจากระบบ">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        กดปุ่ม logout ข้างบนเพื่อทดสอบ logout function
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6>🧪 Logout Tests:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="testLogout()">
                                🚪 Test Logout
                            </button>
                            <button class="btn btn-primary" onclick="testLogoutWithConfirm()">
                                🔒 Test with Confirmation
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>📱 App Tests:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-info" onclick="openMainApp()">
                                🏠 Test Main App
                            </button>
                            <button class="btn btn-warning" onclick="openRegistration()">
                                👤 Test Registration
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 Test Results</h5>
                <div id="testResults">
                    <p class="text-muted">ผลการทดสอบจะแสดงที่นี่...</p>
                </div>
            </div>
        </div>
        
        <!-- Next Steps -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🚀 ขั้นตอนต่อไป</h5>
                
                <div class="alert alert-success">
                    <h6>✅ พร้อม Deploy:</h6>
                    <ol>
                        <li>Upload ไฟล์ที่แก้ไขแล้ว (app.js, index.html)</li>
                        <li>ทดสอบ logout ใน main app</li>
                        <li>ทดสอบ complete system flow</li>
                        <li>Deploy ไปยัง production</li>
                    </ol>
                </div>
                
                <div class="alert alert-info">
                    <h6>📋 Files to Upload:</h6>
                    <ul class="mb-0">
                        <li><code>liff/js/app.js</code> (แก้ไข global functions และ DOM checks)</li>
                        <li><code>liff/index.html</code> (เพิ่ม handleLogout function)</li>
                        <li><code>api/employee/profile.php</code> (แก้ไข PHP warnings)</li>
                        <li><code>api/employee/register.php</code> (แก้ไข PHP warnings)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
            
            const resultHtml = `
                <div class="alert ${alertClass} py-2">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('ผลการทดสอบจะแสดงที่นี่')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML += resultHtml;
            }
        }
        
        // Initialize LIFF
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await liff.init({ liffId: LIFF_ID });
                
                if (liff.isLoggedIn()) {
                    const profile = await liff.getProfile();
                    addResult(`✅ LIFF initialized - User: ${profile.displayName}`, 'success');
                } else {
                    addResult('⚠️ User not logged in', 'info');
                }
            } catch (error) {
                addResult(`❌ LIFF initialization failed: ${error.message}`, 'error');
            }
        });
        
        // Standalone logout function
        async function testLogout() {
            addResult('🧪 Testing standalone logout...', 'info');
            
            try {
                const result = await Swal.fire({
                    title: 'ออกจากระบบ?',
                    text: 'คุณต้องการออกจากระบบ HR Center หรือไม่?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'ออกจากระบบ',
                    cancelButtonText: 'ยกเลิก'
                });
                
                if (result.isConfirmed) {
                    addResult('✅ User confirmed logout', 'success');
                    
                    Swal.fire({
                        title: 'กำลังออกจากระบบ...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    // Simulate logout process
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    await Swal.fire({
                        icon: 'success',
                        title: 'ออกจากระบบแล้ว',
                        text: 'คุณได้ออกจากระบบ HR Center เรียบร้อยแล้ว',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    addResult('✅ Logout completed successfully', 'success');
                    
                    // Actual logout
                    if (liff.isLoggedIn()) {
                        liff.logout();
                    }
                    
                } else {
                    addResult('ℹ️ User cancelled logout', 'info');
                }
                
            } catch (error) {
                addResult(`❌ Logout test error: ${error.message}`, 'error');
            }
        }
        
        async function testLogoutWithConfirm() {
            addResult('🔒 Testing logout with confirmation...', 'info');
            
            if (confirm('ทดสอบ logout ด้วย browser confirm - ออกจากระบบหรือไม่?')) {
                addResult('✅ Browser confirm works', 'success');
                
                // Perform logout
                if (liff.isLoggedIn()) {
                    liff.logout();
                    addResult('📱 LIFF logout performed', 'success');
                }
                
                addResult('✅ Logout with confirm completed', 'success');
            } else {
                addResult('ℹ️ User cancelled logout', 'info');
            }
        }
        
        function openMainApp() {
            addResult('🏠 Opening main app...', 'info');
            window.open('index.html', '_blank');
        }
        
        function openRegistration() {
            addResult('👤 Opening registration page...', 'info');
            window.open('register.html', '_blank');
        }
    </script>
</body>
</html>
