<?php
/**
 * User Model
 * Human Resources Center System
 */

class User {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new Database();
        $this->logger = new Logger();
    }
    
    /**
     * Get all users
     */
    public function getAll($limit = null, $offset = 0, $search = null, $roleId = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT u.*, r.name as role_name 
                    FROM users u 
                    JOIN roles r ON u.role_id = r.id 
                    WHERE 1=1";
            $params = [];
            
            if ($search) {
                $sql .= " AND (u.username LIKE ? OR u.email LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if ($roleId) {
                $sql .= " AND u.role_id = ?";
                $params[] = $roleId;
            }
            
            $sql .= " ORDER BY u.username";
            
            if ($limit) {
                $sql .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get users error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get user by ID
     */
    public function getById($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT u.*, r.name as role_name, r.permissions 
                    FROM users u 
                    JOIN roles r ON u.role_id = r.id 
                    WHERE u.id = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get user by ID error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get users by role
     */
    public function getUsersByRole($roleId) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT u.*, r.name as role_name 
                    FROM users u 
                    JOIN roles r ON u.role_id = r.id 
                    WHERE u.role_id = ? AND u.is_active = 1";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$roleId]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get users by role error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create new user
     */
    public function create($data) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "INSERT INTO users (username, email, password_hash, role_id, is_active) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['username'],
                $data['email'],
                hashPassword($data['password']),
                $data['role_id'],
                $data['is_active'] ?? 1
            ]);
            
            if ($result) {
                $userId = $pdo->lastInsertId();
                
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'user_created',
                    "User created: {$data['username']}",
                    ['user_id' => $userId]
                );
                
                return ['success' => true, 'id' => $userId, 'message' => 'User created successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to create user'];
            
        } catch (Exception $e) {
            error_log("Create user error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'create_user_error', $e->getMessage(), $data);
            return ['success' => false, 'message' => 'Failed to create user: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update user
     */
    public function update($id, $data) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE users SET username = ?, email = ?, role_id = ?, is_active = ?, updated_at = NOW()";
            $params = [$data['username'], $data['email'], $data['role_id'], $data['is_active']];
            
            // Update password if provided
            if (!empty($data['password'])) {
                $sql .= ", password_hash = ?";
                $params[] = hashPassword($data['password']);
            }
            
            $sql .= " WHERE id = ?";
            $params[] = $id;
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'user_updated',
                    "User updated: {$data['username']}",
                    ['user_id' => $id]
                );
                
                return ['success' => true, 'message' => 'User updated successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to update user'];
            
        } catch (Exception $e) {
            error_log("Update user error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'update_user_error', $e->getMessage(), ['user_id' => $id]);
            return ['success' => false, 'message' => 'Failed to update user: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete user
     */
    public function delete($id) {
        try {
            $pdo = $this->db->getConnection();
            
            // Get user info before deletion
            $user = $this->getById($id);
            if (!$user) {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            $sql = "DELETE FROM users WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(),
                    'user_deleted',
                    "User deleted: {$user['username']}",
                    ['user_id' => $id]
                );
                
                return ['success' => true, 'message' => 'User deleted successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to delete user'];
            
        } catch (Exception $e) {
            error_log("Delete user error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'delete_user_error', $e->getMessage(), ['user_id' => $id]);
            return ['success' => false, 'message' => 'Failed to delete user: ' . $e->getMessage()];
        }
    }
    
    /**
     * Count users
     */
    public function count($search = null, $roleId = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT COUNT(*) FROM users u WHERE 1=1";
            $params = [];
            
            if ($search) {
                $sql .= " AND (u.username LIKE ? OR u.email LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if ($roleId) {
                $sql .= " AND u.role_id = ?";
                $params[] = $roleId;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log("Count users error: " . $e->getMessage());
            return 0;
        }
    }
}
