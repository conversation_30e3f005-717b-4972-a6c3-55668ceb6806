<?php
require_once '../../config/config.php';

$auth = new Auth();
if (!$auth->isAuthenticated()) {
    redirect(BASE_URL . '/login.php');
}

requirePermission('salary.export');

try {
    $salaryController = new SalaryController();
    $salaryController->exportReport();
    
} catch (Exception $e) {
    error_log("Salary export error: " . $e->getMessage());
    http_response_code(500);
    echo "Export failed: " . $e->getMessage();
}
?>
