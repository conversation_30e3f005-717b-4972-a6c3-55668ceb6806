<?php
/**
 * Authentication Class
 * Human Resources Center System
 */

class Auth {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new Database();
        $this->logger = new Logger();
        secureSessionStart();
    }
    
    /**
     * Login user
     */
    public function login($username, $password, $rememberMe = false) {
        try {
            // Check rate limiting
            if (!checkLoginAttempts($username)) {
                $this->logger->logSecurity(null, 'login_rate_limit', "Rate limit exceeded for username: {$username}");
                return ['success' => false, 'message' => 'Too many login attempts. Please try again later.'];
            }
            
            $pdo = $this->db->getConnection();
            
            // Get user with role information
            $sql = "SELECT u.*, r.name as role_name, r.permissions 
                    FROM users u 
                    JOIN roles r ON u.role_id = r.id 
                    WHERE u.username = ? AND u.is_active = 1";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if (!$user) {
                $this->logger->logSecurity(null, 'login_failed', "Login failed - user not found: {$username}");
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            // Check if account is locked
            if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                $this->logger->logSecurity($user['id'], 'login_locked', "Login attempt on locked account: {$username}");
                return ['success' => false, 'message' => 'Account is temporarily locked'];
            }
            
            // Verify password
            if (!verifyPassword($password, $user['password_hash'])) {
                // Increment login attempts
                $this->incrementLoginAttempts($user['id']);
                
                $this->logger->logSecurity($user['id'], 'login_failed', "Login failed - invalid password: {$username}");
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            // Reset login attempts on successful login
            $this->resetLoginAttempts($user['id']);
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_role'] = $user['role_id'];
            $_SESSION['role_name'] = $user['role_name'];
            $_SESSION['user_permissions'] = json_decode($user['permissions'], true);
            $_SESSION['last_activity'] = time();
            
            // Handle remember me
            if ($rememberMe) {
                $this->setRememberMeCookie($user['id']);
            }
            
            $this->logger->logAccess($user['id'], 'login', "User logged in: {$username}");
            
            return ['success' => true, 'message' => 'Login successful', 'user' => $user];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $this->logger->logError(null, 'login_error', $e->getMessage());
            return ['success' => false, 'message' => 'Login failed due to system error'];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        $userId = getCurrentUserId();
        
        if ($userId) {
            $this->logger->logAccess($userId, 'logout', "User logged out");
        }
        
        // Clear remember me cookie
        $this->clearRememberMeCookie();
        
        // Destroy session
        session_destroy();
        
        return ['success' => true, 'message' => 'Logged out successfully'];
    }
    
    /**
     * Check if user is authenticated
     */
    public function isAuthenticated() {
        if (!isLoggedIn()) {
            return false;
        }
        
        // Check session timeout
        if (!checkSessionTimeout()) {
            $this->logout();
            return false;
        }
        
        return true;
    }
    
    /**
     * Get current user
     */
    public function getCurrentUser() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT u.*, r.name as role_name, r.permissions 
                    FROM users u 
                    JOIN roles r ON u.role_id = r.id 
                    WHERE u.id = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([getCurrentUserId()]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get current user error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Change password
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            $pdo = $this->db->getConnection();
            
            // Get current user
            $sql = "SELECT password_hash FROM users WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            // Verify current password
            if (!verifyPassword($currentPassword, $user['password_hash'])) {
                $this->logger->logSecurity($userId, 'password_change_failed', "Invalid current password");
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }
            
            // Validate new password strength
            $passwordErrors = checkPasswordStrength($newPassword);
            if (!empty($passwordErrors)) {
                return ['success' => false, 'message' => implode(', ', $passwordErrors)];
            }
            
            // Update password
            $newPasswordHash = hashPassword($newPassword);
            $sql = "UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$newPasswordHash, $userId]);
            
            $this->logger->logActivity($userId, 'password_changed', "Password changed successfully");
            
            return ['success' => true, 'message' => 'Password changed successfully'];
            
        } catch (Exception $e) {
            error_log("Change password error: " . $e->getMessage());
            $this->logger->logError($userId, 'password_change_error', $e->getMessage());
            return ['success' => false, 'message' => 'Failed to change password'];
        }
    }
    
    /**
     * Reset password
     */
    public function resetPassword($email) {
        try {
            $pdo = $this->db->getConnection();
            
            // Check if email exists
            $sql = "SELECT id, username FROM users WHERE email = ? AND is_active = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if (!$user) {
                // Don't reveal if email exists or not
                return ['success' => true, 'message' => 'If the email exists, a reset link has been sent'];
            }
            
            // Generate reset token
            $resetToken = generateSecureToken();
            $resetExpiry = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // Store reset token (you might want to create a password_resets table)
            $_SESSION['password_reset'][$resetToken] = [
                'user_id' => $user['id'],
                'expires' => $resetExpiry
            ];
            
            // Send reset email (implement email sending)
            $resetLink = BASE_URL . "/reset-password.php?token=" . $resetToken;
            
            $this->logger->logSecurity($user['id'], 'password_reset_requested', "Password reset requested for: {$email}");
            
            return ['success' => true, 'message' => 'Password reset link has been sent to your email'];
            
        } catch (Exception $e) {
            error_log("Reset password error: " . $e->getMessage());
            $this->logger->logError(null, 'password_reset_error', $e->getMessage());
            return ['success' => false, 'message' => 'Failed to process password reset'];
        }
    }
    
    /**
     * Increment login attempts
     */
    private function incrementLoginAttempts($userId) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE users SET 
                    login_attempts = login_attempts + 1,
                    locked_until = CASE 
                        WHEN login_attempts + 1 >= ? THEN DATE_ADD(NOW(), INTERVAL ? SECOND)
                        ELSE locked_until 
                    END
                    WHERE id = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([MAX_LOGIN_ATTEMPTS, LOGIN_LOCKOUT_TIME, $userId]);
            
        } catch (Exception $e) {
            error_log("Increment login attempts error: " . $e->getMessage());
        }
    }
    
    /**
     * Reset login attempts
     */
    private function resetLoginAttempts($userId) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE users SET login_attempts = 0, locked_until = NULL WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$userId]);
            
        } catch (Exception $e) {
            error_log("Reset login attempts error: " . $e->getMessage());
        }
    }
    
    /**
     * Update last login
     */
    private function updateLastLogin($userId) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$userId]);
            
        } catch (Exception $e) {
            error_log("Update last login error: " . $e->getMessage());
        }
    }
    
    /**
     * Set remember me cookie
     */
    private function setRememberMeCookie($userId) {
        $token = generateSecureToken();
        $expiry = time() + (30 * 24 * 60 * 60); // 30 days
        
        setcookie('remember_token', $token, $expiry, '/', '', isset($_SERVER['HTTPS']), true);
        
        // Store token in session or database
        $_SESSION['remember_tokens'][$token] = [
            'user_id' => $userId,
            'expires' => $expiry
        ];
    }
    
    /**
     * Clear remember me cookie
     */
    private function clearRememberMeCookie() {
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/', '', isset($_SERVER['HTTPS']), true);
            unset($_SESSION['remember_tokens'][$_COOKIE['remember_token']]);
        }
    }
}
