<?php
/**
 * Employee Model
 * Human Resources Center System
 */

class Employee {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new Database();
        $this->logger = new Logger();
    }
    
    /**
     * Get all employees
     */
    public function getAll($limit = null, $offset = 0, $search = null, $department = null, $status = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT e.*, u.username, u.email as user_email 
                    FROM employees e 
                    LEFT JOIN users u ON e.user_id = u.id 
                    WHERE 1=1";
            $params = [];
            
            if ($search) {
                $sql .= " AND (e.first_name LIKE ? OR e.last_name LIKE ? OR e.employee_code LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if ($department) {
                $sql .= " AND e.department = ?";
                $params[] = $department;
            }
            
            if ($status) {
                $sql .= " AND e.employment_status = ?";
                $params[] = $status;
            }
            
            $sql .= " ORDER BY e.first_name, e.last_name";
            
            if ($limit) {
                $sql .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get employees error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'get_employees_error', $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count employees
     */
    public function count($search = null, $department = null, $status = null) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT COUNT(*) FROM employees WHERE 1=1";
            $params = [];
            
            if ($search) {
                $sql .= " AND (first_name LIKE ? OR last_name LIKE ? OR employee_code LIKE ?)";
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if ($department) {
                $sql .= " AND department = ?";
                $params[] = $department;
            }
            
            if ($status) {
                $sql .= " AND employment_status = ?";
                $params[] = $status;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log("Count employees error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get employee by ID
     */
    public function getById($id) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT e.*, u.username, u.email as user_email 
                    FROM employees e 
                    LEFT JOIN users u ON e.user_id = u.id 
                    WHERE e.id = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get employee by ID error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'get_employee_error', $e->getMessage(), ['employee_id' => $id]);
            return null;
        }
    }
    
    /**
     * Get employee by employee code
     */
    public function getByEmployeeCode($code) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT e.*, u.username, u.email as user_email 
                    FROM employees e 
                    LEFT JOIN users u ON e.user_id = u.id 
                    WHERE e.employee_code = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$code]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get employee by code error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get employee by user ID
     */
    public function getByUserId($userId) {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT e.*, u.username, u.email as user_email 
                    FROM employees e 
                    LEFT JOIN users u ON e.user_id = u.id 
                    WHERE e.user_id = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get employee by user ID error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get employee by LINE ID
     */
    public function getByLineId($lineId) {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT * FROM employees WHERE line_id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$lineId]);

            return $stmt->fetch();

        } catch (Exception $e) {
            error_log("Get employee by LINE ID error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get employee by employee code and phone
     */
    public function getByEmployeeCodeAndPhone($employeeCode, $phone) {
        try {
            $pdo = $this->db->getConnection();

            $sql = "SELECT * FROM employees WHERE employee_code = ? AND phone = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$employeeCode, $phone]);

            return $stmt->fetch();

        } catch (Exception $e) {
            error_log("Get employee by code and phone error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update employee LINE registration
     */
    public function updateLineRegistration($employeeId, $data) {
        try {
            $pdo = $this->db->getConnection();

            $sql = "UPDATE employees SET
                    line_id = ?,
                    line_display_name = ?,
                    line_picture_url = ?,
                    phone = ?,
                    line_registered_at = ?,
                    updated_at = NOW()
                    WHERE id = ?";

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['line_id'],
                $data['line_display_name'],
                $data['line_picture_url'],
                $data['phone'],
                $data['line_registered_at'],
                $employeeId
            ]);

            if ($result) {
                // Log the registration (simple error_log instead of Logger class)
                error_log("Employee LINE registration updated - Employee ID: {$employeeId}, LINE ID: {$data['line_id']}");

                return ['success' => true, 'message' => 'อัปเดตการลงทะเบียน LINE เรียบร้อยแล้ว'];
            }

            return ['success' => false, 'message' => 'ไม่สามารถอัปเดตการลงทะเบียน LINE ได้'];

        } catch (Exception $e) {
            error_log("Update LINE registration error: " . $e->getMessage());
            error_log("Employee ID: {$employeeId}, Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'ไม่สามารถอัปเดตการลงทะเบียน LINE ได้: ' . $e->getMessage()];
        }
    }

    /**
     * Create new employee
     */
    public function create($data) {
        try {
            $pdo = $this->db->getConnection();
            $pdo->beginTransaction();
            
            // Generate employee code if not provided
            if (empty($data['employee_code'])) {
                $data['employee_code'] = $this->generateEmployeeCode();
            }
            
            $sql = "INSERT INTO employees (
                        employee_code, user_id, title_name, first_name, last_name, nickname,
                        id_card, birth_date, gender, phone, email, line_id, address,
                        emergency_contact_name, emergency_contact_phone, position, department,
                        hire_date, employment_status, profile_image
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            // Validate gender value
            $validGenders = ['male', 'female', 'other'];
            $gender = null;
            if (!empty($data['gender']) && in_array($data['gender'], $validGenders)) {
                $gender = $data['gender'];
            }

            // Validate employment status
            $validStatuses = ['active', 'inactive', 'terminated'];
            $employmentStatus = 'active';
            if (!empty($data['employment_status']) && in_array($data['employment_status'], $validStatuses)) {
                $employmentStatus = $data['employment_status'];
            }

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['employee_code'],
                $data['user_id'] ?? null,
                $data['title_name'] ?? null,
                $data['first_name'],
                $data['last_name'],
                $data['nickname'] ?? null,
                $data['id_card'] ?? null,
                !empty($data['birth_date']) ? $data['birth_date'] : null,
                $gender,
                $data['phone'] ?? null,
                $data['email'] ?? null,
                $data['line_id'] ?? null,
                $data['address'] ?? null,
                $data['emergency_contact_name'] ?? null,
                $data['emergency_contact_phone'] ?? null,
                $data['position'] ?? null,
                $data['department'] ?? null,
                !empty($data['hire_date']) ? $data['hire_date'] : null,
                $employmentStatus,
                $data['profile_image'] ?? null
            ]);
            
            if ($result) {
                $employeeId = $pdo->lastInsertId();
                $pdo->commit();
                
                $this->logger->logActivity(
                    getCurrentUserId(), 
                    'employee_created', 
                    "Employee created: {$data['first_name']} {$data['last_name']}", 
                    ['employee_id' => $employeeId]
                );
                
                return ['success' => true, 'id' => $employeeId, 'message' => 'สร้างข้อมูลพนักงานเรียบร้อยแล้ว'];
            }
            
            $pdo->rollback();
            return ['success' => false, 'message' => 'ไม่สามารถสร้างข้อมูลพนักงานได้'];
            
        } catch (Exception $e) {
            $pdo->rollback();
            error_log("Create employee error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'create_employee_error', $e->getMessage(), $data);
            return ['success' => false, 'message' => 'ไม่สามารถสร้างข้อมูลพนักงานได้: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update employee
     */
    public function update($id, $data) {
        try {
            $pdo = $this->db->getConnection();

            // Get current employee data to compare changes
            $currentEmployee = $this->getById($id);
            if (!$currentEmployee) {
                return ['success' => false, 'message' => 'ไม่พบข้อมูลพนักงาน'];
            }

            // Check if id_card is being updated and if it already exists
            if (!empty($data['id_card']) && $data['id_card'] !== $currentEmployee['id_card']) {
                $checkSql = "SELECT id FROM employees WHERE id_card = ? AND id != ?";
                $checkStmt = $pdo->prepare($checkSql);
                $checkStmt->execute([$data['id_card'], $id]);

                if ($checkStmt->fetch()) {
                    return ['success' => false, 'message' => 'หมายเลขบัตรประชาชนนี้มีอยู่ในระบบแล้ว'];
                }
            }

            // Check if employee_code is being updated and if it already exists
            if (!empty($data['employee_code']) && $data['employee_code'] !== $currentEmployee['employee_code']) {
                $checkSql = "SELECT id FROM employees WHERE employee_code = ? AND id != ?";
                $checkStmt = $pdo->prepare($checkSql);
                $checkStmt->execute([$data['employee_code'], $id]);

                if ($checkStmt->fetch()) {
                    return ['success' => false, 'message' => 'รหัสพนักงานนี้มีอยู่ในระบบแล้ว'];
                }
            }

            $sql = "UPDATE employees SET
                        title_name = ?, first_name = ?, last_name = ?, nickname = ?,
                        id_card = ?, birth_date = ?, gender = ?, phone = ?, email = ?,
                        line_id = ?, address = ?, emergency_contact_name = ?,
                        emergency_contact_phone = ?, position = ?, department = ?,
                        hire_date = ?, employment_status = ?, profile_image = ?,
                        updated_at = NOW()
                    WHERE id = ?";

            // Validate gender value
            $validGenders = ['male', 'female', 'other'];
            $gender = null;
            if (!empty($data['gender']) && in_array($data['gender'], $validGenders)) {
                $gender = $data['gender'];
            }

            // Validate employment status
            $validStatuses = ['active', 'inactive', 'terminated'];
            $employmentStatus = 'active';
            if (!empty($data['employment_status']) && in_array($data['employment_status'], $validStatuses)) {
                $employmentStatus = $data['employment_status'];
            }

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                !empty($data['title_name']) ? $data['title_name'] : null,
                $data['first_name'],
                $data['last_name'],
                !empty($data['nickname']) ? $data['nickname'] : null,
                !empty($data['id_card']) ? $data['id_card'] : null,
                !empty($data['birth_date']) ? $data['birth_date'] : null,
                $gender,
                !empty($data['phone']) ? $data['phone'] : null,
                !empty($data['email']) ? $data['email'] : null,
                !empty($data['line_id']) ? $data['line_id'] : null,
                !empty($data['address']) ? $data['address'] : null,
                !empty($data['emergency_contact_name']) ? $data['emergency_contact_name'] : null,
                !empty($data['emergency_contact_phone']) ? $data['emergency_contact_phone'] : null,
                !empty($data['position']) ? $data['position'] : null,
                !empty($data['department']) ? $data['department'] : null,
                !empty($data['hire_date']) ? $data['hire_date'] : null,
                $employmentStatus,
                !empty($data['profile_image']) ? $data['profile_image'] : null,
                $id
            ]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(), 
                    'employee_updated', 
                    "Employee updated: {$data['first_name']} {$data['last_name']}", 
                    ['employee_id' => $id]
                );
                
                return ['success' => true, 'message' => 'อัปเดตข้อมูลพนักงานเรียบร้อยแล้ว'];
            }
            
            return ['success' => false, 'message' => 'ไม่สามารถอัปเดตข้อมูลพนักงานได้'];
            
        } catch (Exception $e) {
            error_log("Update employee error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'update_employee_error', $e->getMessage(), ['employee_id' => $id]);
            return ['success' => false, 'message' => 'ไม่สามารถอัปเดตข้อมูลพนักงานได้: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete employee
     */
    public function delete($id) {
        try {
            $pdo = $this->db->getConnection();
            
            // Get employee info before deletion
            $employee = $this->getById($id);
            if (!$employee) {
                return ['success' => false, 'message' => 'ไม่พบข้อมูลพนักงาน'];
            }
            
            $sql = "DELETE FROM employees WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $this->logger->logActivity(
                    getCurrentUserId(), 
                    'employee_deleted', 
                    "Employee deleted: {$employee['first_name']} {$employee['last_name']}", 
                    ['employee_id' => $id]
                );
                
                return ['success' => true, 'message' => 'ลบข้อมูลพนักงานเรียบร้อยแล้ว'];
            }
            
            return ['success' => false, 'message' => 'ไม่สามารถลบข้อมูลพนักงานได้'];
            
        } catch (Exception $e) {
            error_log("Delete employee error: " . $e->getMessage());
            $this->logger->logError(getCurrentUserId(), 'delete_employee_error', $e->getMessage(), ['employee_id' => $id]);
            return ['success' => false, 'message' => 'ไม่สามารถลบข้อมูลพนักงานได้: ' . $e->getMessage()];
        }
    }
    
    /**
     * Generate unique employee code
     */
    private function generateEmployeeCode() {
        $pdo = $this->db->getConnection();
        
        do {
            $code = generateEmployeeCode();
            $sql = "SELECT COUNT(*) FROM employees WHERE employee_code = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$code]);
            $exists = $stmt->fetchColumn() > 0;
        } while ($exists);
        
        return $code;
    }
    
    /**
     * Get departments
     */
    public function getDepartments() {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT DISTINCT department FROM employees WHERE department IS NOT NULL ORDER BY department";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
            
        } catch (Exception $e) {
            error_log("Get departments error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get positions
     */
    public function getPositions() {
        try {
            $pdo = $this->db->getConnection();
            
            $sql = "SELECT DISTINCT position FROM employees WHERE position IS NOT NULL ORDER BY position";
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
            
        } catch (Exception $e) {
            error_log("Get positions error: " . $e->getMessage());
            return [];
        }
    }
}
