# Human Resources Center (HRC) System

ระบบจัดการทรัพยากรบุคคลแบบครบวงจร พัฒนาด้วย PHP PDO MVC พร้อมระบบรักษาความปลอดภัย การบันทึก Log และการแจ้งเตือนผ่าน LINE และ Email

## คุณสมบัติหลัก

### 🔐 ระบบรักษาความปลอดภัย
- การเข้ารหัสรหัสผ่านด้วย PHP password_hash()
- ระบบ CSRF Token Protection
- Rate Limiting สำหรับการล็อกอิน
- Session Timeout Management
- Input Sanitization และ Validation
- Role-based Access Control

### 📊 ระบบจัดการพนักงาน (Admin/HR)
- จัดการข้อมูลพนักงาน (เพิ่ม ดู แก้ไข ลบ)
- จัดการข้อมูลเงินเดือน
- จัดการการลา การขาด การมาสาย
- จัดการสวัสดิการพนักงาน
- จัดการประกันสังคมและสถานพยาบาล
- ระบบ CRUD ผ่าน Modal พร้อม AJAX

### 📱 ระบบพนักงาน (LINE LIFF Application)
- ดูข้อมูลส่วนตัว
- ขออนุมัติการลา
- ดูประวัติการลา
- ขอสลิปเงินเดือน (เข้ารหัส)

### 🔔 ระบบแจ้งเตือน
- แจ้งเตือนผ่าน LINE Bot API
- แจ้งเตือนผ่าน Email (SMTP)
- แจ้งเตือนการขออนุมัติลา
- แจ้งเตือนผลการอนุมัติ
- แจ้งเตือนสลิปเงินเดือน

### 📝 ระบบบันทึก Log
- Access Log - การเข้าใช้งานระบบ
- Activity Log - กิจกรรมต่างๆ ในระบบ
- Error Log - ข้อผิดพลาดของระบบ
- Security Log - เหตุการณ์ด้านความปลอดภัย
- Log Archiving - เก็บ Log แยกตามวัน

## ความต้องการของระบบ

### Server Requirements
- PHP 7.4 หรือสูงกว่า
- MySQL 5.7 หรือ MariaDB 10.2
- Apache/Nginx Web Server
- SSL Certificate (สำหรับ Production)

### PHP Extensions
- PDO MySQL
- OpenSSL
- cURL
- JSON
- Fileinfo
- GD (สำหรับการจัดการรูปภาพ)

## การติดตั้ง

### 1. Clone Repository
```bash
git clone https://github.com/your-repo/hrc-system.git
cd hrc-system
```

### 2. ตั้งค่า Database
```bash
# สร้าง Database
mysql -u root -p
CREATE DATABASE hrc_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. ตั้งค่า Configuration
แก้ไขไฟล์ `config/config.php`:
```php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'hrc_system');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// LINE API Configuration
define('LINE_CHANNEL_ACCESS_TOKEN', 'your-line-channel-access-token');
define('LINE_CHANNEL_SECRET', 'your-line-channel-secret');
define('LINE_LIFF_ID', 'your-liff-id');

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

### 4. รันการติดตั้ง
เปิดเบราว์เซอร์และไปที่:
```
http://your-domain.com/setup.php
```

### 5. ตั้งค่า Web Server

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1.php [L]
```

#### Nginx
```nginx
location /api/ {
    try_files $uri $uri.php =404;
    fastcgi_pass php-fpm;
    include fastcgi_params;
}
```

## การใช้งาน

### ผู้ดูแลระบบ (Admin)
- URL: `http://your-domain.com/login.php`
- Username: `admin`
- Password: `admin123` (เปลี่ยนหลังติดตั้ง)

### HR Manager
- Username: `hr_manager`
- Password: `hr123` (เปลี่ยนหลังติดตั้ง)

### พนักงาน (Employee)
- เข้าใช้งานผ่าน LINE LIFF Application
- URL: `http://your-domain.com/liff/`

## โครงสร้างไฟล์

```
hrc-system/
├── config/                 # ไฟล์ตั้งค่า
│   ├── config.php
│   └── database.php
├── core/                   # Core Classes
│   ├── Auth.php
│   └── Logger.php
├── models/                 # Data Models
│   ├── Employee.php
│   ├── LeaveRequest.php
│   └── ...
├── controllers/            # Controllers
│   ├── EmployeeController.php
│   └── ...
├── views/                  # View Templates
│   ├── layouts/
│   ├── admin/
│   └── employee/
├── helpers/                # Helper Functions
│   ├── functions.php
│   ├── security.php
│   ├── validation.php
│   └── notification.php
├── api/                    # API Endpoints
│   ├── employees.php
│   └── ...
├── liff/                   # LINE LIFF Application
│   ├── index.html
│   └── js/app.js
├── public/                 # Public Assets
│   └── assets/
├── uploads/                # Upload Directory
├── logs/                   # Log Files
├── database/               # Database Schema
│   └── schema.sql
├── login.php               # Login Page
├── dashboard.php           # Dashboard
├── setup.php               # Setup Script
└── README.md
```

## การตั้งค่า LINE Bot และ LIFF

### 1. สร้าง LINE Bot
1. ไปที่ [LINE Developers Console](https://developers.line.biz/)
2. สร้าง Provider และ Channel ใหม่
3. เลือก Messaging API
4. คัดลอก Channel Access Token และ Channel Secret

### 2. สร้าง LIFF App
1. ในหน้า Channel Settings
2. ไปที่แท็บ LIFF
3. เพิ่ม LIFF App ใหม่
4. ตั้งค่า Endpoint URL: `https://your-domain.com/liff/`
5. คัดลอก LIFF ID

### 3. ตั้งค่า Webhook (ถ้าต้องการ)
- Webhook URL: `https://your-domain.com/webhook.php`

## การตั้งค่า Email

### Gmail SMTP
1. เปิดใช้งาน 2-Factor Authentication
2. สร้าง App Password
3. ใช้ App Password ในการตั้งค่า

## Security Best Practices

### 1. เปลี่ยนรหัสผ่านเริ่มต้น
```sql
UPDATE users SET password_hash = ? WHERE username = 'admin';
```

### 2. ตั้งค่า File Permissions
```bash
chmod 755 uploads/
chmod 644 config/config.php
chmod 600 logs/
```

### 3. ใช้ HTTPS
- ติดตั้ง SSL Certificate
- บังคับใช้ HTTPS

### 4. ตั้งค่า Firewall
- อนุญาตเฉพาะ Port ที่จำเป็น
- จำกัดการเข้าถึง Admin Panel

## การ Backup

### Database Backup
```bash
mysqldump -u username -p hrc_system > backup_$(date +%Y%m%d).sql
```

### File Backup
```bash
tar -czf hrc_backup_$(date +%Y%m%d).tar.gz /path/to/hrc-system/
```

## การ Monitoring

### Log Monitoring
- ตรวจสอบ Error Log ประจำ
- ติดตาม Security Log
- Archive Log เก่า

### Performance Monitoring
- ตรวจสอบ Database Performance
- Monitor Server Resources
- ตรวจสอบ Response Time

## การแก้ไขปัญหา

### ปัญหาการเชื่อมต่อ Database
1. ตรวจสอบ Database Credentials
2. ตรวจสอบ MySQL Service
3. ตรวจสอบ Firewall Settings

### ปัญหา LINE Notification
1. ตรวจสอบ Access Token
2. ตรวจสอบ SSL Certificate
3. ตรวจสอบ Network Connectivity

### ปัญหา Email
1. ตรวจสอบ SMTP Settings
2. ตรวจสอบ App Password
3. ตรวจสอบ Firewall Port 587

## การพัฒนาต่อ

### เพิ่มฟีเจอร์ใหม่
1. สร้าง Model ใหม่ใน `models/`
2. สร้าง Controller ใน `controllers/`
3. สร้าง View ใน `views/`
4. เพิ่ม API Endpoint ใน `api/`

### การทดสอบ
- ทดสอบ Unit Tests
- ทดสอบ Integration Tests
- ทดสอบ Security

## License

MIT License - ดูรายละเอียดใน LICENSE file

## Support

สำหรับการสนับสนุนและคำถาม:
- Email: <EMAIL>
- GitHub Issues: [Create Issue](https://github.com/your-repo/hrc-system/issues)

## Contributors

- Developer Name - Initial work

## Changelog

### Version 1.0.0
- ระบบจัดการพนักงานพื้นฐาน
- ระบบการลา
- LINE LIFF Integration
- ระบบแจ้งเตือน
- ระบบ Logging
- ระบบรักษาความปลอดภัย
