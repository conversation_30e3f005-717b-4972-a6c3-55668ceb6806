<?php
require_once __DIR__ . '/../../../config/config.php';

$auth = new Auth();

// Require authentication
if (!$auth->isAuthenticated()) {
    redirect(BASE_URL . '/login.php');
}

// Initialize controller and get data
$salaryController = new SalaryController();

// Get page data
$page = $_GET['page'] ?? 1;
$search = $_GET['search'] ?? null;
$department = $_GET['department'] ?? null;

$limit = RECORDS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get employees with their salary information
$employeeModel = new Employee();
$employees = $employeeModel->getAll($limit, $offset, $search, $department);
$totalRecords = $employeeModel->count($search, $department);
$pagination = paginate($totalRecords, $page, $limit);

// Get departments for filter
$departments = $employeeModel->getDepartments();

// Get salary statistics
$salaryModel = new Salary();
$stats = [
    'total_employees' => $employeeModel->count(),
    'employees_with_salary' => $salaryModel->getEmployeesWithSalaryCount(),
    'average_salary' => $salaryModel->getAverageSalary(),
    'total_payroll' => $salaryModel->getTotalPayroll()
];

$currentUser = $auth->getCurrentUser();
$pageTitle = 'จัดการเงินเดือน';
include __DIR__ . '/../../layouts/header.php';
?>

<div class="conatiner-fluid content-inner mt-n5 py-0">
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">พนักงานทั้งหมด</span>
                            <h4 class="mb-0"><?= number_format($stats['total_employees']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-primary-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M12.0865 22C11.9627 22 11.8388 21.9716 11.7271 21.9137L8.12599 20.0496C7.10415 19.5201 6.30481 18.9259 5.68063 18.2336C4.31449 16.7195 3.5544 14.776 3.54232 12.7599L3.50004 6.12426C3.495 5.35842 3.98931 4.67103 4.72826 4.41215L11.3405 2.10679C11.7331 1.96656 12.1711 1.9646 12.5707 2.09992L19.2081 4.32684C19.9511 4.57493 20.4535 5.25742 20.4575 6.02228L20.4998 12.6628C20.5129 14.676 19.779 16.6274 18.434 18.1581C17.8168 18.8602 17.0245 19.4632 16.0128 20.0025L12.4439 21.9088C12.3331 21.9686 12.2103 21.999 12.0865 22Z" fill="currentColor"></path>
                                <path d="M11.3194 14.3209C11.1261 14.3219 10.9328 14.2523 10.7838 14.1091L8.86695 12.2656C8.57097 11.9793 8.56795 11.5145 8.86091 11.2262C9.15387 10.9369 9.63207 10.934 9.92906 11.2193L11.3083 12.5451L14.6758 9.22479C14.9698 8.93552 15.448 8.93258 15.744 9.21793C16.041 9.50426 16.044 9.97004 15.751 10.2574L11.8519 14.1022C11.7049 14.2474 11.5127 14.3199 11.3194 14.3209Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">มีข้อมูลเงินเดือน</span>
                            <h4 class="mb-0"><?= number_format($stats['employees_with_salary']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-success-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.7688 8.71387H16.2312C18.5886 8.71387 20.5 10.5831 20.5 12.8885V17.8254C20.5 20.1308 18.5886 22 16.2312 22H7.7688C5.41136 22 3.5 20.1308 3.5 17.8254V12.8885C3.5 10.5831 5.41136 8.71387 7.7688 8.71387Z" fill="currentColor"></path>
                                <path opacity="0.4" d="M17.523 7.39595V8.86667C17.1673 8.7673 16.7913 8.71761 16.4052 8.71761H15.7447V7.39595C15.7447 5.37868 14.0681 3.73903 12.0053 3.73903C9.94257 3.73903 8.26594 5.36874 8.25578 7.37608V8.71761H7.60545C7.20916 8.71761 6.83319 8.7673 6.47754 8.87661V7.39595C6.4877 4.41476 8.95692 2 11.985 2C15.0537 2 17.523 4.41476 17.523 7.39595Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">เงินเดือนเฉลี่ย</span>
                            <h4 class="mb-0"><?= formatCurrency($stats['average_salary']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-info-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M13.3051 5.88243V6.06547C12.8144 6.05584 12.3237 6.05584 11.8331 6.05584V5.89206C11.8331 5.22733 11.2737 4.68784 10.6064 4.68784H9.63482C8.52589 4.68784 7.62305 3.80152 7.62305 2.72254C7.62305 2.32755 7.95671 2 8.35906 2C8.77123 2 9.09508 2.32755 9.09508 2.72254C9.09508 3.01155 9.34042 3.24276 9.63482 3.24276H10.6064C12.0882 3.2524 13.2953 4.43736 13.3051 5.88243Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M15.164 6.08279C15.4791 6.08712 15.7949 6.09145 16.1119 6.09469C19.5172 6.09469 22 8.52241 22 11.875V16.1813C22 19.5339 19.5172 21.9616 16.1119 21.9616C14.7478 21.9905 13.3837 22.0001 12.0098 22.0001C10.6359 22.0001 9.25221 21.9905 7.88813 21.9616C4.48283 21.9616 2 19.5339 2 16.1813V11.875C2 8.52241 4.48283 6.09469 7.89794 6.09469C9.18351 6.07542 10.4985 6.05615 11.8332 6.05615C12.3238 6.05615 12.8145 6.05615 13.3052 6.06579C13.9238 6.06579 14.5425 6.07427 15.164 6.08279Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">เงินเดือนรวม</span>
                            <h4 class="mb-0"><?= formatCurrency($stats['total_payroll']) ?></h4>
                        </div>
                        <div class="icon iq-icon-box-2 bg-warning-subtle rounded">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4" d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z" fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2Z" fill="currentColor"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">Salary Management</h4>
                    </div>
                    <div class="header-action">
                        <?php if (hasPermission('salary.export')): ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <svg width="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                    <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Export Report
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">Export as CSV</a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">Export as Excel</a></li>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search employees..." 
                                       value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select class="form-select" id="departmentFilter">
                                    <option value="">All Departments</option>
                                    <?php foreach ($departments as $dept): ?>
                                    <option value="<?= htmlspecialchars($dept) ?>" <?= ($_GET['department'] ?? '') === $dept ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($dept) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-primary w-100" onclick="applyFilters()">
                                <svg width="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
                                    <circle cx="11" cy="11" r="8" stroke="currentcolor" stroke-width="1.5"></circle>
                                    <path d="21 21l-4.35-4.35" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                Filter
                            </button>
                        </div>
                    </div>

                    <!-- Employees Table -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="salaryTable">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Basic Salary</th>
                                    <th>Allowances</th>
                                    <th>Total Salary</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($employees as $employee): ?>
                                <?php
                                $salaryModel = new Salary();
                                $salary = $salaryModel->getByEmployeeId($employee['id']);
                                $totalSalary = ($salary['basic_salary'] ?? 0) + ($salary['allowances'] ?? 0);
                                ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($employee['employee_code']) ?></small>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($employee['position'] ?? '-') ?></td>
                                    <td><?= htmlspecialchars($employee['department'] ?? '-') ?></td>
                                    <td>
                                        <?php if ($salary): ?>
                                        <?= formatCurrency($salary['basic_salary']) ?>
                                        <?php else: ?>
                                        <span class="text-muted">Not set</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($salary && $salary['allowances'] > 0): ?>
                                        <?= formatCurrency($salary['allowances']) ?>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($salary): ?>
                                        <strong><?= formatCurrency($totalSalary) ?></strong>
                                        <?php else: ?>
                                        <span class="text-muted">Not set</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($salary && $salary['is_active']): ?>
                                        <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="flex align-items-center list-user-action">
                                            <?php if (hasPermission('salary.edit')): ?>
                                            <a class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="Manage Salary" href="#" onclick="manageSalary(<?= $employee['id'] ?>, '<?= htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']) ?>')">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.7688 8.71387H16.2312C18.5886 8.71387 20.5 10.5831 20.5 12.8885V17.8254C20.5 20.1308 18.5886 22 16.2312 22H7.7688C5.41136 22 3.5 20.1308 3.5 17.8254V12.8885C3.5 10.5831 5.41136 8.71387 7.7688 8.71387Z" fill="currentColor"></path>
                                                        <path opacity="0.4" d="M17.523 7.39595V8.86667C17.1673 8.7673 16.7913 8.71761 16.4052 8.71761H15.7447V7.39595C15.7447 5.37868 14.0681 3.73903 12.0053 3.73903C9.94257 3.73903 8.26594 5.36874 8.25578 7.37608V8.71761H7.60545C7.20916 8.71761 6.83319 8.7673 6.47754 8.87661V7.39595C6.4877 4.41476 8.95692 2 11.985 2C15.0537 2 17.523 4.41476 17.523 7.39595Z" fill="currentColor"></path>
                                                    </svg>
                                                </span>
                                            </a>
                                            
                                            <a class="btn btn-sm btn-icon btn-info" data-bs-toggle="tooltip" 
                                               data-bs-placement="top" title="View History" href="#" onclick="viewSalaryHistory(<?= $employee['id'] ?>, '<?= htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']) ?>')">
                                                <span class="btn-inner">
                                                    <svg class="icon-20" width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
                                                        <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67V7z" fill="currentColor"/>
                                                    </svg>
                                                </span>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['previous_page'] ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&department=<?= urlencode($_GET['department'] ?? '') ?>">Previous</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                            <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&department=<?= urlencode($_GET['department'] ?? '') ?>"><?= $i ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $pagination['next_page'] ?>&search=<?= urlencode($_GET['search'] ?? '') ?>&department=<?= urlencode($_GET['department'] ?? '') ?>">Next</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Salary Modal -->
<div class="modal fade" id="salaryModal" tabindex="-1" aria-labelledby="salaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="salaryModalLabel">Manage Salary</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="salaryForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                    <input type="hidden" name="employee_id" id="employee_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="basic_salary" class="form-label">Basic Salary <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="basic_salary" name="basic_salary" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="allowances" class="form-label">Allowances</label>
                                <input type="number" class="form-control" id="allowances" name="allowances" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="overtime_rate" class="form-label">Overtime Rate (per hour)</label>
                                <input type="number" class="form-control" id="overtime_rate" name="overtime_rate" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="effective_date" class="form-label">Effective Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="effective_date" name="effective_date" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label class="form-label">Status</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Salary</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Salary History Modal -->
<div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="historyModalLabel">Salary History</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="historyTable">
                        <thead>
                            <tr>
                                <th>Effective Date</th>
                                <th>Basic Salary</th>
                                <th>Allowances</th>
                                <th>Overtime Rate</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Created By</th>
                                <th>Created At</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody">
                            <!-- History data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php
$additionalScripts = '
<script>
    // Apply filters
    function applyFilters() {
        const search = document.getElementById("searchInput").value;
        const department = document.getElementById("departmentFilter").value;
        
        const params = new URLSearchParams();
        if (search) params.append("search", search);
        if (department) params.append("department", department);
        
        window.location.href = "?" + params.toString();
    }

    // Manage salary
    function manageSalary(employeeId, employeeName) {
        document.getElementById("employee_id").value = employeeId;
        document.getElementById("salaryModalLabel").textContent = `Manage Salary - ${employeeName}`;
        
        // Load existing salary data
        showLoading("กำลังโหลดข้อมูลเงินเดือน...");
        
        fetch(`${BASE_URL}/api/salary.php?employee_id=${employeeId}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success && data.data) {
                const salary = data.data;
                document.getElementById("basic_salary").value = salary.basic_salary || "";
                document.getElementById("allowances").value = salary.allowances || "";
                document.getElementById("overtime_rate").value = salary.overtime_rate || "";
                document.getElementById("effective_date").value = salary.effective_date || "";
                document.getElementById("is_active").checked = salary.is_active == 1;
            } else {
                // Set default values for new salary
                document.getElementById("basic_salary").value = "";
                document.getElementById("allowances").value = "";
                document.getElementById("overtime_rate").value = "";
                document.getElementById("effective_date").value = new Date().toISOString().split("T")[0];
                document.getElementById("is_active").checked = true;
            }
            
            new bootstrap.Modal(document.getElementById("salaryModal")).show();
        })
        .catch(error => {
            hideLoading();
            showAlert("error", "ไม่สามารถโหลดข้อมูลเงินเดือนได้");
        });
    }

    // View salary history
    function viewSalaryHistory(employeeId, employeeName) {
        document.getElementById("historyModalLabel").textContent = `Salary History - ${employeeName}`;
        
        showLoading("กำลังโหลดประวัติเงินเดือน...");
        
        fetch(`${BASE_URL}/api/salary/history.php?employee_id=${employeeId}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                const tbody = document.getElementById("historyTableBody");
                tbody.innerHTML = "";
                
                if (data.data && data.data.length > 0) {
                    data.data.forEach(salary => {
                        const row = `
                            <tr>
                                <td>${formatDate(salary.effective_date)}</td>
                                <td>${formatCurrency(salary.basic_salary)}</td>
                                <td>${formatCurrency(salary.allowances || 0)}</td>
                                <td>${formatCurrency(salary.overtime_rate || 0)}</td>
                                <td><strong>${formatCurrency((parseFloat(salary.basic_salary) + parseFloat(salary.allowances || 0)))}</strong></td>
                                <td><span class="badge bg-${salary.is_active == 1 ? "success" : "secondary"}">${salary.is_active == 1 ? "Active" : "Inactive"}</span></td>
                                <td>${salary.created_by_username || "-"}</td>
                                <td>${formatDateTime(salary.created_at)}</td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                } else {
                    tbody.innerHTML = "<tr><td colspan=\"8\" class=\"text-center text-muted\">No salary history found</td></tr>";
                }
                
                new bootstrap.Modal(document.getElementById("historyModal")).show();
            } else {
                showAlert("error", data.message);
            }
        })
        .catch(error => {
            hideLoading();
            showAlert("error", "ไม่สามารถโหลดประวัติเงินเดือนได้");
        });
    }

    // Export report
    function exportReport(format) {
        const department = document.getElementById("departmentFilter").value;
        const params = new URLSearchParams();
        params.append("format", format);
        if (department) params.append("department", department);
        
        window.open(`${BASE_URL}/api/salary/export.php?${params.toString()}`, "_blank");
    }

    // Handle form submission
    document.getElementById("salaryForm").addEventListener("submit", function(e) {
        e.preventDefault();
        
        if (!validateForm("#salaryForm")) {
            showAlert("error", "กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วนและถูกต้อง");
            return;
        }
        
        showLoading("กำลังบันทึกข้อมูลเงินเดือน...");
        
        const formData = new FormData(this);
        formData.append("ajax", "1");
        
        fetch(`${BASE_URL}/api/salary.php`, {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert("success", data.message);
                bootstrap.Modal.getInstance(document.getElementById("salaryModal")).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert("error", data.message);
            }
        })
        .catch(error => {
            hideLoading();
            showAlert("error", "ไม่สามารถบันทึกข้อมูลเงินเดือนได้");
        });
    });

    // Reset form when modal is hidden
    document.getElementById("salaryModal").addEventListener("hidden.bs.modal", function() {
        document.getElementById("salaryForm").reset();
    });

    // Search on Enter key
    document.getElementById("searchInput").addEventListener("keypress", function(e) {
        if (e.key === "Enter") {
            applyFilters();
        }
    });

    // Helper functions
    function formatCurrency(amount) {
        return new Intl.NumberFormat("th-TH", {
            style: "currency",
            currency: "THB"
        }).format(amount);
    }

    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString("th-TH");
    }

    function formatDateTime(dateString) {
        return new Date(dateString).toLocaleString("th-TH");
    }
</script>
';

include __DIR__ . '/../../layouts/footer.php';
?>
