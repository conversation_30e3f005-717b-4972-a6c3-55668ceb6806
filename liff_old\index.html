<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR Center - Employee Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding-top: 20px;
            padding-bottom: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 20px;
        }
        
        .profile-img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .menu-card {
            text-align: center;
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .menu-card:hover {
            background-color: #f8f9fa;
        }
        
        .menu-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .menu-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .menu-desc {
            font-size: 0.9rem;
            color: #666;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 50px;
        }
        
        .spinner-border {
            color: #667eea;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .badge {
            font-size: 0.8rem;
            padding: 8px 12px;
            border-radius: 20px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Loading Screen -->
        <div id="loading" class="loading">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">กำลังโหลด...</p>
        </div>
        
        <!-- Error Alert -->
        <div id="errorAlert" class="alert alert-danger" style="display: none;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span id="errorMessage"></span>
        </div>
        
        <!-- Main Content -->
        <div id="mainContent" style="display: none;">
            <!-- Profile Card -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <img id="profileImage" src="https://via.placeholder.com/80" alt="Profile" class="profile-img">
                        </div>
                        <div class="col">
                            <h5 class="mb-1" id="employeeName">Loading...</h5>
                            <p class="mb-0 opacity-75" id="employeePosition">Loading...</p>
                            <small class="opacity-75" id="employeeCode">Loading...</small>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-outline-danger btn-sm" onclick="handleLogout()" title="ออกจากระบบ">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Menu Cards -->
            <div class="row">
                <div class="col-6 mb-3">
                    <div class="card menu-card" onclick="showProfile()">
                        <i class="fas fa-user menu-icon"></i>
                        <div class="menu-title">ข้อมูลส่วนตัว</div>
                        <div class="menu-desc">ดูข้อมูลพนักงาน</div>
                    </div>
                </div>
                
                <div class="col-6 mb-3">
                    <div class="card menu-card" onclick="showLeaveRequest()">
                        <i class="fas fa-calendar-alt menu-icon"></i>
                        <div class="menu-title">ขอลา</div>
                        <div class="menu-desc">ยื่นคำขอลา</div>
                    </div>
                </div>
                
                <div class="col-6 mb-3">
                    <div class="card menu-card" onclick="showLeaveHistory()">
                        <i class="fas fa-history menu-icon"></i>
                        <div class="menu-title">ประวัติการลา</div>
                        <div class="menu-desc">ดูประวัติการขอลา</div>
                    </div>
                </div>
                
                <div class="col-6 mb-3">
                    <div class="card menu-card" onclick="showPayslip()">
                        <i class="fas fa-file-invoice-dollar menu-icon"></i>
                        <div class="menu-title">สลิปเงินเดือน</div>
                        <div class="menu-desc">ดูสลิปเงินเดือน</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Profile Modal -->
        <div class="modal fade" id="profileModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">ข้อมูลส่วนตัว</h5>
                        <div>
                            <button class="btn btn-outline-danger btn-sm me-2" onclick="handleLogout()" title="ออกจากระบบ">
                                <i class="fas fa-sign-out-alt me-1"></i>ออกจากระบบ
                            </button>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                    </div>
                    <div class="modal-body" id="profileContent">
                        <!-- Profile content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Leave Request Modal -->
        <div class="modal fade" id="leaveModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">ขอลา</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="leaveForm">
                            <div class="mb-3">
                                <label class="form-label">ประเภทการลา</label>
                                <select class="form-select" id="leaveType" required>
                                    <option value="">เลือกประเภทการลา</option>
                                </select>
                            </div>
                            
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">วันที่เริ่มต้น</label>
                                        <input type="date" class="form-control" id="startDate" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">วันที่สิ้นสุด</label>
                                        <input type="date" class="form-control" id="endDate" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">เหตุผล</label>
                                <textarea class="form-control" id="reason" rows="3" placeholder="กรุณาระบุเหตุผลในการลา"></textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">ส่งคำขอ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Leave History Modal -->
        <div class="modal fade" id="leaveHistoryModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">ประวัติการลา</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="leaveHistoryContent">
                        <!-- Leave history content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Payslip Modal -->
        <div class="modal fade" id="payslipModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">สลิปเงินเดือน</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="payslipContent">
                        <!-- Payslip content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom JavaScript with cache busting -->
    <script src="js/app.js?v=20250111011"></script>

    <script>
        // Handle logout with fallback
        function handleLogout() {
            console.log('handleLogout called');

            try {
                // Try to call the logout function from app.js
                if (typeof window.logout === 'function') {
                    console.log('Calling window.logout function from app.js');
                    window.logout();
                } else if (typeof logout === 'function') {
                    console.log('Calling logout function from app.js');
                    logout();
                } else {
                    console.log('logout function not found, using fallback');
                    fallbackLogout();
                }
            } catch (error) {
                console.error('handleLogout error:', error);
                fallbackLogout();
            }
        }

        // Fallback logout function with SweetAlert2
        async function fallbackLogout() {
            console.log('Using fallback logout with SweetAlert2');

            try {
                // Check if SweetAlert2 is available
                if (typeof Swal === 'undefined') {
                    console.log('SweetAlert2 not available, using browser confirm');
                    if (confirm('ออกจากระบบ HR Center หรือไม่?')) {
                        performLogoutActions();
                    }
                    return;
                }

                // Show SweetAlert2 confirmation
                const result = await Swal.fire({
                    title: 'ออกจากระบบ?',
                    text: 'คุณต้องการออกจากระบบ HR Center หรือไม่?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: '<i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ',
                    cancelButtonText: '<i class="fas fa-times me-2"></i>ยกเลิก',
                    reverseButtons: true,
                    focusCancel: true
                });

                if (result.isConfirmed) {
                    console.log('User confirmed logout (SweetAlert2 fallback)');

                    // Show loading
                    Swal.fire({
                        title: 'กำลังออกจากระบบ...',
                        html: '<i class="fas fa-spinner fa-spin fa-2x text-primary"></i><br><br>กรุณารอสักครู่...',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Perform logout actions
                    await performLogoutActions();

                    // Show success message
                    await Swal.fire({
                        icon: 'success',
                        title: 'ออกจากระบบแล้ว',
                        text: 'คุณได้ออกจากระบบ HR Center เรียบร้อยแล้ว',
                        timer: 2000,
                        timerProgressBar: true,
                        showConfirmButton: false,
                        allowOutsideClick: false
                    });

                } else {
                    console.log('User cancelled logout (SweetAlert2 fallback)');
                }

            } catch (error) {
                console.error('Fallback logout error:', error);

                // Ultimate fallback
                if (confirm('เกิดข้อผิดพลาด - ออกจากระบบหรือไม่?')) {
                    performLogoutActions();
                }
            }
        }

        // Separate function for logout actions
        async function performLogoutActions() {
            try {
                // Clear data
                if (typeof employeeData !== 'undefined') {
                    employeeData = null;
                }
                if (typeof currentUser !== 'undefined') {
                    currentUser = null;
                }

                // Hide main content
                const mainContent = document.getElementById('mainContent');
                if (mainContent) {
                    mainContent.style.display = 'none';
                }

                // Close any open modals
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                });

                // LIFF logout
                if (typeof liff !== 'undefined' && liff.isLoggedIn()) {
                    liff.logout();
                }

                // Small delay for better UX
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Redirect
                if (typeof liff !== 'undefined' && liff.isInClient()) {
                    liff.closeWindow();
                } else {
                    window.location.reload();
                }

            } catch (error) {
                console.error('Logout actions error:', error);
                // Force reload as last resort
                window.location.reload();
            }
        }
    </script>
</body>
</html>
