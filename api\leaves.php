<?php
require_once '../config/config.php';

header('Content-Type: application/json');

$auth = new Auth();
if (!$auth->isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));
$leaveId = isset($pathParts[2]) ? (int)$pathParts[2] : null;

$leaveController = new LeaveController();

try {
    switch ($method) {
        case 'GET':
            if ($leaveId) {
                // Get single leave request
                requirePermission('leave.view');
                $leaveController->getData();
            } else {
                // Get all leave requests (for AJAX table)
                requirePermission('leave.view');
                $leaveModel = new LeaveRequest();
                
                $page = $_GET['page'] ?? 1;
                $employeeId = $_GET['employee_id'] ?? null;
                $status = $_GET['status'] ?? null;
                $dateFrom = $_GET['date_from'] ?? null;
                $dateTo = $_GET['date_to'] ?? null;
                
                $limit = RECORDS_PER_PAGE;
                $offset = ($page - 1) * $limit;
                
                $leaveRequests = $leaveModel->getAll($limit, $offset, $employeeId, $status, $dateFrom, $dateTo);
                $totalRecords = $leaveModel->count($employeeId, $status, $dateFrom, $dateTo);
                $pagination = paginate($totalRecords, $page, $limit);
                
                echo json_encode([
                    'success' => true,
                    'data' => $leaveRequests,
                    'pagination' => $pagination
                ]);
            }
            break;
            
        case 'POST':
            requirePermission('leave.create');
            $_POST['ajax'] = true;
            $leaveController->store();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
