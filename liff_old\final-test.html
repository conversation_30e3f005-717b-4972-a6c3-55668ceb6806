<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Test - LINE LIFF Registration</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .success-card {
            border-left: 4px solid #28a745;
        }
        
        .test-result {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #6c757d;
        }
        
        .test-result.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .test-result.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">🎉 Final Test</h1>
            <p class="text-white-50">ทดสอบระบบ LINE LIFF Registration ครั้งสุดท้าย</p>
        </div>
        
        <!-- Success Status -->
        <div class="card success-card">
            <div class="card-body">
                <h5 class="card-title text-success">✅ ปัญหาที่แก้ไขแล้ว</h5>
                <ul>
                    <li>✅ URL endpoints (.php extension)</li>
                    <li>✅ Method Not Allowed (405)</li>
                    <li>✅ Logger class dependency</li>
                    <li>✅ Employee data structure</li>
                </ul>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🧪 Final Tests</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>ข้อมูลทดสอบ:</h6>
                        <div class="mb-3">
                            <label class="form-label">รหัสพนักงาน:</label>
                            <input type="text" class="form-control" id="employeeCode" value="EMP001">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">เบอร์โทร:</label>
                            <input type="text" class="form-control" id="phone" value="0812345678">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>การทดสอบ:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="testRegistration()">
                                🚀 ทดสอบ Registration
                            </button>
                            <button class="btn btn-primary" onclick="testCompleteFlow()">
                                🔄 ทดสอบ Complete Flow
                            </button>
                            <button class="btn btn-info" onclick="openRegistrationPage()">
                                📱 เปิดหน้าลงทะเบียน
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 ผลการทดสอบ</h5>
                <div id="testResults">
                    <p class="text-muted">พร้อมทดสอบ...</p>
                </div>
            </div>
        </div>
        
        <!-- Next Steps -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🎯 ขั้นตอนต่อไป</h5>
                <div class="alert alert-success">
                    <h6>หลังจากทดสอบสำเร็จ:</h6>
                    <ol>
                        <li>Upload ไฟล์ที่แก้ไขไปยัง production server</li>
                        <li>รัน SQL script เพิ่มข้อมูลพนักงาน</li>
                        <li>ทดสอบใน LINE app จริง</li>
                        <li>ตั้งค่า Rich Menu ใน LINE Official Account</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '**********-Bzx2VrZZ';
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        let currentUser = null;
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultHtml = `
                <div class="test-result ${type}">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('พร้อมทดสอบ')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML += resultHtml;
            }
        }
        
        // Initialize LIFF
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await liff.init({ liffId: LIFF_ID });
                
                if (liff.isLoggedIn()) {
                    currentUser = await liff.getProfile();
                    addResult(`✅ LIFF initialized - User: ${currentUser.displayName}`, 'success');
                } else {
                    addResult('⚠️ User not logged in', 'error');
                }
            } catch (error) {
                addResult(`❌ LIFF initialization failed: ${error.message}`, 'error');
            }
        });
        
        async function testRegistration() {
            const employeeCode = document.getElementById('employeeCode').value;
            const phone = document.getElementById('phone').value;
            
            addResult(`🧪 Testing registration with ${employeeCode}, ${phone}`, 'info');
            
            try {
                if (!currentUser) {
                    addResult('❌ No LINE user profile', 'error');
                    return;
                }
                
                const accessToken = await liff.getAccessToken();
                
                const response = await fetch(`${API_BASE_URL}/employee/register.php`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        employee_code: employeeCode,
                        phone: phone
                    })
                });
                
                addResult(`📡 Response Status: ${response.status}`, response.ok ? 'success' : 'error');
                
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Registration successful: ${data.message}`, 'success');
                    addResult(`👤 Employee: ${data.data.first_name} ${data.data.last_name}`, 'success');
                } else {
                    addResult(`❌ Registration failed: ${data.message}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Test error: ${error.message}`, 'error');
            }
        }
        
        async function testCompleteFlow() {
            addResult('🔄 Testing complete flow...', 'info');
            
            // Test registration
            await testRegistration();
            
            // Wait a bit
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Test profile API
            try {
                const accessToken = await liff.getAccessToken();
                
                const response = await fetch(`${API_BASE_URL}/employee/profile.php`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addResult(`✅ Profile API works: ${data.data.first_name} ${data.data.last_name}`, 'success');
                } else {
                    addResult(`⚠️ Profile API: ${data.message}`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ Profile test error: ${error.message}`, 'error');
            }
            
            addResult('🎉 Complete flow test finished!', 'success');
        }
        
        function openRegistrationPage() {
            window.open('register.html', '_blank');
        }
    </script>
</body>
</html>
