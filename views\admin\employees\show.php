<?php
require_once __DIR__ . '/../../../config/config.php';

$auth = new Auth();

// Require authentication
if (!$auth->isAuthenticated()) {
    redirect(BASE_URL . '/login.php');
}

requirePermission('employees.view');

$id = $_GET['id'] ?? null;
if (!$id) {
    setFlashMessage('error', 'Employee ID is required');
    redirect(BASE_URL . '/admin/employees/index.php');
}

$employeeModel = new Employee();
$employee = $employeeModel->getById($id);

if (!$employee) {
    setFlashMessage('error', 'Employee not found');
    redirect(BASE_URL . '/admin/employees/index.php');
}

// Get additional data
$salaryModel = new Salary();
$leaveModel = new LeaveRequest();

$currentSalary = $salaryModel->getByEmployeeId($id);
$recentLeaves = $leaveModel->getAll(5, 0, $id);

$currentUser = $auth->getCurrentUser();
$pageTitle = 'Employee Details - ' . $employee['first_name'] . ' ' . $employee['last_name'];
include __DIR__ . '/../../layouts/header.php';
?>

<div class="conatiner-fluid content-inner mt-n5 py-0">
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">Employee Details</h4>
                    </div>
                    <div class="header-action">
                        <a href="index.php" class="btn btn-secondary">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path d="M4.25 12.2744L19.25 12.2744" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M10.2998 18.2988L4.2498 12.2748L10.2998 6.24976" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                            Back to List
                        </a>
                        <?php if (hasPermission('employees.edit')): ?>
                        <button type="button" class="btn btn-warning" data-action="edit" data-id="<?= $employee['id'] ?>">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2">
                                <path d="M11.4925 2.78906H7.75349C4.67849 2.78906 2.75049 4.96606 2.75049 8.04806V16.3621C2.75049 19.4441 4.66949 21.6211 7.75349 21.6211H16.5775C19.6625 21.6211 21.5815 19.4441 21.5815 16.3621V12.3341" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.82812 10.921L16.3011 3.44799C17.2321 2.51799 18.7411 2.51799 19.6721 3.44799L20.8891 4.66499C21.8201 5.59599 21.8201 7.10599 20.8891 8.03599L13.3801 15.545C12.9731 15.952 12.4211 16.181 11.8451 16.181H8.09912L8.19312 12.401C8.20712 11.845 8.43412 11.315 8.82812 10.921Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M15.1655 4.60254L19.7315 9.16854" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                            Edit Employee
                        </button>
                        <?php endif; ?>
                        <?php if (hasPermission('employees.delete')): ?>
                        <button type="button" class="btn btn-danger" data-action="delete" data-id="<?= $employee['id'] ?>" data-name="<?= htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']) ?>">
                            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="me-2">
                                <polyline points="3,6 5,6 21,6"></polyline>
                                <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                            Delete Employee
                        </button>
                        <?php endif; ?>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Profile Image -->
                        <div class="col-md-3 text-center">
                            <div class="profile-img-wrapper mb-3">
                                <?php if ($employee['profile_image']): ?>
                                <img src="<?= BASE_URL ?>/uploads/profiles/<?= htmlspecialchars($employee['profile_image']) ?>"
                                     alt="Profile Image" class="img-fluid rounded-circle" style="width: 200px; height: 200px; object-fit: cover;">
                                <?php else: ?>
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 200px; height: 200px; margin: 0 auto;">
                                    <span class="text-white" style="font-size: 4rem;">
                                        <?= strtoupper(substr($employee['first_name'], 0, 1) . substr($employee['last_name'], 0, 1)) ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <h4><?= htmlspecialchars($employee['title_name'] . ' ' . $employee['first_name'] . ' ' . $employee['last_name']) ?></h4>
                            <p class="text-muted"><?= htmlspecialchars($employee['position'] ?? 'No Position') ?></p>
                            <span class="badge bg-<?= $employee['employment_status'] === 'active' ? 'success' : ($employee['employment_status'] === 'inactive' ? 'warning' : 'danger') ?> fs-6">
                                <?= ucfirst($employee['employment_status']) ?>
                            </span>
                        </div>

                        <!-- Employee Details -->
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3">Personal Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Employee Code:</strong></td>
                                            <td><?= htmlspecialchars($employee['employee_code']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Full Name:</strong></td>
                                            <td><?= htmlspecialchars($employee['title_name'] . ' ' . $employee['first_name'] . ' ' . $employee['last_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Nickname:</strong></td>
                                            <td><?= htmlspecialchars($employee['nickname'] ?? '-') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ID Card:</strong></td>
                                            <td><?= htmlspecialchars($employee['id_card'] ?? '-') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Birth Date:</strong></td>
                                            <td><?= $employee['birth_date'] ? date('d/m/Y', strtotime($employee['birth_date'])) : '-' ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Gender:</strong></td>
                                            <td><?= ucfirst($employee['gender'] ?? '-') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Age:</strong></td>
                                            <td>
                                                <?php
                                                if ($employee['birth_date']) {
                                                    $birthDate = new DateTime($employee['birth_date']);
                                                    $today = new DateTime();
                                                    $age = $today->diff($birthDate)->y;
                                                    echo $age . ' years old';
                                                } else {
                                                    echo '-';
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>

                                <div class="col-md-6">
                                    <h5 class="mb-3">Contact Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td><?= htmlspecialchars($employee['phone'] ?? '-') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td><?= htmlspecialchars($employee['email'] ?? '-') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Address:</strong></td>
                                            <td><?= nl2br(htmlspecialchars($employee['address'] ?? '-')) ?></td>
                                        </tr>
                                    </table>

                                    <h5 class="mb-3 mt-4">Employment Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Position:</strong></td>
                                            <td><?= htmlspecialchars($employee['position'] ?? '-') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Department:</strong></td>
                                            <td><?= htmlspecialchars($employee['department'] ?? '-') ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Hire Date:</strong></td>
                                            <td><?= $employee['hire_date'] ? date('d/m/Y', strtotime($employee['hire_date'])) : '-' ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Years of Service:</strong></td>
                                            <td>
                                                <?php
                                                if ($employee['hire_date']) {
                                                    $hireDate = new DateTime($employee['hire_date']);
                                                    $today = new DateTime();
                                                    $service = $today->diff($hireDate);
                                                    echo $service->y . ' years, ' . $service->m . ' months';
                                                } else {
                                                    echo '-';
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge bg-<?= $employee['employment_status'] === 'active' ? 'success' : ($employee['employment_status'] === 'inactive' ? 'warning' : 'danger') ?>">
                                                    <?= ucfirst($employee['employment_status']) ?>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Additional Information -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <h5 class="mb-3">Salary Information</h5>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <?php if ($currentSalary): ?>
                                            <div class="row">
                                                <div class="col-6">
                                                    <small class="text-muted">Basic Salary</small>
                                                    <h6><?= formatCurrency($currentSalary['basic_salary']) ?></h6>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">Allowances</small>
                                                    <h6><?= formatCurrency($currentSalary['allowances'] ?? 0) ?></h6>
                                                </div>
                                                <div class="col-12 mt-2">
                                                    <small class="text-muted">Total Salary</small>
                                                    <h5 class="text-primary"><?= formatCurrency($currentSalary['basic_salary'] + ($currentSalary['allowances'] ?? 0)) ?></h5>
                                                </div>
                                            </div>
                                            <?php else: ?>
                                            <p class="text-muted mb-0">No salary information available</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5 class="mb-3">Recent Leave Requests</h5>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <?php if (!empty($recentLeaves)): ?>
                                            <div class="list-group list-group-flush">
                                                <?php foreach ($recentLeaves as $leave): ?>
                                                <div class="list-group-item bg-transparent border-0 px-0 py-2">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <small class="text-muted"><?= ucfirst($leave['leave_type']) ?></small>
                                                            <div><?= date('d/m/Y', strtotime($leave['start_date'])) ?> - <?= date('d/m/Y', strtotime($leave['end_date'])) ?></div>
                                                        </div>
                                                        <span class="badge bg-<?= $leave['status'] === 'approved' ? 'success' : ($leave['status'] === 'pending' ? 'warning' : 'danger') ?>">
                                                            <?= ucfirst($leave['status']) ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                            <?php else: ?>
                                            <p class="text-muted mb-0">No recent leave requests</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employee Modal (for editing) -->
<div class="modal fade" id="employeeModal" tabindex="-1" aria-labelledby="employeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="employeeModalLabel">Edit Employee</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="employeeForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                    <input type="hidden" name="employee_id" id="employee_id" value="<?= $employee['id'] ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="employee_code" class="form-label">Employee Code</label>
                                <input type="text" class="form-control" id="employee_code" name="employee_code" value="<?= htmlspecialchars($employee['employee_code']) ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="title_name" class="form-label">Title</label>
                                <select class="form-select" id="title_name" name="title_name">
                                    <option value="">Select Title</option>
                                    <option value="Mr." <?= $employee['title_name'] === 'Mr.' ? 'selected' : '' ?>>Mr.</option>
                                    <option value="Ms." <?= $employee['title_name'] === 'Ms.' ? 'selected' : '' ?>>Ms.</option>
                                    <option value="Mrs." <?= $employee['title_name'] === 'Mrs.' ? 'selected' : '' ?>>Mrs.</option>
                                    <option value="Dr." <?= $employee['title_name'] === 'Dr.' ? 'selected' : '' ?>>Dr.</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="<?= htmlspecialchars($employee['first_name']) ?>" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="<?= htmlspecialchars($employee['last_name']) ?>" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="nickname" class="form-label">Nickname</label>
                                <input type="text" class="form-control" id="nickname" name="nickname" value="<?= htmlspecialchars($employee['nickname'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="id_card" class="form-label">ID Card Number</label>
                                <input type="text" class="form-control" id="id_card" name="id_card" value="<?= htmlspecialchars($employee['id_card'] ?? '') ?>" maxlength="13">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="birth_date" class="form-label">Birth Date</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date" value="<?= $employee['birth_date'] ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <label for="gender" class="form-label">Gender</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">Select Gender</option>
                                    <option value="male" <?= $employee['gender'] === 'male' ? 'selected' : '' ?>>Male</option>
                                    <option value="female" <?= $employee['gender'] === 'female' ? 'selected' : '' ?>>Female</option>
                                    <option value="other" <?= $employee['gender'] === 'other' ? 'selected' : '' ?>>Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?= htmlspecialchars($employee['phone'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?= htmlspecialchars($employee['email'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($employee['address'] ?? '') ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" class="form-control" id="position" name="position" value="<?= htmlspecialchars($employee['position'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="department" class="form-label">Department</label>
                                <input type="text" class="form-control" id="department" name="department" value="<?= htmlspecialchars($employee['department'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="hire_date" class="form-label">Hire Date</label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date" value="<?= $employee['hire_date'] ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="employment_status" class="form-label">Employment Status</label>
                                <select class="form-select" id="employment_status" name="employment_status">
                                    <option value="active" <?= $employee['employment_status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= $employee['employment_status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    <option value="terminated" <?= $employee['employment_status'] === 'terminated' ? 'selected' : '' ?>>Terminated</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="profile_image" class="form-label">Profile Image</label>
                        <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                        <div class="mt-2">
                            <img id="imagePreview" src="#" alt="Preview" style="display: none; max-width: 200px; max-height: 200px; object-fit: cover;" class="rounded">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Employee</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additionalScripts = <<<'EOD'
<script>
    // BASE_URL and CSRF_TOKEN are already defined in footer.php
    // No need to redeclare them here

    let isEditMode = true;

    // Test if JavaScript is working
    console.log('Show page JavaScript loaded successfully');
    console.log('BASE_URL:', BASE_URL);
    console.log('CSRF_TOKEN:', CSRF_TOKEN);

    // Make functions global so they can be called from onclick
    window.editEmployee = editEmployee;
    window.deleteEmployee = deleteEmployee;

    // Edit employee
    function editEmployee(id) {
        new bootstrap.Modal(document.getElementById("employeeModal")).show();
    }

    // Delete employee
    function deleteEmployee(id, name) {
        showConfirm("Are you sure you want to delete employee \"" + name + "\"?", function() {
            showLoading("Deleting employee...");

            fetch(BASE_URL + "/api/employees.php?id=" + id, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": CSRF_TOKEN
                },
                body: JSON.stringify({ id: id, csrf_token: CSRF_TOKEN })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert("success", data.message);
                    setTimeout(() => window.location.href = "index.php", 1500);
                } else {
                    showAlert("error", data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert("error", "Failed to delete employee");
            });
        });
    }

    // Handle form submission
    document.getElementById("employeeForm").addEventListener("submit", function(e) {
        e.preventDefault();

        if (!validateForm("#employeeForm")) {
            showAlert("error", "Please fill in all required fields correctly");
            return;
        }

        showLoading("Updating employee...");

        const formData = new FormData(this);
        const employeeId = document.getElementById("employee_id").value;

        // Convert FormData to URLSearchParams for PUT request
        const params = new URLSearchParams();
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log('File field:', key, value.name);
                // Handle file uploads separately if needed
            } else {
                // Handle empty date fields
                if ((key === 'birth_date' || key === 'hire_date') && value === '') {
                    console.log('Empty date field:', key, 'skipping...');
                    continue; // Skip empty date fields
                }

                // Handle empty gender field
                if (key === 'gender' && value === '') {
                    console.log('Empty gender field, skipping...');
                    continue; // Skip empty gender field
                }

                // Handle empty employment_status field
                if (key === 'employment_status' && value === '') {
                    console.log('Empty employment_status field, skipping...');
                    continue; // Skip empty employment_status field
                }

                // Handle empty id_card field
                if (key === 'id_card' && value === '') {
                    console.log('Empty id_card field, skipping...');
                    continue; // Skip empty id_card field
                }

                // Handle empty employee_code field
                if (key === 'employee_code' && value === '') {
                    console.log('Empty employee_code field, skipping...');
                    continue; // Skip empty employee_code field
                }

                params.append(key, value);
            }
        }

        console.log('PUT request body:', params.toString());

        fetch(BASE_URL + "/api/employees.php?id=" + employeeId, {
            method: "PUT",
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': CSRF_TOKEN
            },
            body: params.toString(),
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert("success", data.message);
                bootstrap.Modal.getInstance(document.getElementById("employeeModal")).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert("error", data.message);
            }
        })
        .catch(error => {
            hideLoading();
            showAlert("error", "Failed to update employee");
        });
    });

    // Image preview
    document.getElementById("profile_image").addEventListener("change", function() {
        previewImage(this, "#imagePreview");
    });

    // Show existing image on load
    <?php if ($employee["profile_image"]): ?>
    document.getElementById("imagePreview").src = BASE_URL + "/uploads/profiles/<?= htmlspecialchars($employee["profile_image"]) ?>";
    document.getElementById("imagePreview").style.display = "block";
    <?php endif; ?>

    // Add event listeners for action buttons
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Show page DOM loaded, setting up event listeners');

        // Edit button
        const editBtn = document.querySelector('[data-action="edit"]');
        if (editBtn) {
            editBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                console.log('Edit clicked for ID:', id);
                editEmployee(id);
            });
        }

        // Delete button
        const deleteBtn = document.querySelector('[data-action="delete"]');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                console.log('Delete clicked for ID:', id, 'Name:', name);
                deleteEmployee(id, name);
            });
        }
    });
</script>
EOD;

include __DIR__ . '/../../layouts/footer.php';
?>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <?php if ($employee['profile_image']): ?>
                            <img src="<?= BASE_URL ?>/uploads/profiles/<?= $employee['profile_image'] ?>" 
                                 alt="Profile" class="img-fluid rounded-circle mb-3" style="width: 200px; height: 200px; object-fit: cover;">
                            <?php else: ?>
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                                 style="width: 200px; height: 200px;">
                                <span class="text-white" style="font-size: 4rem;"><?= strtoupper(substr($employee['first_name'], 0, 1)) ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <h4><?= htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']) ?></h4>
                            <p class="text-muted"><?= htmlspecialchars($employee['position']) ?></p>
                            <span class="badge bg-<?= $employee['employment_status'] === 'active' ? 'success' : ($employee['employment_status'] === 'inactive' ? 'warning' : 'danger') ?> fs-6">
                                <?= ucfirst($employee['employment_status']) ?>
                            </span>
                        </div>
                        
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Personal Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Employee Code:</strong></td>
                                            <td><?= htmlspecialchars($employee['employee_code']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Title:</strong></td>
                                            <td><?= htmlspecialchars($employee['title_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Nickname:</strong></td>
                                            <td><?= htmlspecialchars($employee['nickname']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ID Card:</strong></td>
                                            <td><?= $employee['id_card'] ? maskData($employee['id_card'], 4) : '-' ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Birth Date:</strong></td>
                                            <td><?= formatDate($employee['birth_date']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Age:</strong></td>
                                            <td><?= calculateAge($employee['birth_date']) ?> years</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Gender:</strong></td>
                                            <td><?= ucfirst($employee['gender']) ?></td>
                                        </tr>
                                    </table>
                                </div>
                                
                                <div class="col-md-6">
                                    <h5>Contact Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td><?= htmlspecialchars($employee['phone']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td><?= htmlspecialchars($employee['email']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>LINE ID:</strong></td>
                                            <td><?= htmlspecialchars($employee['line_id']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Address:</strong></td>
                                            <td><?= nl2br(htmlspecialchars($employee['address'])) ?></td>
                                        </tr>
                                    </table>
                                    
                                    <h5>Emergency Contact</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td><?= htmlspecialchars($employee['emergency_contact_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td><?= htmlspecialchars($employee['emergency_contact_phone']) ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <h5>Employment Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Position:</strong></td>
                                            <td><?= htmlspecialchars($employee['position']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Department:</strong></td>
                                            <td><?= htmlspecialchars($employee['department']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Hire Date:</strong></td>
                                            <td><?= formatDate($employee['hire_date']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Years of Service:</strong></td>
                                            <td><?= $employee['hire_date'] ? floor((time() - strtotime($employee['hire_date'])) / (365.25 * 24 * 60 * 60)) : 0 ?> years</td>
                                        </tr>
                                    </table>
                                </div>
                                
                                <div class="col-md-6">
                                    <h5>Current Salary</h5>
                                    <?php if ($currentSalary): ?>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Basic Salary:</strong></td>
                                            <td><?= formatCurrency($currentSalary['basic_salary']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Allowances:</strong></td>
                                            <td><?= formatCurrency($currentSalary['allowances']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Effective Date:</strong></td>
                                            <td><?= formatDate($currentSalary['effective_date']) ?></td>
                                        </tr>
                                    </table>
                                    <?php else: ?>
                                    <p class="text-muted">No salary information available</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Leave Requests -->
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Leave Requests</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentLeaves)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Leave Type</th>
                                    <th>Period</th>
                                    <th>Days</th>
                                    <th>Status</th>
                                    <th>Requested</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentLeaves as $leave): ?>
                                <tr>
                                    <td><?= htmlspecialchars($leave['leave_type_name']) ?></td>
                                    <td><?= formatDate($leave['start_date']) ?> - <?= formatDate($leave['end_date']) ?></td>
                                    <td><?= $leave['total_days'] ?></td>
                                    <td>
                                        <span class="badge bg-<?= $leave['status'] === 'approved' ? 'success' : ($leave['status'] === 'rejected' ? 'danger' : 'warning') ?>">
                                            <?= ucfirst($leave['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= formatDateTime($leave['created_at']) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <p class="text-muted">No leave requests found</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../layouts/footer.php'; ?>
