<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Registration - HR Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .debug-log {
            background: #000;
            color: #0f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .error-log {
            color: #f00;
        }
        
        .success-log {
            color: #0f0;
        }
        
        .info-log {
            color: #ff0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white">🔍 Debug Registration</h1>
            <p class="text-white-50">แก้ไขปัญหาการลงทะเบียน</p>
        </div>
        
        <!-- Current Status -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📊 สถานะปัจจุบัน</h5>
                <div class="alert alert-info">
                    <strong>ปัญหา:</strong> "เกิดข้อผิดพลาดในการลงทะเบียน กรุณาลองใหม่อีกครั้ง"<br>
                    <strong>สาเหตุ:</strong> API ทำงานได้แล้ว แต่มี error ในขั้นตอนการประมวลผล
                </div>
            </div>
        </div>
        
        <!-- Debug Tools -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">🛠️ เครื่องมือ Debug</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>ทดสอบ API:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="testAPIConnection()">
                                🔗 ทดสอบการเชื่อมต่อ API
                            </button>
                            <button class="btn btn-secondary" onclick="testBasicAPI()">
                                🧪 ทดสอบ Basic API
                            </button>
                            <button class="btn btn-info" onclick="testLIFFProfile()">
                                👤 ทดสอบ LINE Profile
                            </button>
                            <button class="btn btn-success" onclick="testRegistrationAPI()">
                                📝 ทดสอบ Registration API
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>ทดสอบข้อมูล:</h6>
                        <div class="mb-3">
                            <label class="form-label">รหัสพนักงาน:</label>
                            <input type="text" class="form-control" id="testEmployeeCode" value="EMP001">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">เบอร์โทร:</label>
                            <input type="text" class="form-control" id="testPhone" value="0812345678">
                        </div>
                        <button class="btn btn-warning w-100" onclick="testWithData()">
                            🧪 ทดสอบด้วยข้อมูลนี้
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Logs -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">📋 Debug Logs</h5>
                <div id="debugLogs" class="debug-log">
                    [Ready] Debug console initialized...
                </div>
                <div class="mt-2">
                    <button class="btn btn-secondary btn-sm" onclick="clearLogs()">
                        🗑️ Clear Logs
                    </button>
                    <button class="btn btn-info btn-sm" onclick="exportLogs()">
                        📤 Export Logs
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Common Issues -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">⚠️ ปัญหาที่พบบ่อย</h5>
                
                <div class="accordion" id="issuesAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue1">
                                Database Connection Error
                            </button>
                        </h2>
                        <div id="issue1" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                            <div class="accordion-body">
                                <strong>สาเหตุ:</strong> ไม่สามารถเชื่อมต่อฐานข้อมูลได้<br>
                                <strong>วิธีแก้:</strong> ตรวจสอบ config.php และ database credentials
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue2">
                                Missing Database Columns
                            </button>
                        </h2>
                        <div id="issue2" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                            <div class="accordion-body">
                                <strong>สาเหตุ:</strong> ไม่มี columns สำหรับ LINE integration<br>
                                <strong>วิธีแก้:</strong> รัน SQL script เพิ่ม line_id, line_display_name, etc.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue3">
                                Employee Not Found
                            </button>
                        </h2>
                        <div id="issue3" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                            <div class="accordion-body">
                                <strong>สาเหตุ:</strong> ไม่พบพนักงานที่ตรงกับรหัสและเบอร์โทร<br>
                                <strong>วิธีแก้:</strong> ตรวจสอบข้อมูลในฐานข้อมูล employees table
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <script>
        // Configuration
        const LIFF_ID = '2007905561-Bzx2VrZZ';
        const API_BASE_URL = 'https://smartapplytech.com/hrc/api';
        
        let currentUser = null;
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsDiv = document.getElementById('debugLogs');
            const colorClass = type === 'error' ? 'error-log' : type === 'success' ? 'success-log' : 'info-log';
            logsDiv.innerHTML += `<div class="${colorClass}">${logEntry}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('debugLogs').innerHTML = '[Ready] Debug console cleared...';
        }
        
        function exportLogs() {
            const logText = logs.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'registration-debug-logs.txt';
            a.click();
        }
        
        // Initialize LIFF
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                log('Initializing LIFF...', 'info');
                await liff.init({ liffId: LIFF_ID });
                
                if (liff.isLoggedIn()) {
                    currentUser = await liff.getProfile();
                    log(`LIFF initialized successfully. User: ${currentUser.displayName}`, 'success');
                } else {
                    log('User not logged in', 'error');
                }
            } catch (error) {
                log(`LIFF initialization failed: ${error.message}`, 'error');
            }
        });
        
        async function testAPIConnection() {
            log('Testing API connection...', 'info');

            try {
                const response = await fetch(`${API_BASE_URL}/employee/register.php`, {
                    method: 'OPTIONS'
                });

                log(`API Connection Status: ${response.status}`, response.ok ? 'success' : 'error');
                log(`Response Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`, 'info');

            } catch (error) {
                log(`API Connection Error: ${error.message}`, 'error');
            }
        }

        async function testBasicAPI() {
            log('Testing basic API endpoints...', 'info');

            // Test basic API
            try {
                log('Testing /api/test.php...', 'info');
                const response1 = await fetch(`${API_BASE_URL}/test.php`);
                const data1 = await response1.text();
                log(`Basic API Status: ${response1.status}`, response1.ok ? 'success' : 'error');
                log(`Basic API Response: ${data1.substring(0, 200)}`, 'info');
            } catch (error) {
                log(`Basic API Error: ${error.message}`, 'error');
            }

            // Test employee API
            try {
                log('Testing /api/employee/test.php...', 'info');
                const response2 = await fetch(`${API_BASE_URL}/employee/test.php`);
                const data2 = await response2.text();
                log(`Employee API Status: ${response2.status}`, response2.ok ? 'success' : 'error');
                log(`Employee API Response: ${data2.substring(0, 300)}`, 'info');
            } catch (error) {
                log(`Employee API Error: ${error.message}`, 'error');
            }

            // Test direct file access
            try {
                log('Testing direct register.php access...', 'info');
                const response3 = await fetch(`${API_BASE_URL}/employee/register.php`);
                const data3 = await response3.text();
                log(`Direct Access Status: ${response3.status}`, response3.ok ? 'success' : 'error');
                log(`Direct Access Response: ${data3.substring(0, 200)}`, 'info');
            } catch (error) {
                log(`Direct Access Error: ${error.message}`, 'error');
            }
        }
        
        async function testLIFFProfile() {
            log('Testing LIFF Profile...', 'info');
            
            try {
                if (!currentUser) {
                    log('No user profile available', 'error');
                    return;
                }
                
                log(`User ID: ${currentUser.userId}`, 'info');
                log(`Display Name: ${currentUser.displayName}`, 'info');
                log(`Picture URL: ${currentUser.pictureUrl}`, 'info');
                
                const accessToken = await liff.getAccessToken();
                log(`Access Token: ${accessToken ? 'Available' : 'Not available'}`, accessToken ? 'success' : 'error');
                
            } catch (error) {
                log(`LIFF Profile Error: ${error.message}`, 'error');
            }
        }
        
        async function testRegistrationAPI() {
            log('Testing Registration API with sample data...', 'info');
            await testWithData();
        }
        
        async function testWithData() {
            const employeeCode = document.getElementById('testEmployeeCode').value;
            const phone = document.getElementById('testPhone').value;
            
            log(`Testing registration with Employee Code: ${employeeCode}, Phone: ${phone}`, 'info');
            
            try {
                if (!currentUser) {
                    log('No LINE user profile available', 'error');
                    return;
                }
                
                const accessToken = await liff.getAccessToken();
                if (!accessToken) {
                    log('No access token available', 'error');
                    return;
                }
                
                log('Sending registration request...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/employee/register.php`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        employee_code: employeeCode,
                        phone: phone
                    })
                });
                
                log(`Response Status: ${response.status}`, 'info');
                log(`Response Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`, 'info');
                
                const responseText = await response.text();
                log(`Response Text: ${responseText}`, 'info');
                
                try {
                    const data = JSON.parse(responseText);
                    log(`Parsed JSON: ${JSON.stringify(data, null, 2)}`, data.success ? 'success' : 'error');
                    
                    if (!data.success) {
                        log(`Error Message: ${data.message}`, 'error');
                        if (data.debug) {
                            log(`Debug Info: ${JSON.stringify(data.debug, null, 2)}`, 'error');
                        }
                    }
                } catch (jsonError) {
                    log(`Failed to parse JSON: ${jsonError.message}`, 'error');
                    log(`Raw response: ${responseText.substring(0, 500)}`, 'error');
                }
                
            } catch (error) {
                log(`Registration Test Error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
