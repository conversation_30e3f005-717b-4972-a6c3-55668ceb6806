<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LIFF Setup Guide</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            padding: 20px;
        }
        .step {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
        }
        .alert-custom {
            border-left: 4px solid #007bff;
            background: #f8f9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">🔧 LIFF Setup Guide</h1>
        
        <div class="alert alert-warning">
            <strong>⚠️ ปัญหาปัจจุบัน:</strong> LIFF ID <code>**********-Bzx2VrZZ</code> ไม่สามารถใช้งานได้ (channel not found)
        </div>
        
        <!-- Step 1 -->
        <div class="step">
            <h3><span class="step-number">1</span>ตรวจสอบ LINE Developers Console</h3>
            <p>เข้าไปตรวจสอบการตั้งค่า LIFF ใน LINE Developers Console</p>
            
            <div class="alert alert-custom">
                <strong>📋 ขั้นตอน:</strong>
                <ol>
                    <li>เข้า <a href="https://developers.line.biz/console/" target="_blank">LINE Developers Console</a></li>
                    <li>เลือก Provider และ Channel ที่ถูกต้อง</li>
                    <li>ไปที่แท็บ "LIFF"</li>
                    <li>ตรวจสอบว่ามี LIFF app ที่มี ID: <code>**********-Bzx2VrZZ</code> หรือไม่</li>
                </ol>
            </div>
            
            <div class="alert alert-info">
                <strong>🔍 สิ่งที่ต้องตรวจสอบ:</strong>
                <ul>
                    <li>LIFF ID ตรงกันหรือไม่</li>
                    <li>Status เป็น "Published" หรือไม่</li>
                    <li>Endpoint URL ถูกต้องหรือไม่</li>
                    <li>Channel ยังใช้งานได้หรือไม่</li>
                </ul>
            </div>
        </div>
        
        <!-- Step 2 -->
        <div class="step">
            <h3><span class="step-number">2</span>สร้าง LIFF App ใหม่ (หากจำเป็น)</h3>
            <p>หาก LIFF app ไม่มีอยู่ หรือมีปัญหา ให้สร้างใหม่</p>
            
            <div class="alert alert-custom">
                <strong>⚙️ การตั้งค่า LIFF App:</strong>
                <div class="code-block">
LIFF app name: HR Center Employee Portal
Size: Full
Endpoint URL: https://smartapplytech.com/hrc/liff/
Scope: profile, openid
Bot link feature: On (แนะนำ)
                </div>
            </div>
            
            <div class="alert alert-warning">
                <strong>⚠️ สำคัญ:</strong> Endpoint URL ต้องเป็น HTTPS เท่านั้น! ไม่สามารถใช้ localhost ได้โดยตรง
            </div>
        </div>
        
        <!-- Step 3 -->
        <div class="step">
            <h3><span class="step-number">3</span>ตั้งค่า ngrok สำหรับ localhost testing</h3>
            <p>เนื่องจาก LIFF ต้องใช้ HTTPS ให้ใช้ ngrok สำหรับทดสอบ localhost</p>
            
            <div class="alert alert-custom">
                <strong>📦 ติดตั้ง ngrok:</strong>
                <ol>
                    <li>ดาวน์โหลด ngrok จาก <a href="https://ngrok.com/" target="_blank">ngrok.com</a></li>
                    <li>สมัครสมาชิก (ฟรี) และ copy authtoken</li>
                    <li>รัน command:</li>
                </ol>
                <div class="code-block">
# ติดตั้ง authtoken
ngrok authtoken YOUR_AUTHTOKEN

# เริ่ม tunnel สำหรับ port 80
ngrok http 80
                </div>
            </div>
            
            <div class="alert alert-info">
                <strong>📝 หลังจากรัน ngrok:</strong>
                <ul>
                    <li>จะได้ URL แบบ: <code>https://abc123.ngrok.io</code></li>
                    <li>อัปเดต Endpoint URL ใน LIFF settings เป็น: <code>https://abc123.ngrok.io/hrc/liff/</code></li>
                    <li>ทดสอบผ่าน ngrok URL</li>
                </ul>
            </div>
        </div>
        
        <!-- Step 4 -->
        <div class="step">
            <h3><span class="step-number">4</span>อัปเดต Configuration Files</h3>
            <p>อัปเดต LIFF ID ใหม่ในไฟล์ configuration</p>
            
            <div class="alert alert-custom">
                <strong>📁 ไฟล์ที่ต้องแก้ไข:</strong>
                
                <h6>1. config/config.php:</h6>
                <div class="code-block">
define('LINE_LIFF_ID', 'NEW_LIFF_ID_HERE');
                </div>
                
                <h6>2. liff/js/app.js:</h6>
                <div class="code-block">
const LIFF_ID = 'NEW_LIFF_ID_HERE';
                </div>
            </div>
        </div>
        
        <!-- Step 5 -->
        <div class="step">
            <h3><span class="step-number">5</span>ทดสอบ LIFF App</h3>
            <p>ทดสอบ LIFF app ที่สร้างใหม่</p>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="alert alert-success">
                        <strong>✅ ทดสอบใน Browser:</strong>
                        <ul>
                            <li>เปิด ngrok URL</li>
                            <li>ใช้ debug tools</li>
                            <li>ตรวจสอบ console logs</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-primary">
                        <strong>📱 ทดสอบใน LINE App:</strong>
                        <ul>
                            <li>เปิด LIFF URL ใน LINE</li>
                            <li>ทดสอบ login flow</li>
                            <li>ตรวจสอบ API calls</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Troubleshooting -->
        <div class="step">
            <h3><span class="step-number">🔧</span>Troubleshooting</h3>
            
            <div class="accordion" id="troubleshootingAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#error1">
                            Error: "channel not found"
                        </button>
                    </h2>
                    <div id="error1" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                        <div class="accordion-body">
                            <strong>สาเหตุ:</strong> LIFF ID ไม่มีอยู่ใน LINE system<br>
                            <strong>วิธีแก้:</strong> สร้าง LIFF app ใหม่และอัปเดต LIFF ID
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#error2">
                            Error: "LIFF SDK not loaded"
                        </button>
                    </h2>
                    <div id="error2" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                        <div class="accordion-body">
                            <strong>สาเหตุ:</strong> ไม่ได้เปิดใน LINE app หรือ LIFF browser<br>
                            <strong>วิธีแก้:</strong> เปิดผ่าน LINE app หรือใช้ LINE LIFF browser
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#error3">
                            CORS Errors
                        </button>
                    </h2>
                    <div id="error3" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                        <div class="accordion-body">
                            <strong>สาเหตุ:</strong> API server ไม่อนุญาต CORS<br>
                            <strong>วิธีแก้:</strong> ตรวจสอบ CORS headers ใน API และ .htaccess
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="step">
            <h3><span class="step-number">⚡</span>Quick Actions</h3>
            <div class="row">
                <div class="col-md-4">
                    <a href="https://developers.line.biz/console/" target="_blank" class="btn btn-primary w-100 mb-2">
                        🔗 LINE Developers Console
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="debug.html" class="btn btn-info w-100 mb-2">
                        🔍 LIFF Debug Tool
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="https://ngrok.com/" target="_blank" class="btn btn-success w-100 mb-2">
                        🌐 Download ngrok
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
